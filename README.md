# CMDB模型转换工具

CMDB模型转换工具是一个用于同步华为CMDB和行内CMDB系统之间数据的工具。该工具支持配置化的模型同步，可以处理多个模型的同步，支持字段映射、字段转换和关联模型处理。

## 项目结构

```
cmdb项目/
├── config/                  # 配置文件目录
│   ├── base_config.yaml     # 基础配置文件
│   ├── endpoints/           # API端点配置
│   │   ├── cloud_side.yaml  # 云内API端点配置
│   │   └── internal_side.yaml # 行内API端点配置
│   ├── models/              # 模型配置
│   │   └── pc_virtual_machine.yaml # PC虚拟机模型配置
│   └── transformers/        # 转换函数配置
│       └── common.yaml      # 通用转换函数配置
├── src/                     # 源代码目录
│   ├── config_loader.py     # 配置加载器
│   ├── main.py              # 主程序
│   └── sync_manager.py      # 同步管理器
├── tests/                   # 测试目录
│   └── test_sync_manager.py # 同步管理器测试
└── README.md                # 项目说明
```

## 同步流程

1. **全量获取数据**：
   - 系统启动时，首先全量获取云内侧和行内侧的所有模型数据
   - 云内侧数据包括主模型数据和关联模型数据
   - 行内侧数据包括所有配置的模型数据
   - 获取的数据会保存到缓存中，便于后续处理

2. **模型同步**：
   - 在全量获取数据完成后，系统会根据配置的模型进行同步
   - 同步过程会根据字段映射配置，将云内侧数据转换为行内侧数据格式
   - 支持字段转换，包括Python代码转换、映射转换和API调用转换
   - 支持处理关联模型数据，如一对一、一对多关系

3. **增量同步**：
   - 初始全量同步完成后，后续同步会使用增量方式
   - 云内侧使用last_Modified字段过滤最近更新的数据
   - 行内侧使用beginDate字段过滤最近更新的数据

## 使用方法

### 安装依赖

```bash
pip install -r requirements.txt
```

### 配置

1. 修改`config/base_config.yaml`文件，设置基础配置
2. 修改`config/endpoints`目录下的文件，设置API端点
3. 修改`config/models`目录下的文件，设置模型配置
4. 修改`config/transformers`目录下的文件，设置转换函数

### 运行

```bash
# 同步所有启用的模型
python src/main.py

# 同步指定模型
python src/main.py --model pc_virtual_machine

# 使用指定配置文件
python src/main.py --config ./config/custom_config.yaml
```

### 测试

```bash
# 运行所有测试
python -m unittest discover tests

# 运行特定测试
python -m unittest tests.test_sync_manager
```

## 配置说明

### 基础配置 (base_config.yaml)

基础配置包含全局设置、API基本配置、缓存配置、日志配置等。

### API端点配置 (endpoints/*.yaml)

API端点配置定义了各个API的URL路径。

### 模型配置 (models/*.yaml)

模型配置定义了模型的基本信息、云内侧配置、行内侧配置、字段映射配置等。

#### 关联模型配置

对于那些只在云内存在、作为关联模型使用但不需要同步到行内的模型，可以这样配置：

```yaml
# 卷模型配置（只作为关联模型，不需要同步到行内）
model_id: "volume"
model_name: "卷"
description: "卷模型配置，只作为关联模型使用"
sync_enabled: false  # 表示不需要同步到行内
enabled: true        # 表示需要加载该模型数据

# 云内侧配置
cloud_side:
  primary_model: "volume"
  primary_endpoint: "get_volumes"
  primary_key: "id"
  # 如果卷模型也有自己的关联模型，可以在这里定义
  related_models: []

# 不定义行内侧配置和字段映射配置
```

在需要使用这些关联模型的主模型配置中，通过`related_models`引用它们：

```yaml
cloud_side:
  # ...
  related_models:
    - model: "volume"  # 引用卷模型作为关联模型
      endpoint: "get_volumes"
      join_key: "vmId"
      foreign_key: "id"
      relation_type: "one_to_many"
```

### 转换函数配置 (transformers/*.yaml)

转换函数配置定义了用于字段转换的函数，包括Python代码转换、映射转换等。

# 检查模板系统

## 概述

检查模板系统允许您定义可重用的检查模板，并在多个模型配置中使用它们，减少配置重复。模板支持两种作用域：记录级别和字段级别。

## 作用域说明

- **记录级别 (record)**：对整个记录进行验证，可以检查多个字段之间的关系
- **字段级别 (field)**：对单个字段进行验证，只在该字段被修改时触发验证

## 使用方法

1. 在 `config/check_templates.yaml` 中定义模板
2. 在模型配置文件中使用 `check_templates` 部分引用模板

## 示例

### 模板定义

```yaml
templates:
  required_fields:
    scope: "record"  # 作用域：record 或 field
    template:
      condition: {}
      checks:
        - rule: "required"
          field: "{field}"
          message: "{message}"
```

### 模板使用

```yaml
check_templates:
  - template: required_fields
    params:
      field: "Model"
      message: "型号必填"
```

## 可用模板

1. **required_fields**: 必填字段检查 (记录级别)
   - 参数: `field`, `message`

2. **field_comparison**: 字段比较检查 (记录级别)
   - 参数: `field_a`, `field_b`, `operator`, `message`
   - 支持的运算符: `==`, `!=`, `>`, `<`, `>=`, `<=`, `in`, `not in`

3. **conditional_required**: 条件必填检查 (记录级别)
   - 参数: `condition_field`, `condition_value`, `required_field`, `message`

4. **field_format**: 字段格式验证 (字段级别)
   - 参数: `field`, `pattern`, `message`

## 自定义模板

您可以在 `config/check/check_templates.yaml` 中定义自己的模板。模板定义包括：

1. 模板名称
2. 作用域 (scope): `record` 或 `field` 默认不配置：`field`
3. 模板内容，使用 `{param}` 语法定义参数占位符

例如：

```yaml
templates:
  my_custom_template:
    scope: "field"  # 字段级别
    template:
      condition:
        field: "{field_1}"
        operator: "{operator}"
        value: "{value}"
      checks:
        - field: "{field_2}"
          rule: "{rule}"
          message: "{message}"
```

然后在模型配置中使用：

```yaml
check_templates:
  - template: my_custom_template
    params:
      field_1: "Status"
      operator: "eq"
      value: "Active"
      field_2: "Name"
      rule: "required"
      message: "当状态为激活时，名称必填"
```

## 注意事项

1. 确保配置文件格式正确，遵循YAML语法
2. 确保API认证信息正确，可以使用环境变量设置敏感信息
3. 定期检查日志，确保同步正常进行
4. 缓存目录需要有写入权限
