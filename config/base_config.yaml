# 基础配置文件
server:
  role: primary     # 如果部署为HA，主节点为primary,备节点为backup，单机用不到此参数
  primary_ip: *************  #HA部署下，备节点需要指定主节点的IP，主节点用不到此参数，单机用不到此参数
  port: 23601

# 全局配置
global:
  cache_dir: "./cache"
  models_dir: "./config/models"
  endpoints_dir: "./config/endpoints"
  transformers_dir: "./config/transformers"
  templates_dir: "./config/check/check_templates.yaml"
  license_dir: "./config/license"
  ssl_dir: "./config/ssl"
  licence_type: "XYCMDB"
  default_sync_interval: 3600  # 默认同步间隔（秒）
  max_retries: 3               # 最大重试次数
  retry_interval: 5            # 重试间隔（秒）

# API基本配置
apis:
  cloud_side:
    # 云内侧数据源配置，每个数据源有唯一标识符
    source_1:
      base_url: "https://*************:26335"
      auth:
        type: "token"
        token_url: "/rest/plat/smapp/v1/sessions"
        username: "thirdparty"
        password: "1&16D5C6A183219EB07E2ED50D4BE03B86&AFA1EFE248E93AF9DB15D44307C2C9F1"
        token_expiry: 1800  # Token过期时间（秒）
      headers:
        Content-Type: "application/json"
        Accept: "application/json"
      timeout: 30  # 请求超时时间（秒）
    source_2:
      base_url: "https://*************:26335"
      auth:
        type: "token"
        token_url: "/rest/plat/smapp/v1/sessions"
        username: "thirdparty"
        password: "1&16D5C6A183219EB07E2ED50D4BE03B86&AFA1EFE248E93AF9DB15D44307C2C9F1"
        token_expiry: 1800  # Token过期时间（秒）
      headers:
        Content-Type: "application/json"
        Accept: "application/json"
      timeout: 30  # 请求超时时间（秒）

  sc_cloud_side:
    source_1:
      base_url: "https://iam-apigateway-proxy.851type1.com" # 认证接口
      api_base_url: "https://160.101.15.50"
      api_base_port: 26335
      auth:
        type: "sc_token"
        token_url: "/v3/auth/tokens"
        username: "ghca_ybj"
        password: "Pbu4@123"
        domain_name: "bss_mo_ghca"
        token_expiry: 1800  # Token过期时间（秒）
      headers:
        Content-Type: "application/json"
        Accept: "application/json"
      timeout: 30  # 请求超时时间（秒）

  internal_side:
    base_url: "http://127.0.0.1:23601"
    auth:
      type: "Bearer"
      token_url: "/api/authenticate"
      username: "thirdparty"
      password: "1&16D5C6A183219EB07E2ED50D4BE03B86&AFA1EFE248E93AF9DB15D44307C2C9F1"
    headers:
      Content-Type: "application/json"
      Accept: "application/json"
    timeout:
      get: 30      # GET请求超时时间（秒）
      post: 1800   # POST请求超时时间（秒），用于大数据量上传
      put: 300     # PUT请求超时时间（秒）
      delete: 60   # DELETE请求超时时间（秒）

# 指定每个模型使用的数据源
model_sources:
#  example_model:
#    - source_1
#    - source_1
  another_model:
    - source_1
  access_optical_switch:
    - source_1

# 缓存配置
cache:
  type: "file"  # file或database
  file:
    format: "json"
    path: "./cache"
  database:
    type: "sqlite"
    path: "./cache/cmdb_cache.db"
  expiry: 86400  # 缓存过期时间（秒）
  cleanup_interval: 3600  # 缓存清理间隔（秒）

# 监控配置
monitoring:
  enabled: true
  metrics:
    - name: "sync_success_rate"
      type: "gauge"
      description: "同步成功率"
    - name: "sync_duration"
      type: "histogram"
      description: "同步耗时"
    - name: "api_call_count"
      type: "counter"
      description: "API调用次数"

# 告警配置
alerting:
  enabled: true
  channels:
    email:
      enabled: true
      smtp_server: "smtp.example.com"
      smtp_port: 587
      username: "${SMTP_USERNAME}"
      password: "${SMTP_PASSWORD}"
      from_address: "<EMAIL>"
      to_addresses: ["<EMAIL>"]

database:
  host_name: *************
  user_name: "root"
  password: "1&3878B30080465A1DAEF970E7FE3651FF&307F5843F4489EC91F111EA6CEC0507E"
  db_name: "xycmdb"
  port: 32513

# 同步记录配置
sync_records:
  enabled: true
  max_records: 1000  # 每个模型保留的最大记录数
  retention_days: 30  # 记录保留天数
  detail_level: "detailed"  # 详细程度: minimal, standard, detailed
  storage_type: "database"  # 存储类型: file, database

# 跳过模型
skipping:
  - "firewall_policy_log"
  - "sys_deviceLink"
# 默认同步数量限制配置
default_sync_limits:
  cloud_to_internal:           # 云内向行内同步
    update: 0                 # 更新操作限制
    create: 0                # 创建操作限制
    delete: 0                 # 删除操作限制
  internal_to_cloud:           # 行内向云内同步
    update: 0                 # 更新操作限制

# 模型特定的同步数量限制配置（覆盖默认值）
model_sync_limits:
  CLOUD_VM_NOVA:
    cloud_to_internal:
      update: 5
      create: 5
      delete: 5

#sync:
#  interval_hours: 1    # 改为2小时执行一次
#  start_hour: 0        # 从0点开始  范围：0-23

sync:
  interval_minutes: 30  # 30分钟执行一次
  start_time: "00:00"   # 20:00