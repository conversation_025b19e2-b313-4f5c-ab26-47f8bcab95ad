# 检查模板
templates:
  # 必填字段模板
  required_fields:
    template:
      condition: {}
      checks:
        - rule: "required"
          field: "{field}"
          message: "{message}"

  # 字段值匹配模板 - 检查字段值是否等于指定值
  field_value_match:
    template:
      condition: { }
      checks:
        - field: "{field}"
          rule: "custom"
          expression: "value == '{expected_value}'"
          message: "{message}"

  # 字段比较模板
  field_comparison:
    template:
      condition: {}
      checks:
        - field: "{field_a}"
          rule: "custom"
          expression: "value {operator} context.get('{field_b}')"
          message: "{message}"

  # 条件必填模板
  conditional_required:
    template:
      condition:
        field: "{condition_field}"
        operator: "eq"
        value: "{condition_value}"
      checks:
        - field: "{required_field}"
          rule: "required"
          message: "{message}"

  # 字段格式验证模板
  field_format:
    template:
      condition: {}
      checks:
        - field: "{field}"
          rule: "regex"
          pattern: "{pattern}"
          message: "{message}"

  # 多条件必填模板 - 当某字段满足多个可能值时，另一字段必填
  multi_condition_required:
    template:
      condition:
        operator: "or"
        conditions:
          - field: "{condition_field1}"
            operator: "eq"
            value: "{condition_value1}"
          - field: "{condition_field2}"
            operator: "eq"
            value: "{condition_value2}"
      checks:
        - field: "{required_field}"
          rule: "required"
          message: "{message}"

  # 3. 复合条件必填模板 - 当多个条件同时满足时，某字段必填
  compound_condition_required:
    template:
      condition:
        operator: "and"
        conditions:
          - field: "{condition_field1}"
            operator: "eq"
            value: "{condition_value1}"
          - field: "{condition_field2}"
            operator: "eq"
            value: "{condition_value2}"
      checks:
        - field: "{required_field}"
          rule: "required"
          message: "{message}"

  # 数值范围检查模板 - 检查数值是否在特定范围内
  numeric_range:
    template:
      condition: {}
      checks:
        - field: "{field}"
          rule: "custom"
          expression: "value is None or (is_number(value) and to_number(value) < max_value)"
          message: "{message}"

  # 日期最小值检查模板 - 检查日期是否大于或等于特定值
  date_min_value:
    template:
      condition: {}
      checks:
        - field: "{field}"
          rule: "custom"
          expression: "value is None or compare_dates(value, '{min_date}')"
          message: "{message}"

  # 字段不等检查模板 - 检查两个字段值是否不相等
  fields_not_equal:
    template:
      condition: {}
      checks:
        - field: "{field_a}"
          rule: "custom"
          expression: "value != context.get('{field_b}')"
          message: "{message}"

  # 字段一致性检查模板 - 检查字段值是否与其他相关字段保持一致
  field_consistency:
    template:
      condition: {}
      checks:
        - field: "{field}"
          rule: "custom"
          expression: "value == context.get('{related_field}')"
          message: "{message}"

  # 条件排除值检查模板 - 当某条件满足时，字段不能有特定值
  conditional_exclude_values:
    template:
      condition:
        field: "{condition_field}"
        operator: "eq"
        value: "{condition_value}"
      checks:
        - field: "{check_field}"
          rule: "custom"
          expression: "value not in {excluded_values}"
          message: "{message}"

  # 条件或关系检查模板 - 当某条件满足时，多个字段中至少有一个必填
  conditional_or_required:
    template:
      condition:
        field: "{condition_field}"
        operator: "eq"
        value: "{condition_value}"
      checks:
        - rule: "custom"
          expression: "context.get('{field1}') or context.get('{field2}')"
          message: "{message}"

  # 多条件或关系检查模板 - 当某字段满足多个可能值时，多个字段中至少有一个必填
  multi_condition_or_required:
    template:
      condition:
        operator: "or"
        conditions:
          - field: "{condition_field}"
            operator: "eq"
            value: "{condition_value1}"
          - field: "{condition_field}"
            operator: "eq"
            value: "{condition_value2}"
      checks:
        - rule: "custom"
          expression: "context.get('{field1}') or context.get('{field2}')"
          message: "{message}"

  # 条件数值范围检查模板 - 当某条件满足时，数值必须在特定范围内
  conditional_numeric_range:
    template:
      condition:
        field: "{condition_field}"
        operator: "eq"
        value: "{condition_value}"
      checks:
        - field: "{check_field}"
          rule: "custom"
          expression: "value is None or (is_number(value) and to_number(value) > {min_value} and to_number(value) < {max_value})"
          message: "{message}"

  # 多条件数值范围检查模板 - 当某字段满足多个可能值时，数值必须在特定范围内
  multi_condition_numeric_range:
    template:
      condition:
        operator: "or"
        conditions:
          - field: "{condition_field}"
            operator: "eq"
            value: "{condition_value1}"
          - field: "{condition_field}"
            operator: "eq"
            value: "{condition_value2}"
      checks:
        - field: "{check_field}"
          rule: "custom"
          expression: "value is None or (is_number(value) and to_number(value) > {min_value} and to_number(value) <= {max_value})"
          message: "{message}"

  # 字段范围匹配模板 - 检查字段值是否在指定范围内
  field_range_match:
    template:
      condition: {}
      checks:
        - field: "{field}"
          rule: "custom"
          expression: "value is None or (is_number(value) and to_number(value) >= {min_value} and to_number(value) <= {max_value})"
          message: "{message}"

  # 负责人一致性检查模板 - 检查A角和B角是否相同
  administrator_consistency:
    template:
      condition: {}
      checks:
        - field: "AdministratorA"
          rule: "custom"
          expression: "value == context.get('AdministratorB')"
          message: "数据负责人A角和B角不能相同"

  # 条件非零容量检查模板 - 当条件满足时，检查容量字段不为空且大于0.00
  conditional_non_zero_capacity:
    template:
      condition:
        field: "{condition_field}"
        operator: "eq"
        value: "{condition_value}"
      checks:
        - field: "{field}"
          rule: "custom"
          expression: "value is not None and (not isinstance(value, str) or value != '0.00') and (is_number(value) and to_number(value) > 0)"
          message: "{message}"