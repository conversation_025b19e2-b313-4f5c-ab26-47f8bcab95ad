# 行内API端点配置
endpoints:
  get_ci: "/cmdb/api/class/getCommonDynamicData"
  update_ci: "/cmdb/api/rest/updateItem"
  batch_delete_ci: "/cmdb/api/rest/batchDeleteItem"
  get_relations: "/cmdb/api/relation/getRelations"
  create_relation: "/cmdb/api/relation/createRelation"
  # 行内公共查询模型数据
  publicModel:
    maxBatchSize: 1000
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNum"
      size_field: "pageSize"
    className: "ApplicationSystem"
    itemNames: [ "Description","Code","SysNo","SysCode","RDTeam","MTUnit","AppSystemType","SysAdminA","SysAdminB","AppAdminA","AppAdminB", "NetworkAdminA","NetworkAdminB","IdTenant"]

        #如果是只需要负责人AB角 可以加以下条件 减少查询次数
    #      attribute: "{\"and\":[{\"attribute\":\"BizAdminA\",\"operator\":[{\"operatorType\":\"isnotnull\",\"value\":\"\"}],\"type\":\"varchar\"},{\"attribute\":\"BizAdminB\",\"operator\":[{\"operatorType\":\"isnotnull\",\"value\":\"\"}],\"type\":\"varchar\"}]}"
