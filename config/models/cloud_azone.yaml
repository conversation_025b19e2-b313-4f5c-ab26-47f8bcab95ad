model_id: "CLOUD_AZONE"
model_name: "可用区"
description: "可用区模型同步配置"
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: true        # 是否启用该模型数据加载
sync_enabled: true   # 是否启用该模型同步
sync_model_order: 2

# 云内侧配置
cloud_side:
  primary_model: "CLOUD_AZONE"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"

# 行内侧配置
internal_side:
  model: "CloudAZONE"
  primary_key: "InstanceId"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  get_params:
    className: "CloudAZONE"
    itemNames: ["Code", "Description", "ResourcePoolName", "Remark", "Manager", "ReuseStorage", "EnableCsha", "IdTenant",
                "BackendStorType", "VirtualizeType", "CpuArch", "InstanceId", "CIType", "LogicalRegionId", "LogicalRegionName"]
    attribute: [ ]
    isRelatedCIId: true
  update_params:
    className: "CloudAZONE"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "CloudAZONE"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "InstanceId"  # 主键字段值
  delete_params:
    className: "CloudAZONE"
    api_config:
      request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
      record_config:
        root_key: "record"  # 记录列表的根键
        id_field: "recordId"  # 记录ID字段
        table_field: "tableName"  # 表名字段
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称

# 字段映射配置 云内
field_mappings:
  # 基本信息映射
  - internal_field: "Description"
    cloud_field: "name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Code"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "ResourcePoolName"
    cloud_field: "resourcePoolName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Remark"
    cloud_field: "remark"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Manager"
    cloud_field: "manager"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "ReuseStorage"
    cloud_field: "reuseStorage"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "EnableCsha"
    cloud_field: "enableCsha"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "BackendStorType"
    cloud_field: "backendStorType"
    #transform: "backend_stor_type_mapping"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "VirtualizeType"
    cloud_field: "virtualizeType"
#    transform: "virtualize_type_mapping"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CpuArch"
    cloud_field: "cpuArch"
    sync_direction: "cloud_to_internal"
#    transform: "cpu_arch_mapping"
    required: true

  # 行内新增属性
  - internal_field: "InstanceId"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionId"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionName"
    internal_reference_key: "Code"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "IdTenant"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "IdTenant"
      default_value: "L00500" # 新云应用系统SysNo编码 默认
    internal_reference_key: "Id"
    required: true

# 模型特有的转换函数
transformers:
  backend_stor_type_mapping:  # 行内没给对应的映射关系，当前按照云内映射关系映射
    type: "mapping"
    mapping:
      "fc_san": "FC SAN"
      "fusionstorage_separated": "FusionStorage分离部署"
      "xsky": "XSKY分布式块存储"
      "fusioncube_integrated": "FusionCube融合部署"
      "fusionstorage_integrated": "FusionStorage融合部署"
      "fusionstorage": "FusionStorage"
      "local_disk": "本地磁盘"
      "ip_san": "IP SAN"

  virtualize_type_mapping:  # 行内没给对应的映射关系，当前按照云内映射关系映射
    type: "mapping"
    mapping:
      "vrm": "VRM"
      "xen": "Xen"
      "kvm": "KVM"
      "hybrid": "Hybrid"
      "vmware": "VMware"
      "bms": "裸金属"
      "non-virtualized": "无虚拟化"
      "powervm": "powervm"
      "mcs": "系统容器"
      "unknown": "未知"
      "docker": "Docker"

  #x86=x86, arm=ARM, unknown=未知
  cpu_arch_mapping:
    type: "mapping"
    mapping:
      "x86": "x86"
      "arm": "ARM"
      "unknown": "未知"