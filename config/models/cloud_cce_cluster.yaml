model_id: "CLOUD_CCE_CLUSTER"
model_name: "集群"
description: "集群模型同步配置"
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: true        # 是否启用该模型数据加载
sync_enabled: true   # 是否启用该模型同步
sync_model_order: 3

# 云内侧配置
cloud_side:
  primary_model: "CLOUD_CCE_CLUSTER"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"

# 行内侧配置
internal_side:
  model: "CloudCCECluster"
  primary_key: "InstanceId"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  get_params:
    className: "CloudCCECluster"
    itemNames: [ "Code", "CIType", "Description", "NetworkId" ,"LogicalRegionId", "LogicalRegionName", "TenantName", "VdcName", "ClusterVersion",
                 "ClusterFlavor", "ClusterStatus", "CreateTime", "ClusterType", "VpcId", "SubnetId", "SecurityGroupsId", "SafeMode", "IdTenant",
                 "DockerVersion", "KubeProxyMode", "ContainerNetworkCidr", "KubernetesSvcIpRange", "AppSysNo", "ApplicationSystem", "InstanceId" ]
    attribute: [ ]
    isRelatedCIId: true
  update_params:
    className: "CloudCCECluster"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "CloudCCECluster"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "InstanceId"  # 主键字段值
  delete_params:
    className: "CloudCCECluster"
    api_config:
      request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
      record_config:
        root_key: "record"  # 记录列表的根键
        id_field: "recordId"  # 记录ID字段
        table_field: "tableName"  # 表名字段
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称

# 字段映射配置 云内
field_mappings:
  # 行内->云内
  - internal_field: "Code"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Description"
    cloud_field: "name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TenantName"
    cloud_field: "tenantName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "VdcName"
    cloud_field: "vdcName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "ClusterVersion"
    cloud_field: "clusterVersion"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "ClusterFlavor"
    cloud_field: "clusterFlavor"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "ClusterType"
    cloud_field: "clusterType"
    sync_direction: "cloud_to_internal"
    #    transform: "cce_cluster_type_mapping"
    required: true

  - internal_field: "CreateTime"
    cloud_field: "createTime"
    sync_direction: "cloud_to_internal"
    transform: "format_long_datetime"
    transform_params:
      field: "createTime"
    required: true

  - internal_field: "ClusterStatus"
    cloud_field: "status"
    sync_direction: "cloud_to_internal"
    #    transform: "cce_cluster_status_mapping"
    required: true

  - internal_field: "VpcId"
    internal_reference_key: "Code"
    cloud_field: "ref_vpcId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "NetworkId"
    internal_reference_key: "InstanceId"
    cloud_field: "ref_subnetId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "SecurityGroupsId"
    cloud_field: "ref_securityGroupsId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "SafeMode"
    cloud_field: "safeMode"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "DockerVersion"
    cloud_field: "dockerVersion"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "KubeProxyMode"
    cloud_field: "kubeProxyMode"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "ContainerNetworkCidr"
    cloud_field: "containerNetworkCidr"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "KubernetesSvcIpRange"
    cloud_field: "kubernetesSvcIpRange"
    sync_direction: "cloud_to_internal"
    required: true

    # 行内新增属性
  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionId"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionName"
    internal_reference_key: "Code"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "ApplicationSystem"
    cloud_field: "epsId"
    internal_reference_key: "SysNo"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "SysNo"
      default_value: "L00500"
    required: true

  - internal_field: "AppSysNo"
    cloud_field: "epsId"
    transform: "get_from_application_code"
    sync_direction: "cloud_to_internal"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "SysNo"
      default_value: "L00500"
    required: true

  - internal_field: "IdTenant"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "IdTenant"
      default_value: "L00500" # 新云应用系统SysNo编码 默认
    internal_reference_key: "Id"
    required: true

  # 云内->行内
  - internal_field: "InstanceId"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

# 模型特有的转换函数
transformers:
  cce_cluster_status_mapping:
    type: "mapping"
    mapping:
      "Available": "可用"
      "Unavailable": "不可用"
      "Creating": "创建"
      "ScalingUp": "扩容"
      "ScalingDown": "缩容中"
      "Deleting": "删除中"
      "Upgrading": "升级中"
      "Resizing": "调整中"
      "Empty": "空"
      "Error": "错误"
      "Freezing": "冻结中"
      "Frozen": "冻结"
      "UnFreezing": "解冻中"
      "Hibernation": "休眠"
      "Awaking": "唤醒中"
      "Hibernating": "休眠中"

  cce_cluster_type_mapping:
    type: "mapping"
    mapping:
      "VirtualMachine": "VirtualMachine"
      "ARM64": "ARM64"
