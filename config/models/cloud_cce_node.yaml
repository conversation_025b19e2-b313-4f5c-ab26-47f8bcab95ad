model_id: "CLOUD_CCE_NODE"
model_name: "集群节点"
description: "集群节点模型同步配置"
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: true        # 是否启用该模型数据加载
sync_enabled: true   # 是否启用该模型同步
sync_model_order: 4

# 云内侧配置
cloud_side:
  primary_model: "CLOUD_CCE_NODE"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"

# 行内侧配置
internal_side:
  model: "CloudCCENode"
  primary_key: "InstanceId"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  get_params:
    className: "CloudCCENode"
    itemNames: ["Code", "CIType", "Description", "LogicalRegionId", "LogicalRegionName", "TenantName", "VdcName", "IdTenant",
                 "PrivateIP", "ExternalIP", "ClusterId", "NodeType", "CreateTime", "NodeFlavor", "NodeOs", "Cpu", "Memory",
                 "VmId", "ServerId", "Status", "AppSysNo", "ApplicationSystem", "InstanceId", "ContainerdVersion", "Labels", "Taints", "KubeletVersion"]
    attribute: [ ]
    isRelatedCIId: true
  update_params:
    className: "CloudCCENode"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "CloudCCENode"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "InstanceId"  # 主键字段值
  delete_params:
    className: "CloudCCENode"
    api_config:
      request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
      record_config:
        root_key: "record"  # 记录列表的根键
        id_field: "recordId"  # 记录ID字段
        table_field: "tableName"  # 表名字段
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称

# 字段映射配置 云内
field_mappings:
  # 基本信息映射
  - internal_field: "Code"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Description"
    cloud_field: "name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TenantName"
    cloud_field: "tenantName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "VdcName"
    cloud_field: "vdcName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "PrivateIP"
    cloud_field: "privateIP"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "ExternalIP"
    cloud_field: "externalIP"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "ClusterId"
    internal_reference_key: "Code"
    cloud_field: "ref_clusterId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CreateTime"
    cloud_field: "createTime"
    sync_direction: "cloud_to_internal"
    transform: "format_long_datetime"
    transform_params:
      field: "createTime"
    required: true

  - internal_field: "NodeStatus"
    cloud_field: "status"
    sync_direction: "cloud_to_internal"
#    transform: "node_status_mapping"
    required: true

  - internal_field: "NodeType"
    cloud_field: "nodeType"
    sync_direction: "cloud_to_internal"
#    transform: "node_type_mapping"
    required: true

  - internal_field: "NodeFlavor"
    cloud_field: "nodeFlavor"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "NodeOs"
    cloud_field: "nodeOs"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Cpu"
    cloud_field: "cpu"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Memory"
    cloud_field: "memory"
    transform: "convert_capacity"
    transform_params:
      field: "memory"
      from_unit: "MB"      # 云内单位
      to_unit: "GB"       # 行内单位
      format: "{value:.2f}"  # 保留两位小数
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "VmId"
    internal_reference_key: "InstanceId"
    cloud_field: "ref_vmId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "ServerId"  # 上线时候 依赖模型未上线  临时注释 不进行同步
    internal_reference_key: "InstanceId"
    cloud_field: "ref_serverId"
    sync_direction: "cloud_to_internal"
    required: false

  - internal_field: "ContainerdVersion"
    cloud_field: "containerdVersion"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "labels"
    cloud_field: "labels"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Taints"
    cloud_field: "taints"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "KubeletVersion"
    cloud_field: "kubeletVersion"
    sync_direction: "cloud_to_internal"
    required: true

    # 行内新增属性
  - internal_field: "InstanceId"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionId"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionName"
    internal_reference_key: "Code"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "ApplicationSystem"
    cloud_field: "epsId"
    internal_reference_key: "SysNo"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "SysNo"
      default_value: "L00500"
    required: true

  - internal_field: "AppSysNo"
    cloud_field: "epsId"
    transform: "get_from_application_code"
    sync_direction: "cloud_to_internal"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "SysNo"
      default_value: "L00500"
    required: true

  - internal_field: "IdTenant"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "IdTenant"
      default_value: "L00500" # 新云应用系统SysNo编码 默认
    internal_reference_key: "Id"
    required: true

# 模型特有的转换函数
transformers:
  node_type_mapping:
    type: "mapping"
    mapping:
      "master": "Master节点"
      "node": "Node节点"

  node_status_mapping:
    type: "mapping"
    mapping:
      "Active": "正常"
      "Build": "创建中"
      "Deleting": "删除中"
      "Abnormal": "异常"
      "Error": "故障"
      "Upgrading": "升级中"
      "ShutDown": "关机"
      "Installed": "纳管完成"
      "Installing": "纳管中"
