model_id: "CloudDeployment"
model_name: "CloudDeployment"
description: "CloudDeployment模型同步配置"
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: true        # 是否启用该模型数据加载
sync_enabled: true   # 是否启用该模型同步
sync_model_order: 4

# 云内侧配置
cloud_side:
  primary_model: "CLOUD_CCE_WORKLOAD"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"

# 行内侧配置
internal_side:
  model: "CloudDeployment"
  primary_key: "InstanceId"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  get_params:
    className: "CloudDeployment"
    itemNames: ["Code", "CIType", "Description", "LogicalRegionId", "LogicalRegionName", "TenantName", "VdcName","IdTenant",
                "ClusterId", "Type", "ClusterNameSpace", "CreateTime", "AppSysNo", "ApplicationSystem", "InstanceId", "Labels"]
    attribute: []
    isRelatedCIId: true
  update_params:
    className: "CloudDeployment"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "CloudDeployment"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "InstanceId"  # 主键字段值
  delete_params:
    className: "CloudDeployment"
    api_config:
      request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
      record_config:
        root_key: "record"  # 记录列表的根键
        id_field: "recordId"  # 记录ID字段
        table_field: "tableName"  # 表名字段
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称

# 字段映射配置
field_mappings:
  # 行内->云内
  - internal_field: "Code"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Description"
    cloud_field: "name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TenantName"
    cloud_field: "tenantName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "VdcName"
    cloud_field: "vdcName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "ClusterId"
    internal_reference_key: "Code"
    cloud_field: "ref_clusterId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CreateTime"
    cloud_field: "createTime"
    sync_direction: "cloud_to_internal"
    transform: "format_long_datetime"
    transform_params:
      field: "createTime"
    required: true

  - internal_field: "Type"
    cloud_field: "type"
    sync_direction: "cloud_to_internal"
#    transform: "workload_type_mapping"
    is_check: true
    required: true

  - internal_field: "ClusterNameSpace"
    cloud_field: "namespace"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "DesiredReplication"
    cloud_field: "desiredReplication"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Labels"
    cloud_field: "labels"
    sync_direction: "cloud_to_internal"
    required: false

  # 云内->行内
  - internal_field: "InstanceId"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CIType"
    cloud_field: "custom"
    transform: "Deployment_CiType_constant"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionId"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionName"
    internal_reference_key: "Code"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "ApplicationSystem"
    cloud_field: "epsId"
    internal_reference_key: "SysNo"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "SysNo"
      default_value: "L00500"
    required: true

  - internal_field: "AppSysNo"
    cloud_field: "epsId"
    transform: "get_from_application_code"
    sync_direction: "cloud_to_internal"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "SysNo"
      default_value: "L00500"
    required: true

  - internal_field: "IdTenant"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "IdTenant"
      default_value: "L00500" # 新云应用系统SysNo编码 默认
    internal_reference_key: "Id"
    required: true


check_templates:
  # 使用字段值匹配模板
  - template: field_value_match
    scope: "record"  # 或 "field"，默认：field
    params:
      field: "type"
      expected_value: "deployment"
      message: "cce_workload_deployment模型的Type字段必须为deployment"

transformers:
  # 联调临时写死
  Deployment_CiType_constant:
    type: "constant"
    value: "CloudDeployment"