model_id: "CLOUD_CLUSTER"
model_name: "集群"
description: "集群模型同步配置"
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: true        # 是否启用该模型数据加载
sync_enabled: true   # 是否启用该模型同步
sync_model_order: 3

# 云内侧配置
cloud_side:
  primary_model: "CLOUD_CLUSTER"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"

# 行内侧配置
internal_side:
  model: "CloudCluster"
  primary_key: "InstanceId"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  get_params:
    className: "CloudCluster"
    itemNames: ["Code", "Name", "ResourcePoolName", "AzoneName", "TotalMemoryMB", "TotalCpuMhz", "PodId", "Remark", "UseSort",
                "TotalVcpuCores", "TotalVmemoryMB", "TotalCPUSizeMHz", "TotalMemSizeMB", "HypervisorType", "KeystoneId",
                "HostIds", "IsOpenHa", "TotalDiskSizeMB", "AppSysNo", "ApplicationSystem", "InstanceId", "CIType", "LogicalRegionId",
                "LogicalRegionName", "AzoneId", "IdTenant"]
    attribute: []
    isRelatedCIId: true
  update_params:
    className: "CloudCluster"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "CloudCluster"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "InstanceId"  # 主键字段值
  delete_params:
    className: "CloudCluster"
    api_config:
      request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
      record_config:
        root_key: "record"  # 记录列表的根键
        id_field: "recordId"  # 记录ID字段
        table_field: "tableName"  # 表名字段
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称

# 字段映射配置 云内
field_mappings:
  # 基本信息映射
  - internal_field: "Name"
    cloud_field: "name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Code"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "ResourcePoolName"
    cloud_field: "resourcePoolName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "AzoneName"
    internal_reference_key: "Code"
    cloud_field: "azoneId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TotalMemoryMB"
    cloud_field: "totalMemoryMB"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TotalCpuMhz"
    cloud_field: "totalCpuMhz"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "PodId"
    cloud_field: "podId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Remark"
    cloud_field: "remark"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "UseSort"
    cloud_field: "useSort"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TotalVcpuCores"
    cloud_field: "totalVcpuCores"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TotalVmemoryMB"
    cloud_field: "totalVmemoryMB"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TotalCPUSizeMHz"
    cloud_field: "totalCPUSizeMHz"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TotalMemSizeMB"
    cloud_field: "totalMemSizeMB"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "KeystoneId"
    cloud_field: "keystoneId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "HypervisorType"
    cloud_field: "hypervisorType"
    sync_direction: "cloud_to_internal"
#    transform: "hypervisor_type_mapping"
    required: true

  - internal_field: "HostIds"
    cloud_field: "hostIds"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "IsOpenHa"
    cloud_field: "isOpenHa"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TotalDiskSizeMB"
    cloud_field: "totalDiskSizeMB"
    sync_direction: "cloud_to_internal"
    required: true

  # 行内新增属性
  - internal_field: "InstanceId"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionId"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionName"
    internal_reference_key: "Code"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "AppSysNo"
    cloud_field: "epsId"
    transform: "get_from_application_code"
    sync_direction: "cloud_to_internal"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "SysNo"
      default_value: "L00500"
    required: true

  - internal_field: "ApplicationSystem"
    cloud_field: "epsId"
    internal_reference_key: "SysNo"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "SysNo"
      default_value: "L00500"
    required: true

  - internal_field: "AzoneId"
    cloud_field: "azoneId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "IdTenant"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "IdTenant"
      default_value: "L00500" # 新云应用系统SysNo编码 默认
    internal_reference_key: "Id"
    required: true

# 模型特有的转换函数
transformers:
  hypervisor_type_mapping: # 行内没给对应的映射关系，当前按照云内映射关系映射
    type: "mapping"
    mapping:
      "vrm": "VRM"
      "xen": "Xen"
      "kvm": "KVM"
      "hybrid": "Hybrid"
      "vmware": "VMware"
      "non-virtualized": "无虚拟化"
      "powervm": "powervm"
      "unknown": "未知"
      "docker": "Docker"