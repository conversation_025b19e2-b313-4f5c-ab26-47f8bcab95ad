model_id: "CLOUD_HOST"
model_name: "PC服务器宿主机"
description: "PC服务器宿主机模型同步配置"
resource_type: "physical"  # 资源类型：physical（物理类资源）或 cloud（云资源）
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: true        # 是否启用该模型数据加载
sync_enabled: true   # 是否启用该模型同步

# 云内侧配置
cloud_side:
  primary_model: "CLOUD_HOST"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  match_key: "serialNumber"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"
  # 关联模型配置
  related_models:
    - model: "CLOUD_HOST_NIC"
      endpoint: "cloud_cmdb_model"
      join_key: "id"
      foreign_key: "hostId"
      relation_type: "one_to_many"
      relation_method: "direct"
    - model: "SYS_PhysicalServer"
      endpoint: "cloud_cmdb_model"
      join_key: "serverId"
      foreign_key: "id"
      relation_type: "one_to_many"
      relation_method: "direct"
    - model: "M_PhyServConsistsOfPsu"
      relation_type: "one_to_many"
      relation_method: "relation_table"
      relation_config:
        endpoint: "cloud_relation_api"
        params: &filter_by_source_id
          condition:
            {
              "constraint": [
                {
                  "simple": {
                    "name": "source_Instance_Id",
                    "value": "{serverId}",
                    "operator": "equal"
                  }
                }
              ]
            }
        source_key: "source_Instance_Id"
        target_key: "target_Instance_Id"
        source_field: "serverId"  # 源实体中用于关联的字段，默认为主键
      target_config:
        endpoint: "cloud_cmdb_model"
        model: "SYS_Psu"
        params: &filter_by_ids
          condition:
            {
              "constraint": [
                {
                  "simple": {
                    "name": "id",
                    "value": "{target_ids}",
                    "operator": "in"
                  }
                }
              ]
            }
        id_key: "id"
#    - model: "M_PhyServConsistsOfNic"
#      relation_type: "one_to_many"
#      relation_method: "relation_table"
#      relation_config:
#        endpoint: "cloud_relation_api"
#        params: *filter_by_source_id
#        source_key: "source_Instance_Id"
#        target_key: "target_Instance_Id"
#        source_field: "serverId"
#      target_config:
#        endpoint: "cloud_cmdb_model"
#        model: "SYS_ServerNIC"
#        params: *filter_by_ids
#        id_key: "id"
    - model: "M_PhyServConsistsOfCPU"
      relation_type: "one_to_many"
      relation_method: "relation_table"
      relation_config:
        endpoint: "cloud_relation_api"
        params: *filter_by_source_id
        source_key: "source_Instance_Id"
        target_key: "target_Instance_Id"
        source_field: "serverId"
      target_config:
        endpoint: "cloud_cmdb_model"
        model: "SYS_CPU"
        params: *filter_by_ids
        id_key: "id"
    - model: "M_PhyServConsistsOfRAM"
      relation_type: "one_to_many"
      relation_method: "relation_table"
      relation_config:
        endpoint: "cloud_relation_api"
        params: *filter_by_source_id
        source_key: "source_Instance_Id"
        target_key: "target_Instance_Id"
        source_field: "serverId"
      target_config:
        endpoint: "cloud_cmdb_model"
        model: "SYS_RAM"
        params: *filter_by_ids
        id_key: "id"
    - model: "M_PhyServConsistsOfDisk"
      relation_type: "one_to_many"
      relation_method: "relation_table"
      relation_config:
        endpoint: "cloud_relation_api"
        params: *filter_by_source_id
        source_key: "source_Instance_Id"
        target_key: "target_Instance_Id"
        source_field: "serverId"
      target_config:
        endpoint: "cloud_cmdb_model"
        model: "SYS_Disk"
        params: *filter_by_ids
        id_key: "id"
    - model: "M_PhyServConsistsOfGPU"
      relation_type: "one_to_many"
      relation_method: "relation_table"
      relation_config:
        endpoint: "cloud_relation_api"
        params: *filter_by_source_id
        source_key: "source_Instance_Id"
        target_key: "target_Instance_Id"
        source_field: "serverId"
      target_config:
        endpoint: "cloud_cmdb_model"
        model: "SYS_GPU"
        params: *filter_by_ids
        id_key: "id"
    - model: "M_RackContainsDevice"
      relation_type: "one_to_many"
      relation_method: "relation_table"
      relation_config:
        endpoint: "cloud_relation_api"
        params:
          condition:
            {
              "constraint": [
                {
                  "simple": {
                    "name": "target_Instance_Id",
                    "value": "{id}",
                    "operator": "in"
                  }
                }
              ]
            }
        source_key: "target_Instance_Id" # 这里的源应该是 设备的ID  在关系表里 设备ID作为目标ID
        target_key: "source_Instance_Id" # 目标应该是 机柜的ID 在关系表里 设备ID作为ID
        source_field: "id"  # 源实体中用于关联的字段，默认为主键
      # 这里的目标实体为机柜信息
      target_config:
        endpoint: "cloud_cmdb_model"
        model: SYS_Rack
        params: *filter_by_ids
        id_key: "id"

# 行内侧配置
internal_side:
  model: "PCHostedServer"
  primary_key: "SerialNumber"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  get_params:
    className: "PCHostedServer"
    itemNames: [ "SerialNumber", "Code", "Description", "Notes", "Model", "DeviceStatus", "Rack", "RackBeginU", "RackEndU", "Power", "NetCardMacAddr",
                 "ManageIPAddr", "AssetCode", "MemoryBrand", "CpuProductArch", "CPUBrand", "TotalNumberOfCpu", "TotalNumOfCpuNuclear", "CpuFrequency",
                 "MemoryCapacity", "LocalStorageBrand", "TotalLocalStorageCap", "AdministratorA", "AdministratorB", "TotalMemory", "MaintenanceEndDate", "TotalCPUs",
                 "MaintenanceStartDate", "GPUModel", "GPUCardNum", "VSPlatformInfo", "CPUModel", "InstanceId", "CIType", "LogicalRegionId", "LogicalRegionName",
                 "PrdIPAddr", "Function" ,"SysDevBrand", "SupportIpv6" ,"PurchaseContract" ,"ArrivalDate", "AdminTeam"]
    attribute: [ ]
    isRelatedCIId: true
  update_params:
    className: "PCHostedServer"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "PCHostedServer"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "SerialNumber"  # 主键字段值
  delete_params:
    className: "PCHostedServer"
    api_config:
      request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
      record_config:
        root_key: "record"  # 记录列表的根键
        id_field: "recordId"  # 记录ID字段
        table_field: "tableName"  # 表名字段
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称

# 字段映射配置 云内
field_mappings:
  # 基本信息映射
  - internal_field: "Code"  # 匹配符
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "concat_fields"
    transform_params:
      fields: [ "serialNumber" ]
      constant: "PCHostedServer"
      separator: "_"  # 使用下划线为分隔符
      skip_empty: true  # 跳过空值
    required: true

  - internal_field: "Notes"
    cloud_field: "remark"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Description"
    cloud_field: "name"
    sync_direction: "cloud_to_internal"
    is_check: true
    required: true

  - internal_field: "Model"
    cloud_field: "productName"
    sync_direction: "cloud_to_internal"
    is_check: true
    required: true

  - internal_field: "SerialNumber"
    cloud_field: "serialNumber"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "DeviceStatus"
    cloud_field: "status"
    transform: "x86service_status"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Power"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_related_attribute"
    transform_params:
      model: "M_PhyServConsistsOfPsu"
      attribute: "ratedPower"
      number: false
      format: "{value:.2f}"
    required: true

  - internal_field: "NetCardMacAddr"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_related_attribute"
    transform_params:
      model: "CLOUD_HOST_NIC"
      attribute: "mac"
      format: "{value}"
    required: true

  - internal_field: "VSPlatformInfo"
    internal_reference_key: "Description"
    cloud_field: "custom"
    transform: "vs_platform_constant"
    sync_direction: "cloud_to_internal"
    required: true

  # 内存相关字段映射
  - internal_field: "MemoryBrand"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_related_attribute"
    transform_params:
      model: "M_PhyServConsistsOfRAM"
      attribute: "manufacturer"
      format: "{value}"
    required: true

  - internal_field: "MemoryCapacity"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "calculate_related_sum"
    transform_params:
      model: "M_PhyServConsistsOfRAM"
      attribute: "capacity"
      convert_unit: true
      format: "{value:.2f}"
    required: true

  - internal_field: "LocalStorageBrand"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_related_attribute"
    transform_params:
      model: "M_PhyServConsistsOfDisk"
      attribute: "manufacturer"
      format: "{value}"
    required: true

  - internal_field: "TotalLocalStorageCap"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "calculate_related_sum"
    transform_params:
      model: "M_PhyServConsistsOfDisk"
      attribute: "capacity"
      convert_unit: true
      format: "{value:.2f}"
    required: true

  - internal_field: "TotalMemory"
    cloud_field: "totalVmemoryMB"
    sync_direction: "cloud_to_internal"
    transform: "convert_capacity"
    transform_params:
      field: "totalVmemoryMB"  # 从这个字段获取值
      from_unit: "MB"      # 云内单位
      to_unit: "GB"       # 行内单位
      format: "{value:.2f}"  # 保留两位小数
    required: true

  # cpu相关
  - internal_field: "CpuProductArch"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_related_attribute"
    transform_params:
      model: "M_PhyServConsistsOfCPU"
      attribute: "cpuArch"
      format: "{value}"
    required: true

  - internal_field: "CPUBrand"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_related_attribute"
    transform_params:
      model: "M_PhyServConsistsOfCPU"
      attribute: "manufacturer"
      format: "{value}"
    is_check: true
    required: true

  - internal_field: "TotalNumberOfCpu"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "calculate_related_count"
    transform_params:
      model: "M_PhyServConsistsOfCPU"
      format: "{value}"
    required: true

  - internal_field: "TotalNumOfCpuNuclear"
    cloud_field: "totalCpu"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CpuFrequency"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_related_attribute"
    transform_params:
      model: "M_PhyServConsistsOfCPU"
      attribute: "frequency"
      number: false
      format: "{value}"
    required: true

  - internal_field: "TotalCPUs"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "calculate_related_count"
    transform_params:
      model: "M_PhyServConsistsOfCPU"
      format: "{value}"
    required: true

  - internal_field: "CPUModel"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_related_attribute"
    transform_params:
      model: "M_PhyServConsistsOfCPU"
      attribute: "productName"
      format: "{value}"
    required: true


  # GPU相关字段映射
  - internal_field: "GPUModel"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_related_attribute"
    transform_params:
      model: "M_PhyServConsistsOfGPU"
      attribute: "productName"
      format: "{value}"
    is_check: true
    required: true

  - internal_field: "GPUCardNum"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "calculate_related_count"
    transform_params:
      model: "M_PhyServConsistsOfGPU"
      format: "{value}"
    required: true

  # 行内新增属性
  - internal_field: "InstanceId"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionId"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionName"
    internal_reference_key: "Code"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  # 字段映射配置 行内
#  - internal_field: "Rack_RelatedId"   # Rack是REFERENCE字段,isRelatedCIId=true，结果响应：Rack_RelatedId
#    cloud_field: "rackId"
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "RackBeginU"
#    cloud_field: "position"
#    sync_direction: "internal_to_cloud"
#    transform: "compare_relation_field"
#    transform_params:
#      relation_model: "M_RackContainsDevice"
#      relation_key: "target_Instance_Id"
#      internal_field: "RackBeginU"
#      cloud_field: "position"
#    required: false
#
#  - internal_field: "RackEndU"
#    cloud_field: "occupyHeight"
#    sync_direction: "internal_to_cloud"
#    transform: "compare_relation_field"
#    transform_params:
#      relation_model: "M_RackContainsDevice"
#      relation_key: "target_Instance_Id"
#      internal_field: "RackEndU"
#      cloud_field: "occupyHeight"
#    required: false
#
#  - internal_field: "ManageIPAddr"
#    cloud_field: "mgmtIp"  # 需云内新增字段
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "AssetCode"
#    cloud_field: "assertNo"
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "MaintenanceEndDate" # 行内云内数据类型都是date,格式是否需要转换待定
#    cloud_field: "warrantyEndTime"
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "MaintenanceStartDate"
#    cloud_field: "warrantyStartTime"
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "AdministratorA"
#    cloud_field: "incharger"
#    sync_direction: "internal_to_cloud"
#    required: true

# 模型特有的转换函数
transformers:
  # 模型特有的转换函数
  vs_platform_constant:
    type: "constant"
    value: "FusionSphere"

check_templates:
  # 条件复用组
  - template: condition_group
    id: "normal_status"
    params:
      condition_field: "status"
      condition_value: "normal"  # 云内设备状态没有测试中状态

  - template: conditional_required
    scope: "record"
    params:
      condition_ref: "normal_status"
      required_field: "name"
      message: "设备状态是运行中或者测试中，配置项名称必填"

  - template: conditional_required
    scope: "record"
    params:
      condition_ref: "normal_status"
      required_field: "productName"
      message: "设备状态是运行中或测试中，型号必填"

  - template: conditional_required
    scope: "record"
    params:
      condition_ref: "normal_status"
      required_field: "CPUBrand"
      message: "设备状态是运行中，CPU品牌必填"

  - template: compound_condition_required
    scope: "record"
    params:
      condition_ref: "normal_status"
      #      condition_field2: "hostType"
      #      condition_value2: "GPU服务器" # 云内没有该状态
      required_field: "GPUModel"
      message: "设备状态是运行中且服务器功能是GPU服务器时，GPU卡型号必填"


backfill_field:

  - field_name: "PrdIPAddr"

  - field_name: "CPUBrand"
    is_mapping: true

  - field_name: "MemoryBrand"
    is_mapping: true

  - field_name: "LocalStorageBrand"
    is_mapping: true

  - field_name: "Function"

  - field_name: "RackBeginU"

  - field_name: "SysDevBrand"

  - field_name: "CpuFrequency"
    is_mapping: true

  - field_name: "AdminTeam"
    is_reference: true
    class_key: "Description"

  - field_name: "Rack"
    is_reference: true
    class_key: "Description"

  - field_name: "AdministratorA"

  - field_name: "AdministratorB"

  - field_name: "Description"

  - field_name: "ManageIPAddr"

  - field_name: "CpuProductArch"
    is_mapping: true

  - field_name: "ArrivalDate"

  - field_name: "PurchaseContract"
    is_reference: true
    class_key: "Description"

  - field_name: "RackEndU"

  - field_name: "SupportIpv6"

  - field_name: "Model"
    is_mapping: true