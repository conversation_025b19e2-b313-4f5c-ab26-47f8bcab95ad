model_id: "CLOUD_OBS_BUCKET"
model_name: "对象存储桶"
description: "对象存储桶模型同步配置"
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: true        # 是否启用该模型数据加载
sync_enabled: true   # 是否启用该模型同步

# 云内侧配置
cloud_side:
  primary_model: "CLOUD_OBS_BUCKET"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"

# 行内侧配置
internal_side:
  model: "CloudObsBucket"  # 未提供行内模型名称，暂定和云内名称一致
  primary_key: "InstanceId"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  get_params:
    className: "CloudObsBucket"
    itemNames: [ "Code", "CIType", "Description", "LogicalRegionId", "LogicalRegionName", "TenantName", "VdcName", "AzoneName",
                 "AzoneId", "CloudObsBucketStatus", "Notes", "VolumeId", "CreatedAt", "AppSysNo", "ApplicationSystem", "InstanceId", "Size" ]
    attribute: [ ]
    isRelatedCIId: true
  update_params:
    className: "CloudObsBucket"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "CloudObsBucket"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "InstanceId"  # 主键字段值
  delete_params:
    className: "CloudObsBucket"
    api_config:
      request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
      record_config:
        root_key: "record"  # 记录列表的根键
        id_field: "recordId"  # 记录ID字段
        table_field: "tableName"  # 表名字段
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称

# 字段映射配置 云内
field_mappings:
  # 基本信息映射
  - internal_field: "Code"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TenantName"
    cloud_field: "tenantName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Description"
    cloud_field: "name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "VdcName"
    cloud_field: "vdcName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "AzoneName"
    internal_reference_key: "Code"
    cloud_field: "azoneId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "AzoneId"
    cloud_field: "azoneId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CloudObsBucketStatus"
    cloud_field: "status"
    sync_direction: "cloud_to_internal"
    #    transform: "bucket_status_mapping"
    required: true

  - internal_field: "Notes"
    cloud_field: "description"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "VolumeId"
    cloud_field: "volumeId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CreatedAt"
    cloud_field: "createdAt"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Size"
    cloud_field: "size"
    sync_direction: "cloud_to_internal"
    required: true

  # 行内新增属性
  - internal_field: "instance_id"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionId"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionName"
    internal_reference_key: "Code"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "AppSysNo"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "SysNo"
    required: true

  - internal_field: "ApplicationSystem"
    #    internal_reference_key: "Description"
    #    cloud_field: "applicationSystem"
    #    transform: "ApplicationSystem_constant"
    #    sync_direction: "cloud_to_internal"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "Description"
      replace_field: "SysNo"
      constant: "-"
    required: true

# 模型特有的转换函数
transformers:
  bucket_status_mapping: # creating=创建中, available=可用, error_deleting=删除失败, error=故障
    # 行内设备状态枚举值未知，当前枚举是云内的枚举
    type: "mapping"
    mapping:
      "creating": "创建中"
      "available": "可用"
      "error_deleting": "删除失败"
      "error": "故障"
