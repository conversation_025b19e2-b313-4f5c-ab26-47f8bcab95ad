model_id: "CLOUD_SFS_SHARE"
model_name: "弹性文件服务实例"
description: "弹性文件服务实例模型同步配置"
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: true        # 是否启用该模型数据加载
sync_enabled: true   # 是否启用该模型同步

# 云内侧配置
cloud_side:
  primary_model: "CLOUD_SFS_SHARE"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"

# 行内侧配置
internal_side:
  model: "CloudSfs"
  primary_key: "InstanceId"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  get_params:
    className: "CloudSfs"
    itemNames: [ "Code", "CIType", "Description", "LogicalRegionId", "LogicalRegionName", "AzoneName", "CloudSfsStatus", "Notes",
                 "CreatedAt", "ShareProto", "SharePath", "Size", "FileSystemId", "AppSysNo", "ApplicationSystem", "InstanceId" ]
    attribute: [ ]
    isRelatedCIId: true
  update_params:
    className: "CloudSfs"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "CloudSfs"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "InstanceId"  # 主键字段值
  delete_params:
    className: "CloudSfs"
    api_config:
      request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
      record_config:
        root_key: "record"  # 记录列表的根键
        id_field: "recordId"  # 记录ID字段
        table_field: "tableName"  # 表名字段
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称

# 字段映射配置 云内
field_mappings:
  # 基本信息映射
  - internal_field: "Description"
    cloud_field: "name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Code"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "AzoneName"
    internal_reference_key: "Code"
    cloud_field: "azoneId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "AzoneId"
    cloud_field: "azoneId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CloudSfsStatus"
    cloud_field: "status"
    #    transform: "share_status_mapping"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Notes"
    cloud_field: "description"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CreatedAt"
    cloud_field: "createAt"
    transform: "format_long_datetime"
    transform_params:
      field: "createAt"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "ShareProto"
    cloud_field: "shareProto"
    sync_direction: "cloud_to_internal"
    #    transform: "share_proto_mapping"
    required: true

  - internal_field: "SharePath"
    cloud_field: "sharePath"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Size"  # 单位是否统一不确定，830环境不支持该对象类型
    cloud_field: "size"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "FileSystemId"  # 待定
    cloud_field: "fileSystemId"
    sync_direction: "cloud_to_internal"
    required: false

  # 行内新增属性
  - internal_field: "InstanceId"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionId"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionName"
    internal_reference_key: "Code"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "AppSysNo"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "SysNo"
    required: true

  - internal_field: "ApplicationSystem"
    #    internal_reference_key: "Description"
    #    cloud_field: "applicationSystem"
    #    transform: "ApplicationSystem_constant"
    #    sync_direction: "cloud_to_internal"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "Description"
      replace_field: "SysNo"
      constant: "-"
    required: true


# 模型特有的转换函数
transformers:
  share_status_mapping: #creating=创建中, available=可用, error_deleting=删除失败, error=故障
    type: "mapping"
    mapping:
      "creating": "创建中"
      "available": "可用"
      "error_deleting": "删除失败"
      "error": "故障"

  share_proto_mapping: # NFS/CIFS/DPC/NFS_CIFS/NFS_DPC/CIFS_DPC
    type: "mapping"
    mapping:
      "NFS": ""
      "CIFS": ""
      "DPC": ""
      "NFS_CIFS": ""
      "NFS_DPC": ""
      "CIFS_DPC": ""