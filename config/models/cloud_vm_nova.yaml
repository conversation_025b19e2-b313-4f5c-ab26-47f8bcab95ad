model_id: "CLOUD_VM_NOVA"
model_name: "虚拟机"
description: "虚拟机模型同步配置"
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: true        # 是否启用该模型数据加载
sync_enabled: true   # 是否启用该模型同步
sync_model_order: 3

# 云内侧配置
cloud_side:
  primary_model: "CLOUD_VM_NOVA"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"
  # 关联模型配置
  related_models:
#    - model: "SYS_X86Server"
#      endpoint: "cloud_cmdb_model"
#      join_key: "serverId"
#      foreign_key: "id"
#      relation_type: "one_to_one"
#      relation_method: "direct"
    - model: "R_Volume_MountOn_VM"
      relation_type: "one_to_many"
      relation_method: "relation_table"
      relation_config:
        endpoint: "cloud_relation_api"
        params:
          condition:
            {
              "constraint": [
                {
                  "simple": {
                    "name": "vmId",
                    "value": "{id}",
                    "operator": "in"
                  }
                }
              ]
            }
        source_key: "vmId"
        target_key: "volumeId"
        source_field: "id"  # 源实体中用于关联的字段，默认为主键
      target_config:
        endpoint: "cloud_cmdb_model"
        model: "CLOUD_VOLUME"
        params: &filter_by_ids
          condition:
            {
              "constraint": [
                {
                  "simple": {
                    "name": "id",
                    "value": "{target_ids}",
                    "operator": "in"
                  }
                }
              ]
            }
        id_key: "id"
    - model: "M_VMUsesFlavor"
      relation_type: "one_to_one"
      relation_method: "relation_table"
      relation_config:
        endpoint: "cloud_relation_api"
        params:
          condition:
            {
              "constraint": [
                {
                  "simple": {
                    "name": "source_Instance_Id",
                    "value": "{id}",
                    "operator": "in"
                  }
                }
              ]
            }
        source_key: "source_Instance_Id"
        target_key: "target_Instance_Id"
        source_field: "id"  # 源实体中用于关联的字段，默认为主键
      target_config:
        endpoint: "cloud_cmdb_model"
        model: "CLOUD_FLAVOR"
        params: *filter_by_ids
        id_key: "id"

# 行内侧配置
internal_side:
  model: "CLOUD_VM_NOVA"
  primary_key: "InstanceId"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  delete_endpoint: "batch_delete_ci"
  get_params:
    className: "CLOUD_VM_NOVA"
    itemNames: [ "Description", "Code", "Notes", "HostGroup", "CreatedTime", "HostRunType", "PrdIPAddr", "OS", "DiskCapacity", "ServiceIPAddr", "HostType","HostClusterType",
                 "CPUCoreNumber", "Memory", "HostedServer", "AdminTeam", "IPInfo", "ManageIPAddr", "ApplicationSystem", "Syscode", "HostName", "OSKernelVersion",
                 "PrivateIPAddr", "InstanceId", "CIType", "LogicalRegionId", "LogicalRegionName", "AzoneId", "AzoneName","AdministratorA","AdministratorB", "IdTenant" ]
    attribute: [ ]
    isRelatedCIId: true
  update_params:
    className: "CLOUD_VM_NOVA"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "CLOUD_VM_NOVA"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "InstanceId"  # 主键字段值
  delete_params:
    className: "CLOUD_VM_NOVA"
    api_config:
      request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
      record_config:
        root_key: "record"  # 记录列表的根键
        id_field: "recordId"  # 记录ID字段
        table_field: "tableName"  # 表名字段
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称
      #  related_models:
      #    - className: "ApplicationSystem"
      #      itemNames: [ "Description","Code","SysNo","SysCode","RDTeam","MTUnit","AppSystemType","SysAdminA","SysAdminB","AppAdminA","AppAdminB" ]
    #如果是只需要负责人AB角 可以加以下条件 减少查询次数
#      attribute: "{\"and\":[{\"attribute\":\"BizAdminA\",\"operator\":[{\"operatorType\":\"isnotnull\",\"value\":\"\"}],\"type\":\"varchar\"},{\"attribute\":\"BizAdminB\",\"operator\":[{\"operatorType\":\"isnotnull\",\"value\":\"\"}],\"type\":\"varchar\"}]}"

# 字段映射配置 云内
field_mappings:
  # 基本信息映射
  - internal_field: "Notes"
    cloud_field: "remark"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Description"
    cloud_field: "name"
    sync_direction: "cloud_to_internal"
    #    is_check: true
    required: true

  - internal_field: "Code"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "concat_fields"
    transform_params:
      fields: [ "privateIps" ]
      index: 0
      constant: "CLOUD_VM_NOVA"
      separator: "_"  # 使用下划线为分隔符
      skip_empty: true  # 跳过空值
      ipv4_only: true
    required: true

  - internal_field: "HostGroup"
    cloud_field: "remark"
    sync_direction: "cloud_to_internal"
    #    is_check: true
    required: false

  - internal_field: "CreatedTime"
    cloud_field: "createTime"
    sync_direction: "cloud_to_internal"
    transform: "format_long_datetime"
    transform_params:
      field: "createTime"
    #    is_check: true
    required: true

  - internal_field: "HostRunType"
    cloud_field: "status"
    sync_direction: "cloud_to_internal"
    transform: "run_status"
    required: true

  - internal_field: "PrdIPAddr"
    cloud_field: "privateIps"
    sync_direction: "cloud_to_internal"
    transform: "get_ip_address"
    transform_params:
      separator: ";"  # 使用逗号为分隔符
      index: 0  # 获取第一个IP地址
      field: "privateIps"
    #    is_check: true
    required: true

  - internal_field: "OS"
    cloud_field: "osType"
    sync_direction: "cloud_to_internal"
    #    is_check: true
    required: false

  # 内存相关字段映射
  - internal_field: "DiskCapacity"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "calculate_field_sum"
    transform_params:
      field: "diskSize"
      format: "{value:.2f}"  # 保留两位小数
    required: true

  - internal_field: "CPUCoreNumber"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "calculate_related_sum"
    transform_params:
      model: "M_VMUsesFlavor"
      attribute: "vcpuSize"
      format: "{value:.0f}"  # 取整
    #    is_check: true
    required: true

  - internal_field: "Memory"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "calculate_related_sum"
    transform_params:
      model: "M_VMUsesFlavor"
      attribute: "ramSize"
      convert_unit: true
      format: "{value:.2f}"
    required: true

  - internal_field: "HostedServer"  # 28号版本外键依赖表不上线，这个字段暂时不同步
    internal_reference_key: "Description"
    cloud_field: "hostId"
    transform: "PCHostServer_constant"
    sync_direction: "cloud_to_internal"
    required: false

  - internal_field: "IPInfo"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "concat_fields"
    transform_params:
      fields: [ "privateIps", "ipAddress", "floatingIp", "externalRelayIpAddress" ]
      format: "{value}"
      separator: ";"
      skip_empty: true
    required: true

#  - internal_field: "ServiceIPAddr"
#    cloud_field: "custom"
#    sync_direction: "cloud_to_internal"
#    transform: "concat_fields"
#    transform_params:
#      fields: [ "privateIps", "ipAddress", "floatingIp", "externalRelayIpAddress" ]
#      format: "{value}"
#      separator: ";"
#      skip_empty: true
#    required: false

  - internal_field: "ManageIPAddr"
    cloud_field: "ipAddress"
    transform: "get_ip_address"
    transform_params:
      separator: ";"  # 使用逗号为分隔符
      index: 0  # 获取第一个IP地址
      field: "ipAddress"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "ApplicationSystem"
    cloud_field: "epsId"
    internal_reference_key: "SysNo"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "SysNo"
      default_value: "L00500"
    required: true

  - internal_field: "AdministratorB"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "SysAdminB"
      default_value: "L00500"
    required: false

  - internal_field: "AdministratorA"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "SysAdminA"
      default_value: "L00500"
    required: false

  - internal_field: "PrivateIPAddr"
    cloud_field: "ipAddress"
    transform: "get_ip_address"
    transform_params:
      separator: ";"  # 使用逗号为分隔符
      index: 0  # 获取第一个IP地址
      field: "ipAddress"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Syscode"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "SysNo"
      default_value: "L00500"
    required: true

  # 行内新增属性
  - internal_field: "InstanceId"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionId"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionName"
    internal_reference_key: "Code"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "AzoneId"
    cloud_field: "azoneId"  #云内无AzoneId
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "AzoneName"
    internal_reference_key: "Code"
    cloud_field: "azoneId"
    sync_direction: "cloud_to_internal"
    required: true
  # 行内校验必填字段，同步映射表字段未提及，暂时给常量
  #  - internal_field: "HostType"
  #    cloud_field: "custom"
  #    sync_direction: "cloud_to_internal"
  #    transform: "HostType_constant"
  #    required: true

  - internal_field: "AdminTeam"  # 行内定时任务自己处理
    internal_reference_key: "Description"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "MTUnit"
      replace_field: "SysNo"
      constant: "-"
    required: false

  # 新增
  - internal_field: "HostName"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "calculate_related_sum"
    transform_params:
      model: "SYS_X86Server"
      attribute: "hostName"
      format: "{value}"
    required: false

  - internal_field: "OSKernelVersion"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "calculate_related_sum"
    transform_params:
      model: "SYS_X86Server"
      attribute: "osKernelVersion"
      format: "{value}"
    required: false

  - internal_field: "HostType"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "calculate_related_sum"
    transform_params:
      model: "SYS_X86Server"
      attribute: "hostType"
      format: "{value}"
    required: false

  - internal_field: "HostClusterType"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "calculate_related_sum"
    transform_params:
      model: "SYS_X86Server"
      attribute: "deviceType"
      format: "{value}"
    required: false

  #新增机构
  - internal_field: "IdTenant"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "IdTenant"
      default_value: "L00500" # 新云应用系统SysNo编码 默认
    internal_reference_key: "Id"
    required: true

transformers:

  run_status: #03:已失效  未找到对应，待定
    type: "mapping"
    mapping:
      #      "other": "其他"
      #      live_volume_migrating: "跨存储热迁移中"
      #      error: "故障"
      #      "building": "04"
      #      shutting_down: "删除中"
      #      create_failed: "创建失败"
      #      hibernating: "休眠中"
      #      verify_resize: "更新规格校验中"
      #      migrating: "迁移中"
      #      reboot: "重启中"
      #      stopped: "已停止"
      #      hibernated: "已休眠"
      #      create_success: "创建成功"
      "active": "00"
      #      recycling: "回收中"
      #      pause: "已暂停"
      "suspended": "02"
      "shutoff": "01"
      #      deleted: "已删除"
      #      removed: "软删除"
      #      stopping: "停止中"
      #      resize: "修改中"
      #      storage_migrating: "磁盘迁移中"
      #      starting: "启动中"
      #      live_volume_migrating_fail: "跨存储热迁移失败"
      #      fault_resuming: "故障恢复中"
      "default": "03"  # 行内暂时不支持04 将04改成00测试用

#check_templates:
#  - template: condition_group
#    id: "normal_status"
#    params:
#      condition_field: "status"
#      condition_value: "active"  # 运行中
#
#  - template: conditional_required
#    scope: "record"
#    params:
#      condition_ref: "normal_status"
#      required_field: "name"
#      message: "设备状态是运行中或者测试中，配置项名称必填"
#
#  - template: conditional_required
#    scope: "record"
#    params:
#      condition_ref: "normal_status"
#      required_field: "remark"  # 所属主机组云内值来源不确定
#      message: "设备状态是运行中或运行中（测试环境），所属主机组必填"
#
#  - template: conditional_required
#    scope: "record"
#    params:
#      condition_ref: "normal_status"
#      required_field: "createTime"
#      message: "设备状态是运行中或运行中（测试环境），创建时间必填"
#
#  - template: conditional_required
#    scope: "record"
#    params:
#      condition_ref: "normal_status"
#      required_field: "floatingIp"
#      message: "设备状态是运行中或运行中（测试环境），生产IP必填"
#
#  - template: conditional_required
#    scope: "record"
#    params:
#      condition_ref: "normal_status"
#      required_field: "osType"
#      message: "设备状态是运行中或运行中（测试环境），操作系统必填"
#
#  # 使用条件非零容量检查模板
#  - template: conditional_non_zero_capacity
#    scope: "record"
#    params:
#      condition_field: "status"
#      condition_value: "active"
#      field: "DiskCapacity"
#      message: "设备状态是运行中或运行中（测试环境），硬盘总容量必填"
#
#  - template: numeric_range
#    scope: "record"
#    params:
#      field: "CPUCoreNumber"
#      max_value: 10000
#      message: "CPU（core数）数值必须小于10000"