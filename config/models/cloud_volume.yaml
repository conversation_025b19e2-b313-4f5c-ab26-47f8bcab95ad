model_id: "CLOUD_VOLUME"
model_name: "云硬盘"
description: "云硬盘模型同步配置"
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: true        # 是否启用该模型数据加载
sync_enabled: true   # 是否启用该模型同步

# 云内侧配置
cloud_side:
  primary_model: "CLOUD_VOLUME"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"

# 行内侧配置
internal_side:
  model: "CloudDisk"
  primary_key: "InstanceId"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  get_params:
    className: "CloudDisk"
    itemNames: ["Code", "LogicalRegionName", "TenantName", "VdcName", "DiskStatus", "Notes", "CreatedAt", "Size", "Bootable",
                "ShareType", "LunWwn", "AttachBackendName", "AppSysNo", "ApplicationSystem", "InstanceId", "CIType", "LogicalRegionId"]
    attribute: [ ]
    isRelatedCIId: true
  update_params:
    className: "CloudDisk"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "CloudDisk"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "InstanceId"  # 主键字段值
  delete_params:
    className: "CloudDisk"
    api_config:
      request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
      record_config:
        root_key: "record"  # 记录列表的根键
        id_field: "recordId"  # 记录ID字段
        table_field: "tableName"  # 表名字段
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称

# 字段映射配置 云内
field_mappings:
  # 基本信息映射
  - internal_field: "Code"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Notes"
    cloud_field: "description"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TenantName"
    cloud_field: "tenantName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "VdcName"
    cloud_field: "vdcName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "StoragePool"
    cloud_field: "poolId"
    sync_direction: "cloud_to_internal"
    required: false

  - internal_field: "DiskStatus"
    cloud_field: "status"  # 行内Statu字段位特殊字段，长度限制不满足需求，改成了DiskStatus
    sync_direction: "cloud_to_internal"
#    transform: "lun_type_mapping"
    required: true

  - internal_field: "CreatedAt"
    cloud_field: "createdAt"
    sync_direction: "cloud_to_internal"
    transform: "format_long_datetime"
    transform_params:
      field: "createdAt"
    required: true

  - internal_field: "Size" #GB
    cloud_field: "size"    #GB
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Bootable"
    cloud_field: "bootable"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "ShareType"
    cloud_field: "shareType"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LunWwn"
    cloud_field: "lunWwn"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "AttachBackendName"
    cloud_field: "attachBackendName"
    sync_direction: "cloud_to_internal"
    required: true

    # 行内新增属性
  - internal_field: "InstanceId"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionId"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionName"
    internal_reference_key: "Code"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "AppSysNo"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "SysNo"
    required: true

  - internal_field: "ApplicationSystem"
    internal_reference_key: "Description"
    cloud_field: "applicationSystem"
    transform: "ApplicationSystem_constant"
    sync_direction: "cloud_to_internal"
#    cloud_field: "custom"
#    sync_direction: "cloud_to_internal"
#    transform: "get_from_application_code"
#    transform_params:
#      cloud_field: "epsId"
#      model_name: "ApplicationSystem"
#      model_field: "Description"
#      replace_field: "SysNo"
#      constant: "-"
    required: true

# 模型特有的转换函数
transformers:
  lun_type_mapping:  # 行内新增CI，类型暂时以云内为准
    type: "mapping"
    mapping:
      "retyping": "正在变更"
      "available": "可用"
      "restoring": "正在恢复"
      "error": "错误"
      "in-use": "正在使用"
      "attaching": "正在挂载"
      "unknown": "其他"
      "error_extending": "扩容失败"
      "shrinking": "回收中"
      "downloading": "正在下载"
      "copying": "复制中"
      "error_restoring": "恢复数据失败"
      "migrating": "迁移中"
      "detaching": "正在卸载"
      "rollbacking": "正在回滚"
      "lose": "丢失"
      "error_deleting": "删除失败"
      "snapshoting": "快照中"
      "formating": "格式化中"
      "deleting": "正在删除"
      "removed": "正在创建"
      "restored": "已恢复"
      "extending": "正在扩容"
      "error_rollbacking": "回滚数据失败"