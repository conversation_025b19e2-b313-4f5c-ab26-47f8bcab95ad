# 接入光纤交换机模型配置
model_id: "cloud_kafka"
model_name: "Kafka"
description: "Kafka模型同步配置"
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: true        # 是否启用该模型数据加载
sync_enabled: true   # 是否启用该模型同步
resource_type: "cloud"
#sync_model_order: 4


# 云内侧配置
cloud_side:
  primary_model: "CLOUD_DMS_INSTANCE"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"



# 行内侧配置
internal_side:
  model: "DmsKafka"
  primary_key: "InstanceId"
  cloud_primary_key: "id"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  get_params:
    className: "DmsKafka"
    itemNames: [ "Code", "CIType", "Description","LogicalRegionId", "LogicalRegionName","TenantId","TenantName","VdcId","VdcName",
                 "Introduce","VpcId","NetworkId","SecurityGroupId","CreateTime","UpdateTime","ProjectId","InstanceId","Capacity","Dataplane",
                 "Engine","Engineversion","IpaddressPort","Managedstatus","Tenanttype","Type","EpsId","Sysname","KafkaStatus","IdTenant"]
    attribute: []
  update_params:
    className: "DmsKafka"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "DmsKafka"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "InstanceId"  # 主键字段值
  delete_params:
    className: "DmsKafka"
    api_config:
      request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
      record_config:
        root_key: "record"  # 记录列表的根键
        id_field: "recordId"  # 记录ID字段
        table_field: "tableName"  # 表名字段
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称

# 字段映射配置
field_mappings:
  - internal_field: "Code"
    cloud_field: "resId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Description"
    cloud_field: "name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionId"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
#    internal_reference_key: "Code"
    required: true

  - internal_field: "LogicalRegionName"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    internal_reference_key: "Code"
    required: true

  - internal_field: "ProjectId"
    cloud_field: "projectId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TenantId"
    cloud_field: "tenantId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TenantName"
    cloud_field: "tenantName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "VdcId"
    cloud_field: "vdcId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "VdcName"
    cloud_field: "vdcName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Introduce"
    cloud_field: "remark"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "VpcId"
    cloud_field: "ref_vpcId"
    sync_direction: "cloud_to_internal"
    internal_reference_key: "InstanceId"
    required: true

  - internal_field: "NetworkId"
    cloud_field: "ref_subnetId"
    sync_direction: "cloud_to_internal"
    internal_reference_key: "InstanceId"
    required: true

  - internal_field: "SecurityGroupId"
    cloud_field: "ref_securityGroupId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CreateTime"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "format_long_datetime"
    transform_params:
      field: "creation_date"
    required: true

  - internal_field: "UpdateTime"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "format_long_datetime"
    transform_params:
      field: "last_update_date"
    required: true

  - internal_field: "InstanceId"
    cloud_field: "resId"
    sync_direction: "cloud_to_internal"
    required: true


  - internal_field: "Capacity"
    cloud_field: "capacity"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Dataplane"
    cloud_field: "dataPlane"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Engine"
    cloud_field: "engine"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Engineversion"
    cloud_field: "engineVersion"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "IpaddressPort"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "concat_ip_and_port"
    transform_params:
      ip_field: "ipAddress"
      port_field: "port"
      separator: ";"  # 使用分号作为分隔符
      skip_empty: True  # 跳过空值
    required: true

  - internal_field: "Managedstatus"
    cloud_field: "managedStatus"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Tenanttype"
    cloud_field: "tenantType"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Type"
    cloud_field: "type"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "EpsId"
    cloud_field: "epsId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "KafkaStatus"
    cloud_field: "status"
    sync_direction: "cloud_to_internal"
    transform: "kafka_status_mapping_status"
    required: true


  - internal_field: "Sysname"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "SysNo"
      default_value: "L00500"
    internal_reference_key: "SysNo"
    required: true

    #新增机构
  - internal_field: "IdTenant"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "IdTenant"
      default_value: "L00500" # 给个默认值的key 这样改动 代码改动量最小
    internal_reference_key: "Id"
    required: true

transformers:
  kafka_status_mapping_status:
    type: "mapping"
    mapping:
      PAYING: '支付中'
      CREATING: '创建中'
      RUNNING: '运行中'
      CLOSING: '关闭中'
      STARTING: '启动中'
      RESTARTING: '重启中'
      CLOSED: '已关闭'
      DELETING: '删除中'
      DELETED: '已删除'
      ERROR: '运行异常'
      FAILURE: '配置失败'
      SUCCESS: '配置成功'
      CREATEFAILED: '创建失败'
      SOFTDELETE: '软删除'
      DELETEFAILED: '删除失败'
      FREEZING: '冻结中'
      FROZEN: '已冻结'
      EXTENDING: '扩容中'
      EXTENDEDFAILED: '扩容失败'
      MIGRATING: '迁移中'
      UPGRADING: '升级中'
    description: "Kafka状态配置"  # 添加映射说明

check_templates:
  # 使用字段值匹配模板
  - template: field_value_match
    scope: "record"  # 或 "record"，取决于您的验证需求
    params:
      field: "engine"
      expected_value: "kafka"
      message: "Kafka模型的engine配置必须为Kafka"