# 接入光纤交换机模型配置
model_id: "cloud_nacos"
model_name: "Nacos"
description: "Nacos模型同步配置"
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: true        # 是否启用该模型数据加载
sync_enabled: true   # 是否启用该模型同步
resource_type: "cloud"
#sync_model_order: 4

# 云内侧配置
cloud_side:
  primary_model: "CLOUD_CSE_ENGINES"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"



# 行内侧配置
internal_side:
  model: "CseNacos"
  primary_key: "InstanceId"
  cloud_primary_key: "id"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  get_params:
    className: "CseNacos"
    itemNames: ["Code","CIType","Description","LogicalRegionId","LogicalRegionName","ProjectId","TenantId","TenantName","VdcId",
                "VdcName","Version","CreateTime","Flavor","ExternalAddress","AuthType","VmIds", "InstanceId","EpsId","Sysname","NacosStatus","IdTenant"]
    attribute: []
  update_params:
    className: "CseNacos"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "CseNacos"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "InstanceId"  # 主键字段值
  delete_params:
    className: "CseNacos"
    api_config:
      request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
      record_config:
        root_key: "record"  # 记录列表的根键
        id_field: "recordId"  # 记录ID字段
        table_field: "tableName"  # 表名字段
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称

# 字段映射配置
field_mappings:
  - internal_field: "Code"
    cloud_field: "resId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Description"
    cloud_field: "name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionId"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
#    internal_reference_key: "Code"
    required: true

  - internal_field: "LogicalRegionName"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    internal_reference_key: "Code"
    required: true


  - internal_field: "ProjectId"
    cloud_field: "projectId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TenantId"
    cloud_field: "tenantId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TenantName"
    cloud_field: "tenantName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "VdcId"
    cloud_field: "vdcId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "VdcName"
    cloud_field: "vdcName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Version"
    cloud_field: "version"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CreateTime"
    cloud_field: "createTime"
    sync_direction: "cloud_to_internal"
    transform: "format_long_datetime"
    transform_params:
      field: "createTime"
    required: true

  - internal_field: "Flavor"
    cloud_field: "flavor"
    sync_direction: "cloud_to_internal"
    required: true


  - internal_field: "ExternalAddress"
    cloud_field: "externalAddress"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "AuthType"
    cloud_field: "authType"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "VmIds"
    cloud_field: "vmIds"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "InstanceId"
    cloud_field: "resId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "EpsId"
    cloud_field: "projectId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "NacosStatus"
    cloud_field: "status"
    sync_direction: "cloud_to_internal"
    transform: "nacos_status_mapping_status"
    required: true


  - internal_field: "Sysname"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "Description"
      default_value: "L00500"
    required: true
    #新增机构
  - internal_field: "IdTenant"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "IdTenant"
      default_value: "L00500" # 给个默认值 如果没有就是新云的编码  先给这个值  后面好替换XinYunCode
    internal_reference_key: "Id"
    required: true

transformers:
  nacos_status_mapping_status:
    type: "mapping"
    mapping:
      Creating: '创建中'
      Available: '运行中'
      Unavailable: '不可用'
      Deleting: '删除中'
      Deleted: '已删除'
      Upgrading: '升级中'
      Modifying: '变更中'
      UpgradeFailed: '升级失败'
      ModifyFailed: '变更失败'
      Freezed: '已冻结'
      CreateFailed: '创建失败'
      DeleteFailed: '删除失败'
    description: "Nocas状态"  # 添加映射说明
