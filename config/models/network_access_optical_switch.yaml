# 接入光纤交换机模型配置
model_id: "access_optical_switch"
model_name: "接入光纤交换机"
description: "接入光纤交换机模型同步配置"
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: true        # 是否启用该模型数据加载
sync_enabled: true   # 是否启用该模型同步
resource_type: "physical"  # 资源类型：physical（物理类资源）或 cloud（云资源）
#sync_model_order: 5
# 云内侧配置
cloud_side:
  primary_model: "SYS_Switch"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  match_key: "sn"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"
  related_models:
    #通过关系表关联的模型
    - model: "SYS_NetworkDevicePort"
      endpoint: "cloud_cmdb_model"
      join_key: "id"
      foreign_key: "networkDeviceId"
      relation_type: "one_to_many"
      relation_method: "direct"
    # 这个是根据 目标设备的ID查询关联关系  根据设备ID查询机柜ID
#    - model: "M_RackContainsDevice"
#      relation_type: "one_to_many"
#      relation_method: "relation_table"
#      relation_config:
#        endpoint: "cloud_relation_api"
#        params:
#          condition:
#            {
#              "constraint": [
#                {
#                  "simple": {
#                    "name": "target_Instance_Id",
#                    "value": "{id}",
#                    "operator": "in"
#                  }
#                }
#              ]
#            }
#        source_key: "target_Instance_Id" # 这里的源应该是 设备的ID  在关系表里 设备ID作为目标ID
#        target_key: "source_Instance_Id" # 目标应该是 机柜的ID 在关系表里 设备ID作为ID
#        source_field: "id"  # 源实体中用于关联的字段，默认为主键
#      # 这里的目标实体为机柜信息
#      target_config:
#        endpoint: "cloud_cmdb_model"
#        model: SYS_Rack
#        params:
#          condition:
#            {
#              "constraint": [
#                {
#                  "simple": {
#                    "name": "id",
#                    "value": "{id}",
#                    "operator": "equal"
#                  }
#                }
#              ]
#            }
#        id_key: "id"


# 行内侧配置
internal_side:
  model: "AccessOpticalSwitch"
  primary_key: "SerialNumber"
  cloud_primary_key: "sn"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  get_params:
    className: "AccessOpticalSwitch"
    itemNames: ["SerialNumber","Code","Description","Model","DeviceName","DeviceStatus","Rack","RackBeginU","RackEndU",
                "AdminTeam","RatePower","SystemVersion","AmountOfPorts","AssetCode","AdministratorA","AdministratorB","IPInfo",
                "InstanceId","CIType","LogicalRegionId","LogicalRegionName","IdTenant"]
    attribute: []
  update_params:
    className: "AccessOpticalSwitch"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "AccessOpticalSwitch"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "SerialNumber"  # 主键字段值
  delete_params:
    className: "AccessOpticalSwitch"
    api_config:
      request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
      record_config:
        root_key: "record"  # 记录列表的根键
        id_field: "recordId"  # 记录ID字段
        table_field: "tableName"  # 表名字段
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称

# 字段映射配置
field_mappings:

  - internal_field: "SerialNumber"
    cloud_field: "sn"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Model"
    cloud_field: "productName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "SystemVersion"
    cloud_field: "version"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "AmountOfPorts"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "calculate_related_count"
    transform_params:
      model: "network_port"
      attribute: "id"
      condition:
        attribute: "networkDeviceId"
        operator: "equal"
    is_check: true
    required: true

  - internal_field: "IPInfo"
    cloud_field: "ipAddress"
    sync_direction: "cloud_to_internal"
    required: true

  # 其他基本字段映射
  - internal_field: "InstanceId"
    cloud_field: "resId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionId"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionName"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    internal_reference_key: "Code"
    required: true


#  - internal_field: "Description"
#    cloud_field: "name"
#    sync_direction: "internal_to_cloud"
#    required: true
#  - internal_field: "DeviceName"
#    cloud_field: "deviceName"
#    sync_direction: "internal_to_cloud"
#    required: false

#  - internal_field: "DeviceStatus"
#    cloud_field: "assetStatus"
#    sync_direction: "internal_to_cloud"
#    transform: "switch_device_status"
#    required: false

#  # 字段映射配置 行内
#  - internal_field: "Rack_RelatedId"   # Rack是REFERENCE字段,isRelatedCIId=true，结果响应：Rack_RelatedId
#    cloud_field: "rackId"
#    sync_direction: "internal_to_cloud"
#    is_check: true
#    required: false

#  - internal_field: "RackBeginU"
#    cloud_field: "position"
#    sync_direction: "internal_to_cloud"
#    transform: "compare_relation_field"
#    transform_params:
#      relation_model: "M_RackContainsDevice"
#      relation_key: "target_Instance_Id"
#      internal_field: "RackBeginU"
#      cloud_field: "position"
#    required: false
#
#  - internal_field: "RackEndU"
#    cloud_field: "custom"
#    sync_direction: "internal_to_cloud"
#    transform: "compare_relation_field"
#    transform_params:
#      relation_model: "M_RackContainsDevice"
#      relation_key: "target_Instance_Id"
#      internal_field: "RackEndU"
#      cloud_field: "occupyHeight"
#    required: false
#
#  - internal_field: "AdminTeam"
#    cloud_field: "adminTeam"
#    sync_direction: "internal_to_cloud"
#    required: false
#    add_attr_params:
#      display_name_zh: "设备归属处室"
#      display_name_cn: "AdminTeam"
#      defaultValue: ""
#      length: 255
#      type: "string"
#      enumInfo: []
#      unit: ""
#      analysisType: ""

#  - internal_field: "AssetCode"
#    cloud_field: "assertNo"
#    sync_direction: "internal_to_cloud"
#    required: false
#
#  - internal_field: "AdministratorA"
#    cloud_field: "incharger"
#    sync_direction: "internal_to_cloud"
#    transform: "concat_fields"
#    transform_params:
#      fields: [ "AdministratorA", "AdministratorB" ]
#      separator: ";"  # 使用分号作为分隔符
#      skip_empty: True  # 跳过空值
#    required: false
#
#  - internal_field: "OgMaintenceStartDate"
#    cloud_field: "warrantyStartTime"
#    sync_direction: "internal_to_cloud"
#    transform: "format_long_datetime"
#    transform_params:
#      field: "warrantyStartTime"
#    required: true
#
#  - internal_field: "OgMaintenceEndDate"
#    cloud_field: "warrantyEndTime"
#    sync_direction: "internal_to_cloud"
#    transform: "format_long_datetime"
#    transform_params:
#      field: "warrantyEndTime"
#    required: true
#  - internal_field: "ManageIPAddr"
#    cloud_field: "ipAddress"
#    sync_direction: "cloud_to_internal"
#    is_check: true
#    required: true

check_templates:
  # 条件复用组
#  - template: condition_group
#    id: "normal_status"
#    params:
#      condition_field: "managedStatus"
#      condition_value: "in-use"  # 云内设备状态没有测试中状态
#
#  - template: conditional_required
#    params:
#      condition_ref: "normal_status"
#      required_field: "ipAddress"
#      message: "设备状态是运行中或测试中，管理IP地址必填"

  - template: field_range_match
    params:
      field: "AmountOfPorts"
      min_value: 0
      max_value: 1000
      message: "端口数量,数值必须小于1000或为空值"
#  # 基本信息必填检查
#  - template: conditional_required
#    params:
#      condition_ref: "normal_status"
#      required_field: "RackBeginU"
#      message: "设备状态是运行中或测试中，机柜开始位置必填"
#  - template: conditional_required
#    params:
#      condition_ref: "normal_status"
#      required_field: "RackEndU"
#      message: "设备状态是运行中或测试中，机柜结束位置必填"