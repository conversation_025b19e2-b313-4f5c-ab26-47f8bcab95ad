# 端口模型配置
model_id: "cloud_port"
model_name: "端口"
description: "端口模型同步配置"
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: true        # 是否启用该模型数据加载
sync_enabled: true   # 是否启用该模型同步
resource_type: "cloud"  # 资源类型：physical（物理类资源）或 cloud（云资源）
sync_model_order: 4
# 云内侧配置
cloud_side:
  primary_model: "CLOUD_PORT"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"




# 行内侧配置
internal_side:
  model: "CloudPort"
  primary_key: "InstanceId"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  maxBatchSize: 1000
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  get_params:
    className: "CloudPort"
    itemNames: ["Code", "Description", "TenantName", "VdcName", "CloudPortStatus","AdminStateUp", "NetworkId", "DevId", "DevNativeId", "MacAddr","Mac",
                "VifType", "VnicType", "Qos", "QosPolicyId", "SubnetIds","ExtraSpecs", "IpAddress", "Introduce", "IpAddresses",
                "InstanceId", "CIType", "LogicalRegionId", "LogicalRegionName","AppSysNo", "ApplicationSystem","IdTenant"]
    attribute: []
  update_params:
    className: "CloudPort"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "CloudPort"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "InstanceId"  # 主键字段值
  delete_params:
    className: "CloudPort"
    api_config:
      request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
      record_config:
        root_key: "record"  # 记录列表的根键
        id_field: "recordId"  # 记录ID字段
        table_field: "tableName"  # 表名字段
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称


# 字段映射配置
field_mappings:
  - internal_field: "Code"
    cloud_field: "resId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Description"
    cloud_field: "name"
    transform: "concat_fields"
    transform_params:
      fields: [ "name" , "resId"]
      separator: "/"  # 使用下划线为分隔符
      skip_empty: true  # 跳过空值
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TenantName"
    cloud_field: "tenantName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "VdcName"
    cloud_field: "vdcName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CloudPortStatus"
    cloud_field: "status"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "AdminStateUp"
    cloud_field: "adminStateUp"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "NetworkId"
    cloud_field: "networkId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "DevId"
    cloud_field: "devId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "DevNativeId"
    cloud_field: "devNativeId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "MacAddr"
    cloud_field: "macAddr"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "VifType"
    cloud_field: "vifType"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "VnicType"
    cloud_field: "vnicType"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Qos"
    cloud_field: "qos"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "QosPolicyId"
    cloud_field: "qosPolicyId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "SubnetIds"
    cloud_field: "subnetIds"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "ExtraSpecs"
    cloud_field: "extraSpecs"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "IpAddress"
    cloud_field: "ip"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Introduce"
    cloud_field: "remark"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Mac"
    cloud_field: "mac"
    sync_direction: "cloud_to_internal"
    required: true


  - internal_field: "IpAddresses"
    cloud_field: "ipAddresses"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "InstanceId"
    cloud_field: "resId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionId"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
#    internal_reference_key: "Code"
    required: true

  - internal_field: "LogicalRegionName"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    internal_reference_key: "Code"
    required: true


  # 以下两个字段不存在
  - internal_field: "ApplicationSystem"
    cloud_field: "epsId"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "Description"
      default_value: "L00500" # 给个默认值 如果没有就是新云的编码  先给这个值  后面好替换XinYunCode
    required: true

  - internal_field: "AppSysNo"
    cloud_field: "epsId"
    transform: "get_from_application_code"
    sync_direction: "cloud_to_internal"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "SysNo"
      default_value: "L00500" # 给个默认值 如果没有就是新云的编码  先给这个值  后面好替换XinYunCode
    required: true
  
    #新增机构
  - internal_field: "IdTenant"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "IdTenant"
      default_value: "L00500" # 给个默认值 如果没有就是新云的编码  先给这个值  后面好替换XinYunCode
    internal_reference_key: "Id"
    required: true