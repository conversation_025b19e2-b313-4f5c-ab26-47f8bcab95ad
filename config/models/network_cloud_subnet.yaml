# 网络子网模型配置
model_id: "cloud_subnet"
model_name: "网络子网"
description: "网络子网模型同步配置"
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: true        # 是否启用该模型数据加载
sync_enabled: true   # 是否启用该模型同步
resource_type: "cloud"  # 资源类型：physical（物理类资源）或 cloud（云资源）
sync_model_order: 3
# 云内侧配置
cloud_side:
  primary_model: "CLOUD_SUBNET"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"




# 行内侧配置
internal_side:
  model: "CloudSubnet"
  primary_key: "Code"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  maxBatchSize: 1000
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  get_params:
    className: "CloudSubnet"
    itemNames: ["Code", "Description", "TenantName", "VdcName", "EnableDhcp","NetworkId", "DnsNameServers", "AllocationPools", "Cidr", "GatewayIp",
                "Ipv6AddressMode", "Ipv6RaMode", "TorId", "InstanceId", "CIType","LogicalRegionId", "LogicalRegionName", "AppSysNo", "ApplicationSystem","IdTenant"]
    attribute: []
  update_params:
    className: "CloudSubnet"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "CloudSubnet"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "InstanceId"  # 主键字段值
  delete_params:
    className: "CloudSubnet"
    api_config:
      request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
      record_config:
        root_key: "record"  # 记录列表的根键
        id_field: "recordId"  # 记录ID字段
        table_field: "tableName"  # 表名字段
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称



# 字段映射配置
field_mappings:
  - internal_field: "Code"
    cloud_field: "resId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Description"
    cloud_field: "name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TenantName"
    cloud_field: "tenantName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "VdcName"
    cloud_field: "vdcName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "EnableDhcp"
    cloud_field: "enableDhcp"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "NetworkId"
    cloud_field: "networkId"
    sync_direction: "cloud_to_internal"
    internal_reference_key: "InstanceId"
    required: true

  - internal_field: "DnsNameServers"
    cloud_field: "dnsNameServers"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "AllocationPools"
    cloud_field: "allocationPools"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Cidr"
    cloud_field: "cidr"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Ipv6AddressMode"
    cloud_field: "ipv6AddressMode"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Ipv6RaMode"
    cloud_field: "ipv6RaMode"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TorId"
    cloud_field: "torId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "GatewayIp"
    cloud_field: "gatewayIp"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "InstanceId"
    cloud_field: "resId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionId"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
#    internal_reference_key: "Code"
    required: true

  - internal_field: "LogicalRegionName"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    internal_reference_key: "Code"
    required: true

  # 以下两个字段不存在
  - internal_field: "ApplicationSystem"
    cloud_field: "epsId"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "Description"
      default_value: "L00500" # 给个默认值 如果没有就是新云的编码  先给这个值  后面好替换XinYunCode
    required: true

  - internal_field: "AppSysNo"
    cloud_field: "epsId"
    transform: "get_from_application_code"
    sync_direction: "cloud_to_internal"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "SysNo"
      default_value: "L00500" # 给个默认值 如果没有就是新云的编码  先给这个值  后面好替换XinYunCode
    required: true

    #新增机构
  - internal_field: "IdTenant"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "IdTenant"
      default_value: "L00500" # 给个默认值 如果没有就是新云的编码  先给这个值  后面好替换XinYunCode
    internal_reference_key: "Id"
    required: true