# 网络交换机端口模型配置
model_id: "network_device_port"
model_name: "网络交换机端口"
description: "网络交换机端口模型同步配置"
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: true        # 是否启用该模型数据加载
sync_enabled: true   # 是否启用该模型同步
resource_type: "cloud "  # 资源类型：physical（物理类资源）或 cloud（云资源）
#sync_model_order: 5
# 云内侧配置
cloud_side:
  primary_model: "SYS_NetworkDevicePort"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"
  related_models:
    #通过关系表关联的模型
    - model: "SYS_NetworkDevice"
      endpoint: "cloud_cmdb_model"
      join_key: "id"
      foreign_key: "networkDeviceId"
      relation_type: "one_to_many"
      relation_method: "direct"

# 行内侧配置
internal_side:
  model: "SwitchPort"
  primary_key: "InstanceId"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  maxBatchSize: 1000
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  get_params:
    className: "SwitchPort"
    itemNames: ["Description","PortName","PortDescr","PortRate","PortStatus","Device","SerialNumber","PortType","InstanceId", "CIType","LogicalRegionId","LogicalRegionName","IdTenant"]
    attribute: []
  update_params:
    className: "SwitchPort"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "SwitchPort"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "InstanceId"  # 主键字段值
  delete_params:
    className: "SwitchPort"
    api_config:
      request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
      record_config:
        root_key: "record"  # 记录列表的根键
        id_field: "recordId"  # 记录ID字段
        table_field: "tableName"  # 表名字段
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称
  # 需要根据这个数据里面的SerialNumber 判断我云内的关联设备是否存在
  related_models:
    - className: "NetworkDevice"
      itemNames: ["Description","Code","SerialNumber" ]
      attribute: []

# 字段映射配置
field_mappings:
  - internal_field: "Description"
    cloud_field: "name"
    sync_direction: "cloud_to_internal"
    transform: "concat_primary_and_related_fields"
    transform_params:
      item_fields: ["name"]
      related_fields: ["name"]
      related_model: "SYS_NetworkDevice"
      separator: "_"
      skip_empty: true
    required: true

  - internal_field: "Code"
    cloud_field: "name"
    sync_direction: "cloud_to_internal"
    transform: "concat_primary_and_related_fields"
    transform_params:
      item_fields: [ "name" ]
      related_fields: [ "name" ]
      related_model: "SYS_NetworkDevice"
      separator: "_"
      skip_empty: true
    required: true

  - internal_field: "PortName"
    cloud_field: "name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "PortDescr"
    cloud_field: "remark"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "PortRate"
    cloud_field: "speed"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "PortStatus"
    cloud_field: "operState"
    sync_direction: "cloud_to_internal"
    transform: "network_device_status"
    required: true

  - internal_field: "Device"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_related_attribute_and_check_internal"
    transform_params:
      model: "SYS_NetworkDevice"
      attribute: "sn"
      internal_model: "NetworkDevice"
      internal_attribute: "SerialNumber"
      source: related #source: "primary" 时，从 item 取属性值。source: "related" 时，从 related 取属性值（原有逻辑）。只要有一个匹配就立即返回该值，没有就返回 None。
    internal_reference_key: "SerialNumber"
#    is_check: true
    required: true

  - internal_field: "PortType"
    cloud_field: "logicalType"
    sync_direction: "cloud_to_internal"
    required: true

  # 其他基本字段映射
  - internal_field: "InstanceId"
    cloud_field: "resId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionId"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
#    internal_reference_key: "Code"
    required: true

  - internal_field: "LogicalRegionName"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    internal_reference_key: "Code"
    required: true




# 模型特有的转换函数    行内 -> 云内。 枚举转换
#administratively down；down；up

#up=正常, down=离线, unknown=未知
transformers:
  network_device_status:
    type: "mapping"
    mapping:
      "up": "up"    # 运行中
      "down": "down"   # 闲置
      "unknown": "administratively down"
    description: "核心光纤交换机状态"  # 添加映射说明

check_templates:
  - template: required_fields
    params:
      field: "Device"
      message: "字段:Device所对应的外键未找到"

