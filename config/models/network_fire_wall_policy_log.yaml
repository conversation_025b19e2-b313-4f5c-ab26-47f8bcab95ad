# 新增新云策略台账模型配置
model_id: "firewall_policy_log"
model_name: "新增新云策略台账"
description: "新增新云策略台账同步配置"
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: true        # 是否启用该模型数据加载
sync_enabled: false   # 是否启用该模型同步
resource_type: "cloud"  # 资源类型：physical（物理类资源）或 cloud（云资源）
# 云内侧配置
cloud_side:
  # 网络ACL
  primary_model: "CLOUD_VFW"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"
  related_models:
    # 端口
    - relation_model_name: "CLOUD_PORT"
      primary_endpoint: "cloud_cmdb_model"
      primary_key: "id"
      api_config:
        result_path: "objList"
        pagination:
          total_pages_field: "totalPageNo"
          page_field: "pageNo"
          size_field: "pageSize"
    #网络ACL策略
    - relation_model_name: "CLOUD_VFW_POLICY"
      primary_endpoint: "cloud_cmdb_model"
      primary_key: "id"
      api_config:
        result_path: "objList"
        pagination:
          total_pages_field: "totalPageNo"
          page_field: "pageNo"
          size_field: "pageSize"
    # 网络ACL规则
    - relation_model_name: "CLOUD_VFW_RULE"
      primary_endpoint: "cloud_cmdb_model"
      primary_key: "id"
      api_config:
        result_path: "objList"
        pagination:
          total_pages_field: "totalPageNo"
          page_field: "pageNo"
          size_field: "pageSize"
    # 安全组
    - relation_model_name: "CLOUD_SECURITY_GROUP"
      primary_endpoint: "cloud_cmdb_model"
      primary_key: "id"
      api_config:
        result_path: "objList"
        pagination:
          total_pages_field: "totalPageNo"
          page_field: "pageNo"
          size_field: "pageSize"
    # 安全组规则
    - relation_model_name: "CLOUD_SECURITY_GROUP_RULE"
      primary_endpoint: "cloud_cmdb_model"
      primary_key: "id"
      api_config:
        result_path: "objList"
        pagination:
          total_pages_field: "totalPageNo"
          page_field: "pageNo"
          size_field: "pageSize"
  relation_tables:
    # 网络ACL 和网络ACL 策略关系表
    - relation_table_name: "R_VFW_USE_POLICY"
      primary_endpoint: "cloud_relation_api"
      primary_key: "id"
      api_config:
        result_path: "objList"
        pagination:
          total_pages_field: "totalPageNo"
          page_field: "pageNo"
          size_field: "pageSize"
    # 网络ACL 策略 和网络ACL规则关系表
    - relation_table_name: "R_VFW_POLICY_USE_RULE"
      primary_endpoint: "cloud_relation_api"
      primary_key: "id"
      api_config:
        result_path: "objList"
        pagination:
          total_pages_field: "totalPageNo"
          page_field: "pageNo"
          size_field: "pageSize"
    # 网络ACL和端口 关系表
    - relation_table_name: "R_VFW_USE_PORT"
      primary_endpoint: "cloud_relation_api"
      primary_key: "id"
      api_config:
        result_path: "objList"
        pagination:
          total_pages_field: "totalPageNo"
          page_field: "pageNo"
          size_field: "pageSize"
    # 端口和安全组关系表
    - relation_table_name: "R_PORT_USE_SECURITY_GROUP"
      primary_endpoint: "cloud_relation_api"
      primary_key: "id"
      api_config:
        result_path: "objList"
        pagination:
          total_pages_field: "totalPageNo"
          page_field: "pageNo"
          size_field: "pageSize"

# 行内侧配置
internal_side:
  model: "CloudFirewallPolicy"
  primary_key: "InstanceId"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  maxBatchSize: 1000
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  get_params:
    className: "CloudFirewallPolicy"
    itemNames: ["Description","Code","Notes","Index","Policy","Protocol","SrcIp","SrcPort","DestIp","DestPort","Enabled",
                "RuleType","FirewallName","PolicyName","InstanceId","CIType","LogicalRegionId","LogicalRegionName","IdTenant"]
    attribute: []
  update_params:
    className: "CloudFirewallPolicy"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "CloudFirewallPolicy"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "InstanceId"  # 主键字段值
  delete_params:
    className: "CloudFirewallPolicy"
    api_config:
      request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
      record_config:
        root_key: "record"  # 记录列表的根键
        id_field: "recordId"  # 记录ID字段
        table_field: "tableName"  # 表名字段
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称

# 字段映射配置
field_mappings:
  # 从主模型 CLOUD_VFW 获取的字段
  - internal_field: "Description"
    cloud_field: "name"
    sync_direction: "cloud_to_internal"
    transform: "concat_fields"
    transform_params:
      fields: [ "name", "ruleName" ]  #集群存储没有序列号字段，匹配符待确认
      separator: "/"  # 使用下划线为分隔符
      skip_empty: true  # 跳过空值
    required: true

  - internal_field: "Code"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    transform: "concat_fields"
    transform_params:
      fields: [ "id", "ruleId" ]  #集群存储没有序列号字段，匹配符待确认
      separator: "/"  # 使用下划线为分隔符
      skip_empty: true  # 跳过空值
    required: true

  - internal_field: "Notes"
    cloud_field: "description"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Enabled"
    cloud_field: "enable"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Direction"
    cloud_field: "ruleType"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "IPType"
    cloud_field: "ipAddressType"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Position"
    cloud_field: "position"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Action"
    cloud_field: "action"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Protocol"
    cloud_field: "protocol"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "SrcIp"
    cloud_field: "ipAddress"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "SrcPort"
    cloud_field: "port"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "DestIp"
    cloud_field: "destinationIpAddress"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "DestPort"
    cloud_field: "destinationPort"
    sync_direction: "cloud_to_internal"
    required: true


  - internal_field: "SubnetIds"
    cloud_field: "subnetIds"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "RemoteInfo"
    cloud_field: "remoteIpPrefix"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "PortRangeMax"
    cloud_field: "portRangeMax"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "PortRangeMin"
    cloud_field: "portRangeMin"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TenantName"
    cloud_field: "tenantName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "VdcName"
    cloud_field: "vdcName"
    sync_direction: "cloud_to_internal"
    required: true
  # 其他基本字段映射
  - internal_field: "InstanceId"
    cloud_field: "resId"
    sync_direction: "cloud_to_internal"
    transform: "concat_fields"
    transform_params:
      fields: [ "id", "ruleId" ]  #集群存储没有序列号字段，匹配符待确认
      separator: "/"  # 使用下划线为分隔符
      skip_empty: true  # 跳过空值
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionId"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
#    internal_reference_key: "Code"
    required: true

  - internal_field: "LogicalRegionName"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    internal_reference_key: "Code"
    required: true



  - internal_field: "ApplicationSystem"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "Description"
      replace_field: "SysNo"
      constant: "-"
    required: true

    #新增机构
  - internal_field: "IdTenant"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "IdTenant"
      default_value: "L00500" # 给个默认值 如果没有就是新云的编码  先给这个值  后面好替换XinYunCode
    internal_reference_key: "Id"
    required: true