# LTM负载均衡VS模型配置
model_id: "load_balance_service"
model_name: "LTM负载均衡VS"
description: "LTM负载均衡VS模型同步配置"
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: true        # 是否启用该模型数据加载
sync_enabled: true   # 是否启用该模型同步
resource_type: "cloud"  # 资源类型：physical（物理类资源）或 cloud（云资源）
sync_model_order: 3
# 云内侧配置
cloud_side:
  primary_model: "CLOUD_LISTENER"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "resId"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"
  related_models:
    - model: "CLOUD_ELB"
      endpoint: "cloud_cmdb_model"
      join_key: "elbId"
      foreign_key: "id"
      relation_type: "one_to_many"
      relation_method: "direct"
    - model: "CLOUD_ELB_POOL"
      endpoint: "cloud_cmdb_model"
      join_key: "id"
      foreign_key: "listenerId"
      relation_type: "one_to_many"
      relation_method: "direct"

# 行内侧配置
internal_side:
  model: "LoadBalanceService"
  primary_key: "InstanceId"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  maxBatchSize: 1000
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  get_params:
    className: "LoadBalanceService"
    itemNames: ["Description","Code","VSName","VSStatus","VSAvailable","VIPAddr","VIPPort","IsSessionPersistence","LoadBalanceDevice",
                "InstanceId","CIType","LogicalRegionId","LogicalRegionName","IdTenant","ApplicationSystem","AppSysNo"]
    attribute: []
  update_params:
    className: "LoadBalanceService"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "LoadBalanceService"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "InstanceId"  # 主键字段值
  delete_params:
    className: "LoadBalanceService"
    api_config:
      request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
      record_config:
        root_key: "record"  # 记录列表的根键
        id_field: "recordId"  # 记录ID字段
        table_field: "tableName"  # 表名字段
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称

field_mappings:
  - internal_field: "Description"
    cloud_field: "name"
    sync_direction: "cloud_to_internal"
    transform: "concat_fields"
    transform_params:
      fields: [ "name", "resId" ]
      separator: "/"  # 使用下划线为分隔符
      skip_empty: true  # 跳过空值
    required: true

  - internal_field: "Code"
    cloud_field: "resId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "VSName"
    cloud_field: "name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "VSStatus"
    cloud_field: "adminStateUp"
    sync_direction: "cloud_to_internal"
    transform: "load_balance_service_vsstatus_mapping"
    required: true

  - internal_field: "VSAvailable"
    cloud_field: "status"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "VIPAddr"
    cloud_field: "ip"
    sync_direction: "cloud_to_internal"
    transform: "get_related_attribute"
    transform_params:
      model: "CLOUD_ELB"
      attribute: "ip"
    required: true

  - internal_field: "VIPPort"
    cloud_field: "port"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "IsSessionPersistence"
    cloud_field: "sessionSticky"
    sync_direction: "cloud_to_internal"
    required: true

#  - internal_field: "LoadBalanceDevice"
#    cloud_field: "custom"
#    sync_direction: "cloud_to_internal"
#    transform: "get_related_attribute"
#    transform_params:
#      model: "CLOUD_LISTENER"
#      attribute: "elbId"
#    required: true
  - internal_field: "LoadBalancePool"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_related_attribute"
    transform_params:
      model: "CLOUD_ELB_POOL"
      attribute: "resId"
    internal_reference_key: "InstanceId"
    required: true

  # 其他基本字段映射
  - internal_field: "InstanceId"
    cloud_field: "resId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CIType"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_related_attribute"
    transform_params:
      model: "CLOUD_ELB"
      attribute: "class_Name"
    required: true

  - internal_field: "LogicalRegionId"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
#    internal_reference_key: "Code"
    required: true

  - internal_field: "LogicalRegionName"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    internal_reference_key: "Code"
    required: true
  # 以下两个字段不存在
  - internal_field: "ApplicationSystem"
    cloud_field: "epsId"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "Description"
      default_value: "L00500" # 给个默认值 如果没有就是新云的编码  先给这个值  后面好替换XinYunCode
    required: true

  - internal_field: "AppSysNo"
    cloud_field: "epsId"
    transform: "get_from_application_code"
    sync_direction: "cloud_to_internal"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "SysNo"
      default_value: "L00500" # 给个默认值 如果没有就是新云的编码  先给这个值  后面好替换XinYunCode
    required: true
    #新增机构
  - internal_field: "IdTenant"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "IdTenant"
      default_value: "L00500" # 给个默认值 如果没有就是新云的编码  先给这个值  后面好替换XinYunCode
    internal_reference_key: "Id"
    required: true

transformers:
  load_balance_service_vsstatus_mapping:
    type: "mapping"
    mapping:
      "1": "enabled"
      "default": "disabled"