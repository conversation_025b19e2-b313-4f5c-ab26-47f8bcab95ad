# 设备链路表模型配置
model_id: "sys_deviceLink"
model_name: "设备链路表"
description: "设备链路表模型同步配置"
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: true        # 是否启用该模型数据加载
sync_enabled: false   # 是否启用该模型同步
resource_type: "cloud"  # 资源类型：physical（物理类资源）或 cloud（云资源）
# 云内侧配置
cloud_side:
  primary_model: "SYS_DeviceLink"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"
  related_models:
    # 网络设备信息
    - relation_model_name: "SYS_NetworkDevice"
      primary_endpoint: "cloud_cmdb_model"
      primary_key: "id"
      api_config:
        result_path: "objList"
        pagination:
          total_pages_field: "totalPageNo"
          page_field: "pageNo"
          size_field: "pageSize"
    #网络设备端口
    - relation_model_name: "SYS_NetworkDevicePort"
      primary_endpoint: "cloud_cmdb_model"
      primary_key: "id"
      api_config:
        result_path: "objList"
        pagination:
          total_pages_field: "totalPageNo"
          page_field: "pageNo"
          size_field: "pageSize"
    # 机柜
    - relation_model_name: "SYS_Rack"
      primary_endpoint: "cloud_cmdb_model"
      primary_key: "id"
      api_config:
        result_path: "objList"
        pagination:
          total_pages_field: "totalPageNo"
          page_field: "pageNo"
          size_field: "pageSize"
  relation_tables:
    # 网络ACL 策略 和网络ACL规则关系表
    - relation_table_name: "M_RackContainsDevice"
      primary_endpoint: "cloud_relation_api"
      primary_key: "id"
      api_config:
        result_path: "objList"
        pagination:
          total_pages_field: "totalPageNo"
          page_field: "pageNo"
          size_field: "pageSize"




# 行内侧配置
internal_side:
  model: "CloudDeviceLink"
  primary_key: "InstanceId"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  maxBatchSize: 1000
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  get_params:
    className: "CloudDeviceLink"
    itemNames: ["Code", "CIType", "Description", "Introduce", "SrcDeviceName","SrcDeviceId", "SrcDevicePortName", "SrcDevicePortId",
                "SrcDeviceRackName", "SrcDevicePosition", "TargetDeviceName", "TargetDeviceId", "TargetDevicePortName",
                "TargetDevicePortId","TargetDeviceRackName", "TargetDevicePosition","IdTenant"]
    attribute: []
  update_params:
    className: "CloudDeviceLink"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "CloudDeviceLink"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "InstanceId"  # 主键字段值
  delete_params:
    className: "CloudDeviceLink"
    api_config:
      request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
      record_config:
        root_key: "record"  # 记录列表的根键
        id_field: "recordId"  # 记录ID字段
        table_field: "tableName"  # 表名字段
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称
  # 需要根据这个数据里面的SerialNumber 判断我云内的关联设备是否存在
  related_models:
    - className: "NetworkDevice"
      itemNames: ["Description","Code","SerialNumber","InstanceId"]
      attribute: "{\"and\":[{\"attribute\":\"InstanceId\",\"operator\":[{\"operatorType\":\"isnotnull\",\"value\":\"\"}],\"type\":\"varchar\"}]}"
    - className: "SwitchPort"
      itemNames: [ "Description","Code","SerialNumber","InstanceId" ]
      attribute: "{\"and\":[{\"attribute\":\"InstanceId\",\"operator\":[{\"operatorType\":\"isnotnull\",\"value\":\"\"}],\"type\":\"varchar\"}]}"


# 字段映射配置
field_mappings:
  - internal_field: "Code"
    cloud_field: "resId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Description"
    cloud_field: "name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Introduce"
    cloud_field: "notes"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "SrcDeviceName"
    cloud_field: "srcDeviceName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "SrcDeviceId"
    cloud_field: "srcDeviceId"
    sync_direction: "cloud_to_internal"
    internal_reference_key: "InstanceId"
    is_check: true
    required: true

  - internal_field: "SrcDevicePortName"
    cloud_field: "srcDevicePortName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "SrcDevicePortId"
    cloud_field: "srcDevicePortId"
    sync_direction: "cloud_to_internal"
    internal_reference_key: "InstanceId"
    is_check: true
    required: true

  - internal_field: "SrcDeviceRackName"
    cloud_field: "srcDeviceRackName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "SrcDevicePosition"
    cloud_field: "srcDevicePosition"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TargetDeviceName"
    cloud_field: "targetDeviceName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TargetDeviceId"
    cloud_field: "targetDeviceId"
    sync_direction: "cloud_to_internal"
    internal_reference_key: "InstanceId"
    is_check: true
    required: true

  - internal_field: "TargetDevicePortId"
    cloud_field: "targetDevicePortId"
    sync_direction: "cloud_to_internal"
    internal_reference_key: "InstanceId"
    is_check: true
    required: true

  - internal_field: "TargetDevicePortName"
    cloud_field: "targetDevicePortName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TargetDeviceRackName"
    cloud_field: "targetDeviceRackName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TargetDevicePosition"
    cloud_field: "targetDevicePosition"
    sync_direction: "cloud_to_internal"
    required: true

#    #新增机构
#  - internal_field: "IdTenant"
#    cloud_field: "custom"
#    sync_direction: "cloud_to_internal"
#    transform: "get_from_application_code"
#    transform_params:
#      cloud_field: "epsId"
#      model_name: "ApplicationSystem"
#      model_field: "IdTenant"
#      default_value: "L00500" # 给个默认值 如果没有就是新云的编码  先给这个值  后面好替换XinYunCode
#    internal_reference_key: "Id"
#    required: true