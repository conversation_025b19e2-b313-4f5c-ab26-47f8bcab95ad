# 安全条线 -WEB应用防火墙服务（WAF）
model_id: "cloud_waf_engine"
model_name: "WEB应用防火墙服务"
description: "WEB应用防火墙服务同步配置"
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: true        # 是否启用该模型数据加载
sync_enabled: true   # 是否启用该模型同步
resource_type: "cloud"  # 资源类型：physical（物理类资源）或 cloud（云资源）
#sync_model_order: 4
# 云内侧配置
cloud_side:
  primary_model: "CLOUD_WAF_ENGINE"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"
  related_models:
    - model: "CLOUD_ROUTER"
      endpoint: "cloud_cmdb_model"
      join_key: "ref_vpcId"
      foreign_key: "resId"
      relation_type: "one_to_one"
      relation_method: "direct"


# 行内侧配置
internal_side:
  model: "CloudWaf"
  primary_key: "InstanceId"
  maxBatchSize: 1000
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  get_params:
    className: "CloudWfa"
    itemNames: ["Code", "Description", "LogicalRegionId", "LogicalRegionName", "TenantName", "VdcName", "CloudWfaStatus",
                "CreateTime", "VpcId", "SubnetId", "SecurityGroupId", "IpAddress", "FloatIpAddress", "vmIds",
                "AppSysNo", "ApplicationSystem", "InstanceId", "VpcName", "Brand", "DeployMode", "AdministratorB",
                "AdministratorA","InstanceId","CIType","LogicalRegionId","LogicalRegionName","IdTenant"]
    attribute: []
  update_params:
    className: "CloudWaf"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "CloudWaf"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "InstanceId"  # 主键字段值
  delete_params:
    className: "CloudWaf"
    api_config:
      request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
      record_config:
        root_key: "record"  # 记录列表的根键
        id_field: "recordId"  # 记录ID字段
        table_field: "tableName"  # 表名字段
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称


# 字段映射配置
field_mappings:
  - internal_field: "Code"
    cloud_field: "resId"
    sync_direction: "cloud_to_internal"
    transform: "concat_fields"
    transform_params:
      fields: ["resId"]
      constant: "SecuryAQ_WAF"
      separator: "_"
      skip_empty: true
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Description"
    cloud_field: "name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionId"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
#    internal_reference_key: "Code"
    required: true

  - internal_field: "LogicalRegionName"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    internal_reference_key: "Code"
    required: true


  - internal_field: "ProjectId"
    cloud_field: "projectId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TenantName"
    cloud_field: "tenantName"
    sync_direction: "cloud_to_internal"
    required: true


  - internal_field: "TenantId"
    cloud_field: "tenantId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "VdcId"
    cloud_field: "vdcId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "VdcName"
    cloud_field: "vdcName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CloudWfaStatus"
    cloud_field: "status"
    sync_direction: "cloud_to_internal"
    transform: "wfa_status"
    required: true

  - internal_field: "CreateTime"
    cloud_field: "createTime"
    sync_direction: "cloud_to_internal"
    transform: "format_long_datetime"
    transform_params:
      field: "createTime"
    required: true

  - internal_field: "VpcId"
    cloud_field: "ref_vpcId"
    sync_direction: "cloud_to_internal"
    internal_reference_key: "InstanceId"
    required: true

  - internal_field: "SubnetId"
    cloud_field: "ref_subnetId"
    sync_direction: "cloud_to_internal"
    internal_reference_key: "NetworkId"
    required: true

  - internal_field: "SecurityGroupId"
    cloud_field: "ref_securityGroupId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "IpAddress"
    cloud_field: "ipAddress"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "FloatIpAddress"
    cloud_field: "floatIpAddress"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "vmIds"
    cloud_field: "vmIds"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "AppSysNo"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "SysNo"
    required: true

  - internal_field: "ApplicationSystem"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "Description"
      replace_field: "SysNo"
      constant: "-"
    required: true

  - internal_field: "VpcName"
    cloud_field: "VpcName"
    sync_direction: "cloud_to_internal"
    transform: "get_related_attribute"
    transform_params:
      model: "CLOUD_ROUTER"
      attribute: "name"
    required: true

  - internal_field: "Brand"
    cloud_field: "Brand"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "DeployMode"
    cloud_field: "DeployMode"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "AdministratorB"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "AppAdminB"
    required: true

  - internal_field: "AdministratorA"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "AppAdminA"
    required: true

  - internal_field: "InstanceId"
    cloud_field: "resId"
    sync_direction: "cloud_to_internal"
    required: true

    #新增机构
  - internal_field: "IdTenant"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "IdTenant"
      default_value: "L00500" # 给个默认值 如果没有就是新云的编码  先给这个值总行 后面好替换192
    internal_reference_key: "Id"
    required: true

# 模型特有的转换函数    行内 -> 云内。 枚举转换
#0=创建中, 1=运行中, 2=删除中, 3=已删除, 4=创建失败, 5=已冻结, 6=运行异常, 7=升级中, 8=升级失败
transformers:
  wfa_status:
    type: "mapping"
    mapping:
      "0": "创建中"
      "1": "运行中"
      "2": "删除中"
      "3": "已删除"
      "4": "创建失败"
      "5": "已冻结"
      "6": "运行异常"
      "7": "升级中"
      "8": "升级失败"
    description: "WEB应用防火墙服务"  # 添加映射说明