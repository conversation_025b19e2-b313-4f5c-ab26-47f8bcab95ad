model_id: "SYS_BusinessRegion"
model_name: "业务地区"
description: "业务地区模型同步配置"
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: true        # 是否启用该模型数据加载
sync_enabled: true   # 是否启用该模型同步
sync_model_order: 1

# 云内侧配置
cloud_side:
  primary_model: "SYS_BusinessRegion"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"
  # 关联模型配置
  related_models:
    - model: "M_StorDevConsistsOfStorDisk"
      relation_type: "one_to_many"
      relation_method: "relation_table"
      relation_config:
        endpoint: "cloud_relation_api"
        params: &filter_by_source_id
          condition:
            {
              "constraint": [
                {
                  "simple": {
                    "name": "source_Instance_Id",
                    "value": "{id}",
                    "operator": "equal"
                  }
                }
              ]
            }
        source_key: "source_Instance_Id"
        target_key: "target_Instance_Id"
        source_field: "id"  # 源实体中用于关联的字段，默认为主键
      target_config:
        endpoint: "cloud_cmdb_model"
        model: "SYS_StorageDisk"
        params: &filter_by_ids
          condition:
            {
              "constraint": [
                {
                  "simple": {
                    "name": "id",
                    "value": "{target_ids}",
                    "operator": "in"
                  }
                }
              ]
            }
        id_key: "id"
    - model: "M_StorDevConsistsOfLun"
      relation_type: "one_to_many"
      relation_method: "relation_table"
      relation_config:
        endpoint: "cloud_relation_api"
        params: *filter_by_source_id
        source_key: "source_Instance_Id"
        target_key: "target_Instance_Id"
        source_field: "id"  
      target_config:
        endpoint: "cloud_cmdb_model"
        model: "SYS_Lun"
        params: *filter_by_ids
        id_key: "id"

# 行内侧配置
internal_side:
  model: "BusinessRegion"  # 未提供行内模型名称，暂定和云内名称一致
  primary_key: "InstanceId"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  get_params:
    className: "BusinessRegion"
    itemNames: ["Code", "Description", "Notes", "Type", "RegionType", "SolutionVersion", "StorTypeOfMgmt", "StorTypeOfBusiness",
                "SolutionScense", "GlobalServiceScale", "ServiceScale", "HistoryVersionList", "MgmtNodeCpuArch", "NetworkNodeType",
                "BusinessNetworkType", "MgmtNetworkType", "InstanceId", "CIType", "IdTenant"]
    attribute: []
    isRelatedCIId: true
  update_params:
    className: "BusinessRegion"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "BusinessRegion"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "InstanceId"  # 主键字段值
  delete_params:
    className: "CloudAZONE"
    api_config:
      request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
      record_config:
        root_key: "record"  # 记录列表的根键
        id_field: "recordId"  # 记录ID字段
        table_field: "tableName"  # 表名字段
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称
# 字段映射配置 云内
field_mappings:
  # 基本信息映射
  - internal_field: "Code"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Description"
    cloud_field: "name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Notes"
    cloud_field: "remark"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Type"
    cloud_field: "type"
    sync_direction: "cloud_to_internal"
#    transform: "region_type_mapping_1"  # 区域类型
    required: true

  - internal_field: "RegionType"
    cloud_field: "regionType"
    sync_direction: "cloud_to_internal"
#    transform: "region_type_mapping_2"  # 部署类型
    required: true

  - internal_field: "SolutionVersion"
    cloud_field: "solutionVersion"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "StorTypeOfMgmt"
    cloud_field: "storTypeOfMgmt"
    sync_direction: "cloud_to_internal"
#    transform: "stor_type_of_mgmt_mapping"
    required: true

  - internal_field: "StorTypeOfBusiness"
    cloud_field: "storTypeOfBusiness"
    sync_direction: "cloud_to_internal"
#    transform: "stor_type_of_business_mapping"
    required: true

  - internal_field: "SolutionScense"
    cloud_field: "solutionScense"
    sync_direction: "cloud_to_internal"
#    transform: "solution_scense_mapping"
    required: true

  - internal_field: "GlobalServiceScale"
    cloud_field: "globalServiceScale"
    sync_direction: "cloud_to_internal"
#    transform: "global_service_scale_mapping"
    required: true

  - internal_field: "ServiceScale"
    cloud_field: "serviceScale"
    sync_direction: "cloud_to_internal"
#    transform: "global_service_scale_mapping"  # 值域与全局业务规模相等
    required: true

  - internal_field: "HistoryVersionList"
    cloud_field: "historyVersionList"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "MgmtNodeCpuArch"
    cloud_field: "mgmtNodeCpuArch"
    sync_direction: "cloud_to_internal"
#    transform: "mgmt_node_cpu_arch_mapping"
    required: true

  - internal_field: "NetworkNodeType"
    cloud_field: "networkNodeType"
    sync_direction: "cloud_to_internal"
#    transform: "network_node_type_mapping"
    required: true

  - internal_field: "BusinessNetworkType"
    cloud_field: "businessNetworkType"
    sync_direction: "cloud_to_internal"
#    transform: "business_network_type_mapping"
    required: true

  - internal_field: "MgmtNetworkType"
    cloud_field: "mgmtNetworkType"
    sync_direction: "cloud_to_internal"
#    transform: "business_network_type_mapping"  # 值域与业务网络类型相等
    required: true

  # 行内新增属性
  - internal_field: "InstanceId"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "IdTenant"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    transform_params:
      cloud_field: "epsId"
      model_name: "ApplicationSystem"
      model_field: "IdTenant"
      default_value: "L00500" # 新云应用系统SysNo编码 默认
    internal_reference_key: "Id"
    required: true

# 模型特有的转换函数
transformers:
  stor_type_of_business_mapping:  # 行内没给对应的映射关系，当前按照云内映射关系映射
    type: "mapping"
    mapping:
      "fc_san": "FC SAN"
      "fusionstorage_separated": "FusionStorage分离部署"
      "xsky": "XSKY分布式块存储"
      "fusioncube_integrated": "FusionCube融合部署"
      "fusionstorage_integrated": "FusionStorage融合部署"
      "local_disk": "本地磁盘"
      "ip_san": "IP SAN"

  # 区域类型
  region_type_mapping_1: # 行内设备状态枚举值未知，当前枚举是云内的枚举
    type: "mapping"
    mapping:
      "Azure": "Azure"
      "CTC": "CTC"
      "CRS": "两级云"
      "HWS": "HWS"
      "HCSO": "华为云Stack Online"
      "private_cloud": "私有云"
      "AWS": "AWS"

  # 部署类型
  region_type_mapping_2: # 行内设备状态枚举值未知，当前枚举是云内的枚举
    type: "mapping"
    mapping:
      "slave": "从区域"
      "dr_slave": "容灾从区域"
      "dr_standby": "备区域"
      "master": "主区域"

  #网络节点类型  vm=虚拟机, pm=物理机
  network_node_type_mapping: # 行内设备状态枚举值未知，当前枚举是云内的枚举
    type: "mapping"
    mapping:
      "vm": "虚拟机"
      "pm": "物理机"

  stor_type_of_mgmt_mapping:  # 行内设备状态枚举值未知，当前枚举是云内的枚举
    type: "mapping"
    mapping:
      "fc_san": "FC SAN"
      "fusionstorage": "FusionStorage"
      "local_disk": "本地磁盘"
      "ip_san": "IP SAN"

  solution_scense_mapping: # 行内设备状态枚举值未知，当前枚举是云内的枚举
    type: "mapping"
    mapping:
      "nfvi": "NFVI"
      "hcs_lite": "华为云Stack轻量化"
      "hcs": "华为云Stack"

  mgmt_node_cpu_arch_mapping: # 行内设备状态枚举值未知，当前枚举是云内的枚举
    type: "mapping"
    mapping:
      "x86": "x86"
      "arm": "ARM"
      "unknown": "未知"

  # 管理网络类型 ipv4=IPv4, ipv6=IPv6, ipv4v6=IPv4&IPv6
  business_network_type_mapping: # 行内设备状态枚举值未知，当前枚举是云内的枚举
    type: "mapping"
    mapping:
      "ipv4": "IPv4"
      "ipv6": "IPv6"
      "ipv4v6": "IPv4&IPv6"

  global_service_scale_mapping:
    # 行内设备状态枚举值未知，当前枚举是云内的枚举
    type: "mapping"
    mapping:
      "10000pm_100000vm": "≤10000主机，≤100000虚拟机"
      "100pm_500vm": "≤100主机，≤500虚拟机"
      "200pm_2000vm": "≤200主机，≤2000虚拟机"
      "3000pm_30000vm": "≤3000主机，≤30000虚拟机"
      "50pm_500vm": "≤50主机，≤500虚拟机"
      "500pm_2500vm": "≤500主机，≤2500虚拟机"
      "500pm_5000vm": "≤500主机，≤5000虚拟机"
      "100pm_1000vm": "≤100主机，≤1000虚拟机"
      "2000pm_20000vm": "≤2000主机，≤20000虚拟机"
      "1000pm_10000vm": "≤1000主机，≤10000虚拟机"
      "20000pm_200000vm": "≤20000主机，≤200000虚拟机"
      "4000pm_40000vm": "≤4000主机，≤40000虚拟机"