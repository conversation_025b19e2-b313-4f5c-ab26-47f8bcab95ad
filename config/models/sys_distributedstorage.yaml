model_id: "SYS_DistributedStorage"
model_name: "集中式存储"
description: "集中式存储模型同步配置"
resource_type: "physical"  # 资源类型：physical（物理类资源）或 cloud（云资源）
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: false        # 是否启用该模型数据加载
sync_enabled: false   # 是否启用该模型同步

# 云内侧配置
cloud_side:
  primary_model: "SYS_DistributedStorage"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  match_key: "sn"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"
  # 关联模型配置
  related_models:
    - model: "M_StorDevConsistsOfStorPool"
      relation_type: "one_to_many"
      relation_method: "relation_table"
      relation_config:
        endpoint: "cloud_relation_api"
        params:
          condition:
            {
              "constraint": [
                {
                  "simple": {
                    "name": "source_Instance_Id",
                    "value": "{id}",
                    "operator": "equal"
                  }
                }
              ]
            }
        source_key: "source_Instance_Id"
        target_key: "target_Instance_Id"
        source_field: "id"  # 源实体中用于关联的字段，默认为主键
      target_config:
        endpoint: "cloud_cmdb_model"
        model: "SYS_StoragePool"
        params:
          condition:
            {
              "constraint": [
                {
                  "simple": {
                    "name": "id",
                    "value": "{target_ids}",
                    "operator": "in"
                  }
                }
              ]
            }
        id_key: "id"

# 行内侧配置
internal_side:
  model: "DistributedStorage"  # 未提供行内模型名称，暂定和云内名称一致
  primary_key: "SerialNumber"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  get_params:
    className: "DistributedStorage"
    itemNames: ["SerialNumber","Code","Description", "Model", "HealthStatus", "Notes", "Brand", "IpAddress","StoragePoolId",
                "Version", "StorageAddress", "InstanceId", "CIType", "LogicalRegionId", "LogicalRegionName"]
    attribute: []
    isRelatedCIId: true
  api_config:
    result_path: "data"
    pagination:
      page_field: "pageNum"
      size_field: "pageSize"
    update_params:
      className: "DistributedStorage"
      api_config:
        request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
        nested_config:
          root_key: "CIs"
          class_key: "DistributedStorage"  # 对应 Class_Name
          constant_key: "ClassKey"  # 主键字段名
          constant_value: "SerialNumber"  # 主键字段值
    delete_params:
      className: "DistributedStorage"
      api_config:
        request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
        record_config:
          root_key: "record"  # 记录列表的根键
          id_field: "recordId"  # 记录ID字段
          table_field: "tableName"  # 表名字段
    field_wrapper:
      enabled: true
      foreign_key_wrapper: ""  # 外键字段的包装器名称

# 字段映射配置 云内
field_mappings:
  # 基本信息映射
  - internal_field: "Code"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "concat_fields"
    transform_params:
      fields: [ "sn" ]
      constant: "DistributedStorage"
      separator: "_"  # 使用下划线为分隔符
      skip_empty: true  # 跳过空值
    required: true

  - internal_field: "SerialNumber"  # 匹配符
    cloud_field: "sn"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Model"
    cloud_field: "productName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "IpAddress"
    cloud_field: "ipAddress"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "HealthStatus"
    cloud_field: "status"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "StorageAddress"
    cloud_field: "nativeId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Version"
    cloud_field: "softwareVersion"
    sync_direction: "cloud_to_internal"
    required: true

#  - internal_field: "StoragePoolId"
#    cloud_field: "custom"  # 云内没有所属存储池字段 外键
#    sync_direction: "cloud_to_internal"
#    required: true

  # 行内新增属性
  - internal_field: "InstanceId"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionId"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionName"
    cloud_field: "logicalRegionName"
    sync_direction: "cloud_to_internal"
    required: true
