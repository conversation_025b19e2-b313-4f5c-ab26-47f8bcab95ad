model_id: "SYS_LUN"
model_name: "存储LUN"
description: "存储LUN模型同步配置"
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: true        # 是否启用该模型数据加载
sync_enabled: true   # 是否启用该模型同步

# 云内侧配置
cloud_side:
  primary_model: "SYS_LUN"
  primary_endpoint: "cloud_cmdb_model"
  update_endpoint: "update_ci"
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  primary_key: "id"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"
  # 关联模型配置
  related_models:
    - model: "M_StorDevConsistsOfLun"
      relation_type: "one_to_one"
      relation_method: "relation_table"
      relation_config:
        endpoint: "cloud_relation_api"
        params:
          condition:
            {
              "constraint": [
                {
                  "simple": {
                    "name": "target_Instance_Id",
                    "value": "{id}",
                    "operator": "in"
                  }
                }
              ]
            }
        source_key: "target_Instance_Id"
        target_key: "source_Instance_Id"
        source_field: "id"  # 源实体中用于关联的字段，默认为主键
      target_config:
        endpoint: "cloud_cmdb_model"
        model: "SYS_StorDevice"
        params: &filter_by_ids
          condition:
            {
              "constraint": [
                {
                  "simple": {
                    "name": "id",
                    "value": "{target_ids}",
                    "operator": "in"
                  }
                }
              ]
            }
        id_key: "id"

# 行内侧配置
internal_side:
  model: "StorageLun"
  primary_key: "InstanceId"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  get_params:
    className: "StorageLun"
    itemNames: [ "Description", "Code", "Notes", "StorageLunType", "LunSize", "StoragePool", "DStorageDevice",
                 "LunID", "HostWWPN", "InstanceId", "CIType", "LogicalRegionId", "LogicalRegionName" ]
    attribute: [ ]
    isRelatedCIId: true
  update_params:
    className: "StorageLun"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "StorageLun"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "InstanceId"  # 主键字段值
  delete_params:
    className: "StorageLun"
    api_config:
      request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
      record_config:
        root_key: "record"  # 记录列表的根键
        id_field: "recordId"  # 记录ID字段
        table_field: "tableName"  # 表名字段
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称

# 字段映射配置 云内
field_mappings:
  # 基本信息映射
  - internal_field: "Description"
    cloud_field: "name"
    sync_direction: "cloud_to_internal"
    is_check: true
    required: true

  - internal_field: "Code"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "concat_fields"
    transform_params:
      fields: [ "name" ]
      index: 0
      constant: "StorageLun"
      separator: "_"  # 使用下划线为分隔符
      skip_empty: true  # 跳过空值
      ipv4_only: true
    required: true

  - internal_field: "Notes"
    cloud_field: "remark"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "StorageLunType"
    cloud_field: "lunType"
    sync_direction: "cloud_to_internal"
    transform: "lun_type_mapping"
    required: true

  - internal_field: "LunSize"  # GB
    cloud_field: "totalCapacity"     # GB
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "StoragePool"
    internal_reference_key: "Code"
    cloud_field: "custom"  # 先映射查询存储设备id,在拼接存储池id,拼接结果作为Code
    transform: "get_related_attribute_and_concat"
    transform_params:
      model: "M_StorDevConsistsOfLun"
      attribute: "id"
      format: "{value}"
      fields: [ "poolId" ]  # nativeId:NE=60000000+nxxxxxx
      separator: "+"
      skip_empty: true
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "DStorageDevice"
    cloud_field: "custom"
    internal_reference_key: "Code"
    sync_direction: "cloud_to_internal"
    transform: "get_related_attribute"
    transform_params:
      model: "M_StorDevConsistsOfLun"
      attribute: "id"
      format: "{value}"
    required: true

  - internal_field: "LunID"
    cloud_field: "lunId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "HostWWPN"
    cloud_field: "hostWWPN" # 云内缺少字段
    sync_direction: "cloud_to_internal"
    required: true

  # 行内新增属性
  - internal_field: "InstanceId"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionId"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionName"
    internal_reference_key: "Code"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

# 模型特有的转换函数
transformers:
  lun_type_mapping: #normal=普通LUN, private=私有LUN, compact=精简LUN, extended=扩展LUN
    type: "mapping"
    mapping:
      "normal": "普通"  #行内不带Lun字符
      "private": "私有"
      "compact": "精简"
      "extended": "扩展"

check_templates:
  - template: conditional_required
    params:
      condition_field: "status"
      condition_value: "normal"
      required_field: "name"
      message: "设备状态是运行中或者测试中，配置项名称必填"