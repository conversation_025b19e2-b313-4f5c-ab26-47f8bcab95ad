model_id: "SYS_PhysicalServer"
model_name: "PC物理主机"
description: "PC物理主机模型同步配置"
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: false        # 是否启用该模型数据加载
sync_enabled: false   # 是否启用该模型同步

# 云内侧配置
cloud_side:
  primary_model: "SYS_PhysicalServer"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"
  # 关联模型配置
  related_models:
    - model: "M_PhyServConsistsOfDisk"
      relation_type: "one_to_many"
      relation_method: "relation_table"
      relation_config:
        endpoint: "cloud_relation_api"
        params: &filter_by_source_id
          condition:
            {
              "constraint": [
                {
                  "simple": {
                    "name": "source_Instance_Id",
                    "value": "{id}",
                    "operator": "equal"
                  }
                }
              ]
            }
        source_key: "source_Instance_Id"
        target_key: "target_Instance_Id"
        source_field: "id"  # 源实体中用于关联的字段，默认为主键
      target_config:
        endpoint: "cloud_cmdb_model"
        model: "SYS_Disk"
        params: &filter_by_ids
          condition:
            {
              "constraint": [
                {
                  "simple": {
                    "name": "id",
                    "value": "{target_ids}",
                    "operator": "in"
                  }
                }
              ]
            }
        id_key: "id"
    - model: "M_PhyServConsistsOfCPU"
      relation_type: "one_to_many"
      relation_method: "relation_table"
      relation_config:
        endpoint: "cloud_relation_api"
        params: *filter_by_source_id
        source_key: "source_Instance_Id"
        target_key: "target_Instance_Id"
        source_field: "id"
      target_config:
        endpoint: "cloud_cmdb_model"
        model: "SYS_CPU"
        params: *filter_by_ids
        id_key: "id"

# 行内侧配置
internal_side:
  model: "PCHost"
  primary_key: "InstanceId"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  get_params:
    className: "PCHost"
    itemNames: ["Description", "Code", "Notes", "HostGroup", "HostRunType", "PrdIPAddr", "OS", "DiskCapacity", "TotalCPUs",
                "CPUCoreNumber", "CPUFrequency", "Memory", "PCServer", "AdminTeam", "IPInfo", "InstanceId", "CIType",
                "logicalRegionId", "logicalRegionName", "AppSysNo", "ApplicationSystem"]
    attribute: []
    isRelatedCIId: true
  update_params:
    className: "PCHost"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "PCHost"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "InstanceId"  # 主键字段值
  delete_params:
    className: "PCHost"
    api_config:
      request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
      record_config:
        root_key: "record"  # 记录列表的根键
        id_field: "recordId"  # 记录ID字段
        table_field: "tableName"  # 表名字段
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称

# 字段映射配置 云内
field_mappings:
  # 基本信息映射
  - internal_field: "Description"
    cloud_field: "name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Code"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "concat_fields"
    transform_params:
      fields: [ "ipAddress" ]
      constant: "PCHost"
      separator: "_"  # 使用下划线为分隔符
      skip_empty: true  # 跳过空值
    required: true

  - internal_field: "Notes"
    cloud_field: "remark"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "HostGroup"  #待确认字段
    cloud_field: "remark"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "HostRunType"
    cloud_field: "status"
    sync_direction: "cloud_to_internal"
#    transform: "host_run_type_mapping"  # 行内只要开关机状态
    required: true

  - internal_field: "PrdIPAddr"
    cloud_field: "ipAddress"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "OS"
    cloud_field: "ownerType"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "PCServer"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "AdminTeam"  #待定
    cloud_field: "ipAddress"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "IPInfo"
    cloud_field: "ipAddress"
    sync_direction: "cloud_to_internal"
    required: true

  # 内存相关字段映射
  - internal_field: "DiskCapacity"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "calculate_related_sum"
    transform_params:
      model: "M_PhyServConsistsOfDisk"
      attribute: "capacity"
      convert_unit: true
      format: "{value:.2f}"
    required: true

  - internal_field: "Memory"
    cloud_field: "memoryCapacity"
    transform: "convert_capacity"
    transform_params:
      field: "memoryCapacity"
      from_unit: "MB"      # 云内单位
      to_unit: "GB"       # 行内单位
      format: "{value:.2f}"  # 保留两位小数
    sync_direction: "cloud_to_internal"
    required: true

  # cpu相关
  - internal_field: "TotalCPUs"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "calculate_related_count"
    transform_params:
      model: "M_PhyServConsistsOfCPU"
      format: "{value}"
    required: true

  - internal_field: "CPUCoreNumber"
    cloud_field: "cpuCores"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CpuFrequency"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "calculate_related_sum"
    transform_params:
      model: "M_PhyServConsistsOfCPU"
      attribute: "frequency"
      format: "{value:.2f}"
    required: true

  # 行内新增属性
  - internal_field: "InstanceId"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "logicalRegionId"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionName"
    cloud_field: "logicalRegionName"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "AppSysNo"
    cloud_field: "epsId"
    sync_direction: "cloud_to_internal"
    transform: "get_from_application_code"
    required: true

  - internal_field: "ApplicationSystem"
#    internal_reference_key: "SysNo"
#    cloud_field: "epsId"
#    transform: "get_from_application_code"
#    sync_direction: "cloud_to_internal"
    internal_reference_key: "Description"
    cloud_field: "applicationSystem"
    transform: "ApplicationSystem_constant"
    sync_direction: "cloud_to_internal"
    required: true


# 模型特有的转换函数
transformers:
  # 模型特有的转换函数
  vs_platform_constant:
    type: "constant"
    value: "FusionSphere"
  # 云内 normal=正常, offline=离线, abnormal=故障, unknown=未知
  # 行内 运行中、已关机、已挂起、已失效、未知
  host_run_type_mapping:
    type: "mapping"
    mapping:
      "normal": "运行中"
      "offline": "已关机"
      "abnormal": "已失效"  #待确认是否合理，行内字段 已失效是否等同于故障
      "default": "未知"

check_templates:
  # 条件复用组
  - template: condition_group
    id: "normal_status"
    params:
      condition_field: "status"
      condition_value: "normal"  # 云内设备状态没有测试中状态

  - template: conditional_required
    params:
      condition_ref: "normal_status"
      required_field: "remark"  # 所属主机组云内值来源不确定
      message: "设备状态是运行中或运行中（测试环境），所属主机组必填"

  - template: conditional_required
    params:
      condition_ref: "normal_status"
      required_field: "ipAddress"
      message: "设备状态是运行中或运行中（测试环境），生产IP必填"

  - template: conditional_required
    params:
      condition_ref: "normal_status"
      required_field: "applicationSystem"
      message: "运行状态是运行中或运行中（测试环境），关联应用系统必填"

  - template: conditional_required
    params:
      condition_ref: "normal_status"
      required_field: "osType"
      message: "设备状态是运行中或运行中（测试环境），操作系统必填"