model_id: "SYS_Rack_ShieldedCabinet"
model_name: "屏蔽机柜"
description: "屏蔽机柜模型同步配置"
resource_type: "physical"  # 资源类型：physical（物理类资源）或 cloud（云资源）
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: false        # 是否启用该模型数据加载
sync_enabled: false   # 是否启用该模型同步

# 云内侧配置
cloud_side:
  primary_model: "SYS_Rack"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  match_key: "number"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"

# 行内侧配置
internal_side:
  model: "ShieldedCabinet"
  primary_key: "SerialNumber"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  get_params:
    className: "ShieldedCabinet"
    itemNames: [ "Code", "Description", "Notes", "InstanceId", "CIType", "SerialNumber", "DeviceName","Room","MaximumVolume","CIStatus",
                 "AssetCode","MaintenanceStartDate","EquipManufacturer","AdministratorB","MaintenanceEndDate" ]
    attribute: [ ]
    isRelatedCIId: true
  update_params:
    className: "ShieldedCabinet"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "ShieldedCabinet"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "InstanceId"  # 主键字段值
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称

# 字段映射配置
field_mappings:
  # 字段映射配置 行内
  - internal_field: "Code"
    cloud_field: "id"
    sync_direction: "internal_to_cloud"
    required: true

  - internal_field: "Description"
    cloud_field: "name"
    sync_direction: "internal_to_cloud"
    required: true

  - internal_field: "Notes"
    cloud_field: "remark"
    sync_direction: "internal_to_cloud"
    required: true

  - internal_field: "InstanceId"
    cloud_field: "id"
    sync_direction: "internal_to_cloud"
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "internal_to_cloud"
    required: true

  - internal_field: "SerialNumber"
    cloud_field: "number"
    sync_direction: "internal_to_cloud"
    required: true

  - internal_field: "DeviceName"
    cloud_field: "name"
    sync_direction: "internal_to_cloud"
    required: true

  - internal_field: "Room"
    cloud_field: "roomId"
    sync_direction: "internal_to_cloud"
    required: true

  - internal_field: "MaximumVolume"
    cloud_field: "capacity"
    sync_direction: "internal_to_cloud"
    required: true

  - internal_field: "CIStatus"
    cloud_field: "assetStatus"
#    transform: "asset_status_mapping"
    sync_direction: "internal_to_cloud"
    required: true

  - internal_field: "AssetCode"
    cloud_field: "assertNo"
    sync_direction: "internal_to_cloud"
    required: true

  - internal_field: "MaintenanceStartDate"
    cloud_field: "warrantyStartTime"
    sync_direction: "internal_to_cloud"
    required: true

  - internal_field: "AdministratorB"
    cloud_field: "incharger"
    sync_direction: "internal_to_cloud"
    required: true

  - internal_field: "EquipManufacturer"
    cloud_field: "equipManufacturer"
    sync_direction: "internal_to_cloud"
    required: true

  - internal_field: "MaintenanceEndDate"
    cloud_field: "warrantyEndTime"
    sync_direction: "internal_to_cloud"
    required: true

transformers:
  asset_status_mapping: # 投产，维护，改造，未启用，报废  缺少对应的英文字段解释
    type: "mapping"
    mapping:
      "running": "运行"
      "unused": "闲置"
      "used": "已使用"
      "stock": "库存"
      "retirement": "报废"
      "maintenance": "维护"
      "occupied": "已分配"
