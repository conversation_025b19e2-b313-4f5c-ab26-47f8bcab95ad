model_id: "SYS_Room"
model_name: "机柜"
description: "机柜模型同步配置"
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: false        # 是否启用该模型数据加载
sync_enabled: false   # 是否启用该模型同步

# 云内侧配置
cloud_side:
  primary_model: "SYS_Room"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"

# 行内侧配置
internal_side:
  model: "Room"
  primary_key: "InstanceId"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  get_params:
    className: "Room"
    itemNames: ["Code", "Description", "Notes", "InstanceId", "CIType", "Floor"]
    attribute: []
    isRelatedCIId: true
  update_params:
    className: "Room"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "Room"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "InstanceId"  # 主键字段值
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称

# 字段映射配置
field_mappings:
    # 字段映射配置 行内
  - internal_field: "Code"
    cloud_field: "id"
    sync_direction: "internal_to_cloud"
    required: true

  - internal_field: "Description"
    cloud_field: "name"
    sync_direction: "internal_to_cloud"
    required: true

  - internal_field: "Notes"
    cloud_field: "remark"
    sync_direction: "internal_to_cloud"
    required: true

  - internal_field: "InstanceId"
    cloud_field: "id"
    sync_direction: "internal_to_cloud"
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "internal_to_cloud"
    required: true

  - internal_field: "Floor"
    cloud_field: "dcId"
    sync_direction: "internal_to_cloud"
    required: true
