model_id: "SYS_StorageCluster"
model_name: "存储集群"
description: "存储集群模型同步配置"
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: true        # 是否启用该模型数据加载
sync_enabled: true   # 是否启用该模型同步

# 云内侧配置
cloud_side:
  primary_model: "SYS_StorageCluster"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"

# 行内侧配置
internal_side:
  model: "StorageCluster"
  primary_key: "InstanceId"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  get_params:
    className: "StorageCluster"
    itemNames: ["Code", "CIType", "Description", "Notes", "TotalCapacity", "UsedCapacity", "NodeNum", "HealthStatus",
                "RunningStatus", "Version", "InstanceId", "LogicalRegionId", "LogicalRegionName"]
    attribute: []
    isRelatedCIId: true
  update_params:
    className: "StorageCluster"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "StorageCluster"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "InstanceId"  # 主键字段值
  delete_params:
    className: "StorageCluster"
    api_config:
      request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
      record_config:
        root_key: "record"  # 记录列表的根键
        id_field: "recordId"  # 记录ID字段
        table_field: "tableName"  # 表名字段
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称

# 字段映射配置 云内
field_mappings:
  # 基本信息映射
  - internal_field: "Description"
    cloud_field: "name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Code"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
#    transform: "concat_fields"
#    transform_params:
#      fields: ["nativeId"]  #集群存储没有序列号字段，nativeId
#      constant: "StorageCluster"
#      separator: "_"  # 使用下划线为分隔符
#      skip_empty: true  # 跳过空值
    required: true

  - internal_field: "Notes"
    cloud_field: "remark"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TotalCapacity"  # GB
    cloud_field: "totalCapacity"     # MB
    transform: "convert_capacity"
    transform_params:
      field: "totalCapacity"  # 从这个字段获取值
      from_unit: "MB"      # 云内单位
      to_unit: "GB"       # 行内单位
      format: "{value:.2f}"  # 保留两位小数
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "UsedCapacity"  # GB
    cloud_field: "usedCapacity"     # GB
    transform: "convert_capacity"
    transform_params:
      field: "usedCapacity"
      from_unit: "MB"
      to_unit: "GB"
      format: "{value:.2f}"  # 保留两位小数
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "NodeNum"
    cloud_field: "nodeNum"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "HealthStatus"
    cloud_field: "healthStatus"
    sync_direction: "cloud_to_internal"
    transform: "health_status_mapping"
    required: true

  - internal_field: "RunningStatus"
    cloud_field: "runningStatus"
    sync_direction: "cloud_to_internal"
#    transform: "running_status_mapping"
    required: true

  - internal_field: "Version"
    cloud_field: "productName"
    sync_direction: "cloud_to_internal"
    required: true

  # 行内新增属性
  - internal_field: "InstanceId"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionId"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionName"
    internal_reference_key: "Code"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true
# 模型特有的转换函数
transformers:
  running_status_mapping: #offline=离线, online=在线, fault=故障, unknown=未知  无行内映射信息
    type: "mapping"
    mapping:
      "normal": "健康"
      "offline": "离线"
      "fault": "故障"
      "unknown": "故障"