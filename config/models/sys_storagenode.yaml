model_id: "SYS_StorageNode"
model_name: "分布式存储"
description: "分布式存储模型同步配置"
resource_type: "physical"  # 资源类型：physical（物理类资源）或 cloud（云资源）
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: true        # 是否启用该模型数据加载
sync_enabled: true   # 是否启用该模型同步

# 云内侧配置
cloud_side:
  primary_model: "SYS_StorageNode"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"

# 行内侧配置
internal_side:
  model: "DistributedStorage"
  primary_key: "InstanceId"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  get_params:
    className: "DistributedStorage"
    itemNames: [ "Code", "CIType", "Description", "Notes", "Brand", "Model", "IpAddress", "HealthStatus", "Cabinet", "SlotNumber",
                "WarrantyStartTime", "WarrantyEndTime", "AssertNo", "storageAddress", "Version", "StoragePoolId",
                "InstanceId", "LogicalRegionId", "LogicalRegionName"]
    attribute: []
    isRelatedCIId: true
    api_config:
      result_path: "data"
      pagination:
        page_field: "pageNum"
        size_field: "pageSize"
  update_params:
    className: "DistributedStorage"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "DistributedStorage"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "InstanceId"  # 主键字段值
  delete_params:
    className: "DistributedStorage"
    api_config:
      request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
      record_config:
        root_key: "record"  # 记录列表的根键
        id_field: "recordId"  # 记录ID字段
        table_field: "tableName"  # 表名字段
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称

# 字段映射配置 云内
field_mappings:
  # 基本信息映射
  - internal_field: "Code"
#    cloud_field: "storageDeviceId"  # 作为存储池的外键存在，这里需要时存储设备ID  方案不成立 storagedeviceid重复
#    sync_direction: "cloud_to_internal"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "concat_fields"
    transform_params:
      fields: [ "nodeSn" ]
      constant: "DistributedStorage"
      separator: "_"  # 使用下划线为分隔符
      skip_empty: true  # 跳过空值
    required: true

  - internal_field: "Notes"
    cloud_field: "remark"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Description"
    cloud_field: "name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Brand"
    cloud_field: "manufacturer"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Model"
    cloud_field: "model"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "IpAddress"
    cloud_field: "ipAddress"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "HealthStatus"
    cloud_field: "healthStatus"
    sync_direction: "cloud_to_internal"
#    transform: "health_status_mapping"
    required: true

  - internal_field: "LogicalRegionId"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "StorageAddress"
    cloud_field: "bussIP"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Version"
    cloud_field: "productName"    # 未提供映射集 字段准确性不确定
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "StoragePoolId"
    cloud_field: "storagePoolId"    # 云内字段来源不确定 未提供映射集 字段准确性不确定
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "InstanceId"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionName"
    internal_reference_key: "Code"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

#  # 字段映射配置 行内
#  - internal_field: "Cabinet"
#    cloud_field: "cabinet"
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "SlotNumber"
#    cloud_field: "slotNumber"
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "MaintenanceStartDate"
#    cloud_field: "warrantyStartTime"
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "MaintenanceEndDate" # 行内云内数据类型都是date,格式是否需要转换待定
#    cloud_field: "warrantyEndTime"
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "AssetCode"
#    cloud_field: "assertNo"
#    sync_direction: "internal_to_cloud"
#    required: true

