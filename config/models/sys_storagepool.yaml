model_id: "SYS_StoragePool"
model_name: "存储池"
description: "存储池模型同步配置"
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: true        # 是否启用该模型数据加载
sync_enabled: true   # 是否启用该模型同步

# 云内侧配置
cloud_side:
  primary_model: "SYS_StoragePool"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"
  # 关联模型配置
#  related_models:
#    - model: "M_StorDevConsistsOfStorPool"
#      relation_type: "one_to_one"
#      relation_method: "relation_table"
#      relation_config:
#        endpoint: "cloud_relation_api"
#        params:
#          condition:
#            {
#              "constraint": [
#                {
#                  "simple": {
#                    "name": "target_Instance_Id",
#                    "value": "{id}",
#                    "operator": "equal"
#                  }
#                }
#              ]
#            }
#        source_key: "target_Instance_Id"
#        target_key: "source_Instance_Id"
#        source_field: "id"  # 源实体中用于关联的字段，默认为主键
#      target_config:
#        endpoint: "cloud_cmdb_model"
#        model: "SYS_StorDevice"
#        params: &filter_by_ids
#          condition:
#            {
#              "constraint": [
#                {
#                  "simple": {
#                    "name": "id",
#                    "value": "{target_ids}",
#                    "operator": "in"
#                  }
#                }
#              ]
#            }
#        id_key: "id"

# 行内侧配置
internal_side:
  model: "StoragePool"
  primary_key: "InstanceId"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  get_params:
    className: "StoragePool"
    itemNames: ["Description", "Code", "Notes", "PoolType", "TotalCapacity", "AvaliableCapacity", "DSStorageDevicePool",
                "DiskType", "PoolID", "AllocatedCapacity", "InstanceId", "CIType", "LogicalRegionId", "LogicalRegionName"]
    attribute: []
    isRelatedCIId: true
  update_params:
    className: "StoragePool"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "StoragePool"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "InstanceId"  # 主键字段值
  delete_params:
    className: "StoragePool"
    api_config:
      request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
      record_config:
        root_key: "record"  # 记录列表的根键
        id_field: "recordId"  # 记录ID字段
        table_field: "tableName"  # 表名字段
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称

# 字段映射配置 云内
field_mappings:
  # 基本信息映射
  - internal_field: "Description"
    cloud_field: "name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Code"
    cloud_field: "custom"
    transform: "concat_fields"
    transform_params:
      fields: [ "storageDeviceId", "nativeId", "poolId" ]  # nativeId:NE=60000000+nxxxxxx
      separator: "+"
      skip_empty: true
      sub_split:
        fields: ["nativeId"]
        index: 0
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Notes"
    cloud_field: "remark"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "PoolType"
    cloud_field: "poolType"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TotalCapacity"  # GB
    cloud_field: "totalCapacity"     # GB
    transform: "convert_capacity"
    transform_params:
      field: "totalCapacity"  # 从这个字段获取值
      from_unit: "MB"      # 云内单位
      to_unit: "GB"       # 行内单位
      format: "{value:.2f}"  # 保留两位小数
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "AvaliableCapacity"   # GB
    cloud_field: "custom"                 # GB
    transform: "calculate_expression"
    transform_params:
      expression: "totalCapacity - usedCapacity"
      fields:
        - name: "totalCapacity"
        - name: "usedCapacity"
      convert_unit: true
      from_unit: "MB"
      to_unit: "GB"
      format: "{value:.2f}"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "DSStorageDevicePool"  # 类型改成字符串，因为1:N StorageDeviceNode
    cloud_field: "storageDeviceId"
#    internal_reference_key: "Code"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "DiskType"
    cloud_field: "mediaType"
    sync_direction: "cloud_to_internal"
    transform: "media_type_mapping"
    required: true

  - internal_field: "PoolID"
    cloud_field: "poolId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "AllocatedCapacity"
    cloud_field: "usedCapacity"
    transform: "convert_capacity"
    transform_params:
      field: "usedCapacity"
      from_unit: "MB"      # 云内单位
      to_unit: "GB"        # 行内单位
      format: "{value:.2f}"
    sync_direction: "cloud_to_internal"
    required: true

  # 行内新增属性
  - internal_field: "InstanceId"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionId"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionName"
    internal_reference_key: "Code"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "AdministratorA"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "AdministratorA_constant"
    required: true

  - internal_field: "AdministratorB"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "AdministratorB_constant"
    required: true

# 模型特有的转换函数
transformers:
  media_type_mapping:
    type: "mapping"
    mapping:
      "ssd": "01"
      "hdd": "00"
      "hhd": "02"
      "default": "99"