model_id: "SYS_StorDevice"
model_name: "集中式存储"
description: "集中式存储模型同步配置"
resource_type: "physical"  # 资源类型：physical（物理类资源）或 cloud（云资源）
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: false        # 是否启用该模型数据加载
sync_enabled: false   # 是否启用该模型同步

# 云内侧配置
cloud_side:
  primary_model: "SYS_StorDevice"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  match_key: "sn"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"
  # 关联模型配置
  related_models:
    - model: "M_StorDevConsistsOfStorDisk"
      relation_type: "one_to_many"
      relation_method: "relation_table"
      relation_config:
        endpoint: "cloud_relation_api"
        params: &filter_by_source_id
          condition:
            {
              "constraint": [
                {
                  "simple": {
                    "name": "source_Instance_Id",
                    "value": "{id}",
                    "operator": "equal"
                  }
                }
              ]
            }
        source_key: "source_Instance_Id"
        target_key: "target_Instance_Id"
        source_field: "id"  # 源实体中用于关联的字段，默认为主键
      target_config:
        endpoint: "cloud_cmdb_model"
        model: "SYS_Disk"
        params: &filter_by_ids
          condition:
            {
              "constraint": [
                {
                  "simple": {
                    "name": "id",
                    "value": "{target_ids}",
                    "operator": "in"
                  }
                }
              ]
            }
        id_key: "id"
    - model: "M_StorDevConsistsOfLun"
      relation_type: "one_to_many"
      relation_method: "relation_table"
      relation_config:
        endpoint: "cloud_relation_api"
        params: *filter_by_source_id
        source_key: "source_Instance_Id"
        target_key: "target_Instance_Id"
        source_field: "id"  
      target_config:
        endpoint: "cloud_cmdb_model"
        model: "SYS_Lun"
        params: *filter_by_ids
        id_key: "id"
    - model: "M_RackContainsDevice"
      relation_type: "one_to_many"
      relation_method: "relation_table"
      relation_config:
        endpoint: "cloud_relation_api"
        params:
          condition:
            {
              "constraint": [
                {
                  "simple": {
                    "name": "target_Instance_Id",
                    "value": "{id}",
                    "operator": "in"
                  }
                }
              ]
            }
        source_key: "target_Instance_Id" # 这里的源应该是 设备的ID  在关系表里 设备ID作为目标ID
        target_key: "source_Instance_Id" # 目标应该是 机柜的ID 在关系表里 设备ID作为ID
        source_field: "id"  # 源实体中用于关联的字段，默认为主键
      # 这里的目标实体为机柜信息
      target_config:
        endpoint: "cloud_cmdb_model"
        model: SYS_Rack
        params: *filter_by_ids
        id_key: "id"


# 行内侧配置
internal_side:
  model: "SYS_StorDevice"  # 未提供行内模型名称，暂定和云内名称一致
  primary_key: "SerialNumber"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  get_params:
    className: "SYS_StorDevice"
    itemNames: ["SerialNumber","Code","Description", "Model", "DeviceStatus", "ArrivalDate", "Rack", "RackBeginU", "RackEndU", "AdminTeam",
                "PurchaseContract", "ManageIPAddr", "TotalCapacity", "DiskType", "MicroCodeVersion", "ApplicationSystem",
                "AssetValue", "SupportIpv6", "NumberOfDisks", "EquipManufacturer", "MaintenanceEndDate", "AmountOfLun", "MaintenanceStartDate",
                "AdministratorA", "AdministratorB", "OgMaintenceStartDate", "OgMaintenceEndDate", "InstanceId", "CIType", "logicalRegionId",
                "logicalRegionName"]
    attribute: []
    isRelatedCIId: true
  api_config:
    result_path: "data"
    pagination:
      page_field: "pageNum"
      size_field: "pageSize"
    update_params:
      className: "SYS_StorDevice"
      api_config:
        request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
        nested_config:
          root_key: "CIs"
          class_key: "SYS_StorDevice"  # 对应 Class_Name
          constant_key: "ClassKey"  # 主键字段名
          constant_value: "SerialNumber"  # 主键字段值
    delete_params:
      className: "SYS_StorDevice"
      api_config:
        request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
        record_config:
          root_key: "record"  # 记录列表的根键
          id_field: "recordId"  # 记录ID字段
          table_field: "tableName"  # 表名字段
    field_wrapper:
      enabled: true
      foreign_key_wrapper: ""  # 外键字段的包装器名称

# 字段映射配置 云内
field_mappings:
  # 基本信息映射
  - internal_field: "SerialNumber"  # 匹配符
    cloud_field: "sn"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Model"
    cloud_field: "productName"
    sync_direction: "cloud_to_internal"
    is_check: true
    required: true

  - internal_field: "ManageIPAddr"
    cloud_field: "ipAddress"
    sync_direction: "cloud_to_internal"
    is_check: true
    required: true

  - internal_field: "TotalCapacity"  # GB
    cloud_field: "totalCapacity"  # GB
    sync_direction: "cloud_to_internal"
    is_check: true
    required: true

  - internal_field: "DiskType"
    cloud_field: "custom"
    transform: "get_related_attribute_and_map"
    transform_params:
      model: "M_StorDevConsistsOfStorDisk"
      attribute: "physicalType"
      mapping_transformer: "physical_type_mapping"
      format: "{value}"
    sync_direction: "cloud_to_internal"
    is_check: true
    required: true

  - internal_field: "NumberOfDisks"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "calculate_related_count"
    transform_params:
      model: "M_StorDevConsistsOfStorDisk"
      format: "{value}"
    required: true

  - internal_field: "EquipManufacturer"
    cloud_field: "manufacturer"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "AmountOfLun"
    cloud_field: "custom"
    transform: "calculate_related_count"
    transform_params:
      model: "M_StorDevConsistsOfLun"
      format: "{value}"
    sync_direction: "cloud_to_internal"
    required: true

  # 行内新增属性
  - internal_field: "InstanceId"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "logicalRegionId"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "logicalRegionName"
    cloud_field: "logicalRegionName"
    sync_direction: "cloud_to_internal"
    required: true

#  # 字段映射配置 行内
#  - internal_field: "Description"
#    cloud_field: "name"
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "DeviceStatus"
#    cloud_field: "assetStatus"
#    sync_direction: "internal_to_cloud"
#    transform: "asset_status_mapping"
#    required: true
#
#  - internal_field: "ArrivalDate"
#    cloud_field: "arrivalDate"  # 云内无字段
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "Rack_RelatedId"   # Rack是REFERENCE字段,isRelatedCIId=true，结果响应：Rack_RelatedId
#    cloud_field: "rackId"
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "RackBeginU"
#    cloud_field: "position"
#    sync_direction: "internal_to_cloud"
#    transform: "compare_relation_field"
#    transform_params:
#      relation_model: "M_RackContainsDevice"
#      relation_key: "target_Instance_Id"
#      internal_field: "RackBeginU"
#      cloud_field: "position"
#    required: false
#
#  - internal_field: "RackEndU"
#    cloud_field: "occupyHeight"
#    sync_direction: "internal_to_cloud"
#    transform: "compare_relation_field"
#    transform_params:
#      relation_model: "M_RackContainsDevice"
#      relation_key: "target_Instance_Id"
#      internal_field: "RackEndU"
#      cloud_field: "occupyHeight"
#    required: false
#
#
#  - internal_field: "MaintenanceEndDate" # 行内云内数据类型都是date,格式是否需要转换待定
#    cloud_field: "warrantyEndTime"
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "MaintenanceStartDate"
#    cloud_field: "warrantyStartTime"
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "AdministratorA"
#    cloud_field: "incharger"
#    transform: "concat_fields"
#    transform_params:
#      fields: [ "AdministratorA", "AdministratorB" ]
#      separator: "\\"  # 使用分号作为分隔符
#      skip_empty: true  # 跳过空值
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "AdminTeam"
#    cloud_field: "adminTeam"
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "PurchaseContract"
#    cloud_field: "purchaseContract"    # 云内无字段
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "MicroCodeVersion"
#    cloud_field: "microCodeVersion"    # 云内无字段
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "ApplicationSystem"
#    cloud_field: "applicationSystem"    # 云内无字段
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "AssetValue"
#    cloud_field: "assetValue"    # 云内无字段
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "SupportIpv6"
#    cloud_field: "supportIpv6"    # 云内无字段
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "OgMaintenceStartDate"
#    cloud_field: "ogMaintenceStartDate"    # 云内无字段
#    sync_direction: "internal_to_cloud"
#    is_check: true
#    required: true
#
#  - internal_field: "OgMaintenceEndDate"
#    cloud_field: "ogMaintenceEndDate"    # 云内无字段
#    sync_direction: "internal_to_cloud"
#    required: true

# 模型特有的转换函数
#：——00：机械硬盘——01：固态硬盘——02：混合硬盘——03：磁带——04：光盘——99：其它
transformers:
  physical_type_mapping:  # 行内没给对应的映射关系，当前按照云内映射关系映射
    type: "mapping"
    mapping:
#      "sas-flash-vp": "SAS-FLASH-VP"
#      "sas": "SAS"
#      "lun": "LUN"
      "ssd": "01"
#      "nl-ssd": "NL-SSD"
#      "ssd_card": "SSD卡"
#      "nl-sas": "NL-SAS"
#      "sata": "SATA"
#      "unknown": "未知"
#      "ata": "ATA"
#      "scm": "SCM"
#      "fc": "FC"
#      "vmdisk": "VMDISK"
#      "flash": "FLASH"
      "default": "99"

  asset_status_mapping:  # running=运行, unused=闲置, used=已使用, stock=库存, retirement=报废, maintenance=维护, occupied=已分配
                         # 未到货、测试中、运行中、闲置、待报废、外借
    type: "mapping"
    mapping:
      "running": "运行中"
      "unused": "闲置"
#      "used": "已使用"
#      "stock": "库存"
#      "retirement": "报废"
#      "maintenance": "维护"
#      "occupied": "已分配"
      "default": "其他"

check_templates:
  # 条件复用组
  - template: condition_group
    id: "normal_status"
    params:
      condition_field: "status"
      condition_value: "normal"  # 云内设备状态没有测试中状态

  - template: conditional_required
    params:
      condition_ref: "normal_status"
      required_field: "productName"
      message: "设备状态是运行中或测试中，型号必填"

  - template: conditional_required
    params:
      condition_ref: "normal_status"
      required_field: "ipAddress"
      message: "设备状态是运行中或测试中，管理IP地址必填"

  - template: numeric_range
    params:
      field: "totalCapacity"
      max_value: 102400000
      message: "存储总容量必须小于102400000GB"

  - template: conditional_required
    params:
      condition_ref: "normal_status"
      required_field: "DiskType"
      message: "设备状态是运行中或测试中，硬盘类型必填"

  - template: conditional_required
    params:
      condition_ref: "normal_status"
      required_field: "ogMaintenceStartDate"
      message: "设备状态是运行中或测试中，原厂维保开始时间必填"

  - template: date_min_value
    params:
      field: "ogMaintenceStartDate"
      min_date: "2000-01-01"
      message: "原厂维保开始日期必须大于2000-01-01"