model_id: "SYS_X86Server"
model_name: "PC服务器裸设备"
description: "PC服务器裸设备模型同步配置"
resource_type: "physical"  # 资源类型：physical（物理类资源）或 cloud（云资源）
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: true        # 是否启用该模型数据加载
sync_enabled: true   # 是否启用该模型同步

# 云内侧配置
cloud_side:
  primary_model: "SYS_X86Server"
  primary_endpoint: "cloud_cmdb_model"
  primary_key: "id"
  match_key: "sn"
  api_config:
    result_path: "objList"
    pagination:
      total_pages_field: "totalPageNo"
      page_field: "pageNo"
      size_field: "pageSize"

  # 关联模型配置
  related_models:
    #    - model: "sys_rack"
    #      endpoint: "get_racks"
    #      join_key: "rackId"
    #      foreign_key: "id"
    #      relation_type: "one_to_many"
    #      relation_method: "direct"
    #通过关系表关联的模型
    - model: "M_PhyServConsistsOfRAM"
      relation_type: "one_to_many"
      relation_method: "relation_table"
      relation_config:
        endpoint: "cloud_relation_api"
        params: &filter_by_source_id
          condition:
            {
              "constraint": [
                {
                  "simple": {
                    "name": "source_Instance_Id",
                    "value": "{id}",
                    "operator": "in"
                  }
                }
              ]
            }
        source_key: "source_Instance_Id"
        target_key: "target_Instance_Id"
        source_field: "id"  # 源实体中用于关联的字段，默认为主键
      target_config:
        endpoint: "cloud_cmdb_model"
        model: "SYS_RAM"
        params: &filter_by_id
          condition:
            {
              "constraint": [
                {
                  "simple": {
                    "name": "id",
                    "value": "{target_ids}",
                    "operator": "in"
                  }
                }
              ]
            }
        id_key: "id"

    - model: "M_PhyServConsistsOfDisk"
      relation_type: "one_to_many"
      relation_method: "relation_table"
      relation_config:
        endpoint: "cloud_relation_api"
        params: *filter_by_source_id
        source_key: "source_Instance_Id"
        target_key: "target_Instance_Id"
        source_field: "id"  # 源实体中用于关联的字段，默认为主键
      target_config:
        endpoint: "cloud_cmdb_model"
        model: "SYS_Disk"
        params: *filter_by_id
        id_key: "id"
    - model: "M_PhyServConsistsOfPsu"
      relation_type: "one_to_many"
      relation_method: "relation_table"
      relation_config:
        endpoint: "cloud_relation_api"
        params: *filter_by_source_id
        source_field: "id"  # 源实体中用于关联的字段，默认为主键
      target_config:
        endpoint: "cloud_cmdb_model"
        model: "SYS_Psu"
        params: *filter_by_id
        id_key: "id"
    - model: "M_PhyServConsistsOfGPU"
      relation_type: "one_to_many"
      relation_method: "relation_table"
      relation_config:
        endpoint: "cloud_relation_api"
        params: *filter_by_source_id
        source_key: "source_Instance_Id"
        target_key: "target_Instance_Id"
        source_field: "id"  # 源实体中用于关联的字段，默认为主键
      target_config:
        endpoint: "cloud_cmdb_model"
        model: "SYS_GPU"
        params: *filter_by_id
        id_key: "id"

    - model: "M_PhyServConsistsOfCPU"
      relation_type: "one_to_many"
      relation_method: "relation_table"
      relation_config:
        endpoint: "cloud_relation_api"
        params: *filter_by_source_id
        source_key: "source_Instance_Id"
        target_key: "target_Instance_Id"
        source_field: "id"  # 源实体中用于关联的字段，默认为主键
      target_config:
        endpoint: "cloud_cmdb_model"
        model: "SYS_CPU"
        params: *filter_by_id
        id_key: "id"
    - model: "M_RackContainsDevice"
      relation_type: "one_to_many"
      relation_method: "relation_table"
      relation_config:
        endpoint: "cloud_relation_api"
        params:
          condition:
            {
              "constraint": [
                {
                  "simple": {
                    "name": "target_Instance_Id",
                    "value": "{id}",
                    "operator": "in"
                  }
                }
              ]
            }
        source_key: "target_Instance_Id" # 这里的源应该是 设备的ID  在关系表里 设备ID作为目标ID
        target_key: "source_Instance_Id" # 目标应该是 机柜的ID 在关系表里 设备ID作为ID
        source_field: "id"  # 源实体中用于关联的字段，默认为主键
      # 这里的目标实体为机柜信息
      target_config:
        endpoint: "cloud_cmdb_model"
        model: SYS_Rack
        params: *filter_by_id
        id_key: "id"

# 行内侧配置
internal_side:
  model: "PCServer"
  primary_key: "SerialNumber"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  delete_endpoint: "batch_delete_ci"  # 添加删除端点
  get_params:
    className: "PCServer"
    itemNames: [ "SerialNumber", "Code" ,"SysDevBrand", "Model", "DeviceStatus", "Rack", "RackBeginU", "RackEndU", "ApplicationSystem", "Power", "ManageIPAddr",
                 "AssetCode", "MemoryBrand", "CpuProductArch", "AdminTeam", "CPUBrand", "TotalNumberOfCpu", "TotalNumOfCpuNuclear", "Function", "AdministratorB",
                 "CpuFrequency", "MemoryCapacity", "LocalStorageBrand", "TotalLocalStorageCap", "MaintenanceEndDate", "MaintenanceStartDate", "Description",
                 "AdministratorA", "OgMaintenceEndDate" ,"GPUModel", "GPUCardNum", "CPUModel", "InstanceId", "CIType", "LogicalRegionId", "LogicalRegionName", "ArrivalDate",
                 "SupportIpv6", "PurchaseContract"]
    isRelatedCIId: true
    api_config:
      result_path: "data"
      pagination:
        page_field: "pageNum"
        size_field: "pageSize"
  update_params:
    className: "PCServer"
    api_config:
      request_format: "nested"  #设置为 "nested"，表示请求体是嵌套结构
      nested_config:
        root_key: "CIs"
        class_key: "PCServer"  # 对应 Class_Name
        constant_key: "ClassKey"  # 主键字段名
        constant_value: "SerialNumber"  # 主键字段值
  delete_params:
    className: "PCServer"
    api_config:
      request_format: "record_list"  # 设置为 "record_list"，表示请求体是记录列表结构
      record_config:
        root_key: "record"  # 记录列表的根键
        id_field: "recordId"  # 记录ID字段
        table_field: "tableName"  # 表名字段
  field_wrapper:
    enabled: true
    foreign_key_wrapper: ""  # 外键字段的包装器名称

# 字段映射配置 云内
field_mappings:
  # 基本信息映射
  - internal_field: "SerialNumber"  # 匹配符
    cloud_field: "sn"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "SysDevBrand"
    cloud_field: "manufacturer"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "Model"
    cloud_field: "productName"
    sync_direction: "cloud_to_internal"
    is_check: true
    required: true

  - internal_field: "DeviceStatus"
    cloud_field: "status"
    sync_direction: "cloud_to_internal"
    transform: 'x86service_status'
    required: true

  - internal_field: "Power"
    cloud_field: "ratedPower"
    sync_direction: "cloud_to_internal"
    transform: "get_related_attribute"
    transform_params:
      model: "M_PhyServConsistsOfPsu"
      attribute: "ratedPower"
      number: false
      format: "{value:.2f}"
    required: true

  # CPU相关字段映射
  - internal_field: "CPUBrand"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_related_attribute"
    transform_params:
      model: "M_PhyServConsistsOfCPU"
      attribute: "manufacturer"
      format: "{value}"
    is_check: true
    required: true

  - internal_field: "CPUModel"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_related_attribute"
    transform_params:
      model: "M_PhyServConsistsOfCPU"
      attribute: "productName"
      format: "{value}"
    required: true

  - internal_field: "CpuFrequency"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_related_attribute"
    transform_params:
      model: "M_PhyServConsistsOfCPU"
      attribute: "frequency"
      number: false
      format: "{value}"
    required: true

  - internal_field: "CpuProductArch"
    cloud_field: "cpuArch"
    sync_direction: "cloud_to_internal"
    transform: "cpu_arch"
    transform_params:
      field: "cpuArch"
    required: true

  - internal_field: "TotalNumOfCpuNuclear"
    cloud_field: "cpuCores"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "TotalNumberOfCpu"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "calculate_related_count"
    transform_params:
      model: "M_PhyServConsistsOfCPU"
      format: "{value}"
    required: true

  # 内存相关字段映射
  - internal_field: "MemoryBrand"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_related_attribute"
    transform_params:
      model: "M_PhyServConsistsOfRAM"  # 关联名称
      attribute: "manufacturer"  # 目标属性名称
      format: "{value}" # 格式化字符串
    required: true

  - internal_field: "MemoryCapacity"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "calculate_related_sum"
    transform_params:
      model: "M_PhyServConsistsOfRAM"
      attribute: "capacity"
      convert_unit: true
      format: "{value:.2f}"  # format: "{value:.2f} GB" 格式化字符串（可选）
    required: true

  - internal_field: "LocalStorageBrand"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_related_attribute"
    transform_params:
      model: "M_PhyServConsistsOfDisk"
      attribute: "manufacturer"
      format: "{value:.2f}"
    required: true

  - internal_field: "TotalLocalStorageCap"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "calculate_related_sum"
    transform_params:
      model: "M_PhyServConsistsOfDisk"
      attribute: "capacity"
      convert_unit: true
      format: "{value:.2f}"
    required: true

  # GPU相关字段映射
  - internal_field: "GPUModel"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_related_attribute"
    transform_params:
      model: "M_PhyServConsistsOfGPU"
      attribute: "productName"
      format: "{value}"
    is_check: true
    required: true

  - internal_field: "GPUCardNum"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "calculate_related_count"
    transform_params:
      model: "M_PhyServConsistsOfGPU"
      format: "{value}"
    required: true

  # 其他基本字段映射
  - internal_field: "InstanceId"
    cloud_field: "id"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "CIType"
    cloud_field: "class_Name"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionId"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

  - internal_field: "LogicalRegionName"
    internal_reference_key: "Code"
    cloud_field: "logicalRegionId"
    sync_direction: "cloud_to_internal"
    required: true

#  # 字段映射配置 行内
#  - internal_field: "Rack_RelatedId"   # Rack是REFERENCE字段,isRelatedCIId=true，结果响应：Rack_RelatedId
#    cloud_field: "rackId"
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "RackBeginU"
#    cloud_field: "position"
#    sync_direction: "internal_to_cloud"
#    transform: "compare_relation_field"
#    transform_params:
#      relation_model: "M_RackContainsDevice"
#      relation_key: "target_Instance_Id"
#      internal_field: "RackBeginU"
#      cloud_field: "position"
#    required: false
#
#  - internal_field: "RackEndU"
#    cloud_field: "occupyHeight"
#    sync_direction: "internal_to_cloud"
#    transform: "compare_relation_field"
#    transform_params:
#      relation_model: "M_RackContainsDevice"
#      relation_key: "target_Instance_Id"
#      internal_field: "RackEndU"
#      cloud_field: "occupyHeight"
#    required: false
#
#  - internal_field: "ManageIPAddr"
#    cloud_field: "mgmtIp"
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "AssetCode"
#    cloud_field: "assertNo"
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "MaintenanceEndDate" # 行内云内数据类型都是date,格式是否需要转换待定
#    cloud_field: "warrantyEndTime"
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "MaintenanceStartDate"
#    cloud_field: "warrantyStartTime"
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "AdministratorA"
#    cloud_field: "incharger"
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "ApplicationSystem"
#    cloud_field: "applicationSystem"
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "AdminTeam"
#    cloud_field: "adminTeam"
#    sync_direction: "internal_to_cloud"
#    required: true
#
#  - internal_field: "OgMaintenceEndDate"
#    cloud_field: "ogMaintenceEndDate"
#    sync_direction: "internal_to_cloud"
#    is_check: true
#    required: true

# 云内 other=其他, x86=x86, powerpc=PowerPC, mips=MIPS, arm=ARM
# 行内 ——00：X86架构 ——01：ARM架构 ——02：Power架构 ——03：IA64架构 ——99：其它 默认为00
transformers:
  cpu_arch:
    type: "mapping"
    mapping:
      "x86": "00"
      "arm": "01"
      "default": "99"

check_templates:
  # 条件复用组
  - template: condition_group
    id: "normal_status"
    params:
      condition_field: "status"
      condition_value: "normal"  # 云内设备状态没有测试中状态

  # 基本信息必填检查
  - template: conditional_required
    scope: "record"
    params:
      condition_ref: "normal_status"
      required_field: "productName"
      message: "设备状态是运行中或测试中，型号必填"

  - template: date_min_value
    scope: "record"
    params:
      field: "ogMaintenceEndDate"
      min_date: "2000-01-01"
      message: "原厂维保结束日期必须大于2000-01-01"

  - template: compound_condition_required
    scope: "record"
    params:
      condition_ref: "normal_status"
      #      condition_field2: "hostType"
      #      condition_value2: "GPU服务器" # 云内没有该状态
      required_field: "GPUModel"
      message: "设备状态是运行中且服务器功能是GPU服务器时，GPU卡型号必填"

  - template: conditional_required
    scope: "record"
    params:
      condition_ref: "normal_status"
      required_field: "CPUBrand"
      message: "设备状态是运行中，CPU品牌必填"

# 行内需要回填的必填字段
backfill_field:
  - field_name: "Code"
    is_mapping: true  # 是否在同步列表中
    is_reference: false # 是否是外键属性

  - field_name: "CPUBrand"
    is_mapping: true

  - field_name: "MemoryBrand"
    is_mapping: true

  - field_name: "SerialNumber"
    is_mapping: true

  - field_name: "LocalStorageBrand"
    is_mapping: true

  - field_name: "Function"

  - field_name: "DeviceStatus"
    is_mapping: true

  - field_name: "RackBeginU"

  - field_name: "SysDevBrand"
    is_mapping: true

  - field_name: "TotalNumberOfCpu"
    is_mapping: true

  - field_name: "CpuFrequency"
    is_mapping: true

  - field_name: "AdminTeam"
    is_reference: true
    class_key: "Description"

  - field_name: "Rack"
    is_reference: true
    class_key: "Description"

  - field_name: "AdministratorA"

  - field_name: "AdministratorB"

  - field_name: "Description"

  - field_name: "ManageIPAddr"
    is_mapping: true

  - field_name: "CpuProductArch"
    is_mapping: true

  - field_name: "ArrivalDate"

  - field_name: "PurchaseContract"
    is_reference: true
    class_key: "Description"

  - field_name: "RackEndU"

  - field_name: "SupportIpv6"

  - field_name: "TotalNumOfCpuNuclear"
    is_mapping: true

  - field_name: "Model"
    is_mapping: true
