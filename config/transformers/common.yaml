# 通用转换函数配置
transformers:
  # 联调临时写死
  OS_constant:
    type: "constant"
    value: "LINUX-32位 -1.1"
  PCHostServer_constant:
    type: "constant"
    value: "PCHostedServer0001实例名"
  HostGroup_constant:
    type: "constant"
    value: "节点IP测试主机-001"
  ApplicationSystem_constant:
    type: "constant"
    value: "基础技术支撑平台B-Ggb226"
  HostType_constant:
    type: "constant"
    value: "管理服务器"
  AdministratorA_constant:
    type: "constant"
    value: "曹四四/018704"
  AdministratorB_constant:
    type: "constant"
    value: "缪是杰/011329"
  AdminTeam_constant:
    type: "constant"
    value: "总行/科技运维中心/福州应用处"

  format_datetime:
    type: "python"
    code: |
      def format_datetime(dt_str):
          from datetime import datetime
          if not dt_str:
              return ""
          try:
              dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
              return dt.strftime('%Y/%m/%d')
          except:
              return dt_str

  format_long_datetime:
    type: "python"
    code: |
      def format_timestamp(item, related=None, params=None, transformers=None):
          """
          智能格式化时间戳或时间字符串为 '%Y-%m-%d %H:%M:%S' 格式
          
          Args:
              timestamp: 支持以下类型:
                  - int/float: 毫秒级时间戳
                  - str: 符合 '%Y-%m-%d %H:%M:%S' 格式的字符串
                  - str: 符合 'YYYY-MM-DDTHH:MM:SS.ssss' 格式的ISO时间字符串
          
          Returns:
              格式化后的日期字符串（格式：YYYY-MM-DD HH:MM:SS）
              若转换失败则返回原始输入的字符串表示
          """
          from datetime import datetime
          if params is None:
                params = {}
  
          # 处理输入值
          timestamp = ''  # 默认为空字符串
          if isinstance(item, dict) and params and 'field' in params:
              # 如果item是字典且指定了字段，从字典中获取值
              field = params.get('field')
              timestamp = item.get(field, '')  # 如果字段不存在，返回空字符串
          elif isinstance(item, str):
              # 如果item是字符串，直接使用
              timestamp = item
            # 其他情况保持默认的空字符串
          # 处理空值
          if not timestamp:
              return ""
          try:
              # 处理数值类型的时间戳
              if isinstance(timestamp, (int, float)):
                  # 将毫秒级时间戳转换为秒级
                  timestamp_seconds = timestamp / 1000.0
                  dt = datetime.fromtimestamp(timestamp_seconds)
                  return dt.strftime('%Y-%m-%d %H:%M:%S')
              
              # 处理字符串类型
              elif isinstance(timestamp, str):
                  # 尝试解析标准格式
                  try:
                      dt = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')
                      return timestamp  # 已经是正确格式，直接返回
                  except ValueError:
                      pass
                  
                  # 尝试解析ISO格式（带毫秒）
                  try:
                      dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                      return dt.strftime('%Y-%m-%d %H:%M:%S')
                  except ValueError:
                      pass
                  
                  # 尝试解析ISO格式（不带毫秒）
                  try:
                      dt = datetime.strptime(timestamp, '%Y-%m-%dT%H:%M:%S')
                      return dt.strftime('%Y-%m-%d %H:%M:%S')
                  except ValueError:
                      pass
              # 无法处理的类型，返回原始输入的字符串表示
              return str(timestamp)
          except Exception as e:
              # 捕获所有其他异常，确保函数不会崩溃
              return str(timestamp)
  
  format_ip_info:
    type: "python"
    code: |
      def format_ip_info(vm, related):
          ips = []
          if vm.get("ipAddress"):
              ips.append(f"私网IP: {vm['ipAddress']}")
          if vm.get("floatingIp"):
              ips.append(f"公网IP: {vm['floatingIp']}")
          return ", ".join(ips)

  #状态映射---行内的设备状态是00:运行中01:测试中02:退役03:待报废04:外借05:已入库06:待运行，新增一个关机状态、故障状态
  #云内的设备状态normal=正常, offline=离线, abnormal=故障, unknown=未知。
  #如果基于云内设备的状态更新行内设备的状态，则：
  #新云【运行中】->行内的【运行中】，新云【离线】->行内的【关机】，新云【故障】->行内的【故障】
  x86service_status:
    type: "mapping"
    mapping:
      "normal": "00"
      "offline": "07" # 新增关机状态
      "abnormal": "08" # 新增故障状态
      "default": "08" # 默认故障状态

  # 存储设备健康状态
  health_status_mapping: #normal=健康, offline=离线, fault=故障, pre-fault=即将故障, unknown=未知  无行内映射信息
    type: "mapping"
    mapping:
      "normal": "健康"
      "offline": "离线"
      "fault": "故障"
      "pre-fault": "即将故障"
      "unknown": "未知"

  workload_type_mapping:
    type: "mapping"
    mapping:
      "deployment": "无状态负载"
      "statefulSet": "有状态负载"
      "daemonSet": "守护进程集"
      "job": "普通任务"
      "cronJob": "定时任务"
      "podGroup": "容器组"
  # SYS_Switch 对应设备状态
  # 模型特有的转换函数    行内 -> 云内。 枚举转换
  # 行内枚举值：运行中、运行中（灾备）、闲置、测试中、外借
  # 云内枚举值：running=运行, unused=闲置, used=已使用, stock=库存, retirement=报废, maintenance=维护, occupied=已分配
  switch_device_status:
    type: "mapping"
    mapping: # 云内同步行内的枚举值映射关系
      "running": "运行中"    # 运行中
      "unused": "闲置"   # 闲置
    reverse_mapping: # 行内同步云内的枚举值映射关系
      "运行中": "running"
      "闲置": "unused"
      "default": "stock"
    description: "网络设备状态"  # 添加映射说明

  # 获取关联模型的单个属性
  get_related_attribute:
    type: "python"
    code: |
      def get_related_attribute(item, related, params=None):
          """获取关联模型的属性
      
          Args:
              item: 主模型数据
              related: 关联模型数据
              params: 转换参数
                  model: 关联模型名称
                  attribute: 要获取的属性
                  format: 格式化字符串
                  number: 是否全部列举（true全部列举，false只取第一个）
      
          Returns:
              格式化后的属性值
          """
          if not params:
              return None
      
          model = params.get('model')
          attribute = params.get('attribute')
          format_str = params.get('format', '{value}')
          number = params.get('number',True)
      
          if not model or not attribute or model not in related:
              return None
      
          related_items = related.get(model)
      
          if not related_items:
              return None
      
          # 获取属性值
          values = []
      
          if isinstance(related_items, list):
              # 一对多或关系表关联
              for item in related_items:
                  value = item.get(attribute)
                  if value:
                      values.append(format_str.format(value=value))
          else:
              # 一对一关联
              value = related_items.get(attribute)
              if value:
                  values.append(format_str.format(value=value))
      
          if not number:
              return values[0] if values else None
      
          return ', '.join(values) if values else None
  # 获取application的值
  get_from_application_code:
    type: "python"
    code: |
      def get_from_application_code(item, related, params, transformers):
          """
          从 internal_related_data 中获取指定 model_name、eps_id、model_field 的值。
          如果 params 中包含 replace_field，则对获取到的 model_field 进行字符串替换处理。
          """
          # 提取必要参数并设置默认值
          if not params:
              return None
         
          internal_related_data = params.get('internal_related_data',{})
          model_name = params.get('model_name','')
          model_field = params.get('model_field','')
          cloud_field = params.get('cloud_field', params.get('field'))
          # 快速失败：检查必要条件
          if not all([internal_related_data, model_name, model_field,cloud_field]):
              return None
          eps_id = item.get(cloud_field,'')
          if not eps_id or eps_id == '0':
              default_key = params.get('default_value')
              application_info_dict = internal_related_data.get(model_name, {}).get(default_key, {})
          else:
              application_info_dict = (internal_related_data.get(model_name, {}).get(eps_id, {}))
          if not application_info_dict:
              return None
          # 如果需要替换字段
          if "replace_field" in params:
              model_field_value = application_info_dict.get(params.get("model_field"))
              replace_field_value = application_info_dict.get(params.get("replace_field"))
              constant_value = params.get("constant")
              if model_field_value and replace_field_value:
                  return model_field_value.replace(f"{constant_value}{replace_field_value}", "")
              else:
                  return None  # 或者 return None，视业务需求
          else:
              # 链式查找并返回结果
              return application_info_dict.get(model_field)
    # 获取关联属性并应用指定的映射转换器
  get_related_attribute_and_map:
    type: "python"
    code: |
      def get_related_attribute_and_map(item, related, params=None, transformers=None):
          """获取关联模型的属性并应用指定的映射转换器
      
          Args:
              item: 主模型数据
              related: 关联模型数据
              params: 转换参数
                  model: 关联模型名称
                  attribute: 要获取的属性
                  mapping_name: 映射转换器名称
                  format: 格式化字符串（可选，默认为"{value}"）
                  default: 默认值（可选）
              transformers: 所有可用的转换器（由框架注入）
      
          Returns:
              转换后的属性值
          """
          if not params:
              return None
      
          model = params.get('model')
          attribute = params.get('attribute')
          mapping_name = params.get('mapping_name')
          format_str = params.get('format', '{value}')
          default = params.get('default', '未知')
      
          if not model or not attribute or model not in related:
              return None
      
          related_items = related.get(model)
      
          if not related_items:
              return None
      
          # 获取属性值
          value = None
          if isinstance(related_items, list):
              # 一对多或关系表关联，取第一个值
              if related_items:
                  value = related_items[0].get(attribute)
          else:
              # 一对一关联
              value = related_items.get(attribute)
      
          if value is None:
              return default
      
          # 获取映射转换器
          if transformers and mapping_name and mapping_name in transformers:
              mapping_transformer = transformers.get(mapping_name)
              if mapping_transformer and mapping_transformer.get('type') == 'mapping':
                  # 获取映射字典
                  mapping = mapping_transformer.get('mapping', {})
                  # 应用映射
                  str_value = str(value).lower() if value is not None else ''
                  value = mapping.get(str_value, mapping.get('default', default))
      
          # 应用格式化
          try:
              return format_str.format(value=value)
          except:
              return value

  get_related_attribute_and_concat:
  type: "python"
  code: |
    def get_related_attribute_and_concat(item, related, params=None, transformers=None):
        """获取关联模型的属性并与其他字段拼接
    
        Args:
            item: 主模型数据
            related: 关联模型数据
            params: 转换参数
                model: 关联模型名称
                attribute: 要获取的主属性
                fields: 要拼接的其他字段列表
                separator: 拼接分隔符（默认","）
                format: 格式化字符串（默认"{value}"）
                default: 默认值（默认""）
                skip_empty: 是否跳过空值（默认True）
        
        Returns:
            拼接后的属性值
        """
        if not params:
            return None
    
        model = params.get('model')
        attribute = params.get('attribute')
        fields = params.get('fields', [])
        separator = params.get('separator', ',')
        format_str = params.get('format', '{value}')
        default = params.get('default', '')
        skip_empty = params.get('skip_empty', True)
    
        if not model or not attribute or model not in related:
            return None
    
        related_items = related.get(model)
        if not related_items:
            return None
    
        # 获取主属性值
        main_value = None
        if isinstance(related_items, list):
            # 一对多关联，取第一个
            if related_items:
                main_value = related_items[0].get(attribute)
        else:
            # 一对一关联
            main_value = related_items.get(attribute)
    
        if main_value is None:
            return default
    
        # 收集要拼接的值
        values = [str(main_value)]
        
        # 处理额外的拼接字段
        if fields:
            for field in fields:
                field_value = item.get(field)
                # 处理空值
                if field_value is not None:
                    values.append(str(field_value))
                elif not skip_empty:
                    values.append('')
    
        # 拼接所有值
        result = separator.join(values)
        
        # 应用格式化
        try:
            return format_str.format(value=result)
        except:
            return result

  # 获取关联模型的多个属性
  get_related_attributes:
    type: "python"
    code: |
      def get_related_attributes(item, related, params=None):
          """获取关联模型的多个属性
      
          Args:
              item: 主模型数据
              related: 关联模型数据
              params: 转换参数
                  model: 关联模型名称
                  attributes: 要获取的多个属性
                  format: 格式化字符串
                  separator: 分隔符
      
          Returns:
              格式化后的属性值
          """
          if not params:
              return None
      
          model = params.get('model')
          attributes = params.get('attributes', [])
          format_str = params.get('format', '{label}: {value}')
          separator = params.get('separator', ', ')
      
          if not model or not attributes or model not in related:
              return None
      
          related_items = related.get(model)
      
          if not related_items:
              return None
      
          # 获取属性值
          result = []
      
          if isinstance(related_items, list):
              # 一对多或关系表关联
              for related_item in related_items:
                  item_values = []
      
                  for attr in attributes:
                      attr_name = attr.get('name')
                      attr_label = attr.get('label', attr_name)
      
                      if attr_name and attr_name in related_item:
                          value = related_item.get(attr_name)
                          if value:
                              item_values.append(format_str.format(label=attr_label, value=value))
      
                  if item_values:
                      result.append(separator.join(item_values))
          else:
              # 一对一关联
              item_values = []
      
              for attr in attributes:
                  attr_name = attr.get('name')
                  attr_label = attr.get('label', attr_name)
      
                  if attr_name and attr_name in related_items:
                      value = related_items.get(attr_name)
                      if value:
                          item_values.append(format_str.format(label=attr_label, value=value))
      
              if item_values:
                  result.append(separator.join(item_values))
      
          return '\n'.join(result) if result else None

  # 计算关联模型属性的总和
  calculate_related_sum:
    type: "python"
    code: |
      def calculate_related_sum(item, related, params=None, transformers=None):
          """计算关联模型属性的总和，并可选择性地进行单位转换

          Args:
              item: 主模型数据
              related: 关联模型数据
              params: 转换参数
                  model: 关联模型名称
                  attribute: 要统计的属性
                  unit_conversion: 单位转换因子（可选）
                  format: 格式化字符串（可选）
                  convert_unit: 是否进行单位转换（可选）
                  from_unit: 源单位（可选，默认为"MB"）
                  to_unit: 目标单位（可选，默认为"GB"）

          Returns:
              统计结果
          """
          if not params:
              return 0

          model = params.get('model')
          attribute = params.get('attribute')
          unit_conversion = params.get('unit_conversion', 1)
          format_str = params.get('format', '{value}')
          convert_unit = params.get('convert_unit', False)
          from_unit = params.get('from_unit', 'MB')
          to_unit = params.get('to_unit', 'GB')

          if not model or not attribute or model not in related:
              return 0

          related_items = related.get(model)

          if not related_items:
              return 0

          # 计算总和
          total = 0

          if isinstance(related_items, list):
              # 一对多或关系表关联
              for item in related_items:
                  value = item.get(attribute)
                  if value is not None:
                      try:
                          # 尝试转换为数值
                          numeric_value = float(value)
                          total += numeric_value
                      except (ValueError, TypeError):
                          # 忽略非数值
                          pass
          else:
              # 一对一关联
              value = related_items.get(attribute)
              if value is not None:
                  try:
                      # 尝试转换为数值
                      numeric_value = float(value)
                      total += numeric_value
                  except (ValueError, TypeError):
                      # 忽略非数值
                      pass

          # 应用单位转换因子
          if unit_conversion != 1:
              total = total / unit_conversion

          # 进行单位转换
          if convert_unit:
              # 定义单位和对应的幂
              units = {
                  "B": 0,
                  "KB": 1,
                  "MB": 2,
                  "GB": 3,
                  "TB": 4,
                  "PB": 5
              }
      
              # 检查单位是否有效
              if from_unit.upper() in units and to_unit.upper() in units:
                  # 计算单位差异
                  unit_diff = units[from_unit.upper()] - units[to_unit.upper()]
      
                  # 应用转换
                  if unit_diff > 0:
                      # 大转小，乘以 1024^unit_diff
                      total = total * (1024 ** unit_diff)
                  else:
                      # 小转大，除以 1024^(-unit_diff)
                      total = total / (1024 ** (-unit_diff))

          # 格式化结果
          try:
              return format_str.format(value=total)
          except:
              return total

    # 计算多个关联模型属性的总和
  calculate_multi_related_sum:
    type: "python"
    code: |
      def calculate_multi_related_sum(item, related, params=None):
          """计算多个关联模型属性的总和
      
          Args:
              item: 主模型数据
              related: 关联模型数据
              params: 转换参数
                  models: 关联模型配置列表
                    - model: 关联模型名称
                      attribute: 要统计的属性
                      unit_conversion: 单位转换因子（可选）
                  format: 格式化字符串（可选）
      
          Returns:
              统计结果
          """
          if not params or 'models' not in params:
              return 0
      
          models_config = params.get('models', [])
          format_str = params.get('format', '{value}')
      
          if not models_config:
              return 0
      
          # 计算总和
          total = 0
      
          # 遍历每个模型配置
          for model_config in models_config:
              model = model_config.get('model')
              attribute = model_config.get('attribute')
              unit_conversion = model_config.get('unit_conversion', 1)
      
              if not model or not attribute or model not in related:
                  continue
      
              related_items = related.get(model)
      
              if not related_items:
                  continue
      
              # 计算当前模型的总和
              model_total = 0
      
              if isinstance(related_items, list):
                  # 一对多或关系表关联
                  for item in related_items:
                      value = item.get(attribute)
                      if value is not None:
                          try:
                              # 尝试转换为数值
                              numeric_value = float(value)
                              model_total += numeric_value
                          except (ValueError, TypeError):
                              # 忽略非数值
                              pass
              else:
                  # 一对一关联
                  value = related_items.get(attribute)
                  if value is not None:
                      try:
                          # 尝试转换为数值
                          numeric_value = float(value)
                          model_total += numeric_value
                      except (ValueError, TypeError):
                          # 忽略非数值
                          pass
      
              # 应用单位转换
              if unit_conversion != 1:
                  model_total = model_total / unit_conversion
      
              # 累加到总和
              total += model_total
      
          # 格式化结果
          try:
              return format_str.format(value=total)
          except:
              return total

  # 计算关联模型属性的平均值
  calculate_related_average:
    type: "python"
    code: |
      def calculate_related_average(item, related, params=None):
          """计算关联模型属性的平均值

          Args:
              item: 主模型数据
              related: 关联模型数据
              params: 转换参数
                  model: 关联模型名称
                  attribute: 要统计的属性
                  format: 格式化字符串（可选）

          Returns:
              统计结果
          """
          if not params:
              return 0

          model = params.get('model')
          attribute = params.get('attribute')
          format_str = params.get('format', '{value}')

          if not model or not attribute or model not in related:
              return 0

          related_items = related.get(model)

          if not related_items:
              return 0

          # 计算平均值
          total = 0
          count = 0

          if isinstance(related_items, list):
              # 一对多或关系表关联
              for item in related_items:
                  value = item.get(attribute)
                  if value is not None:
                      try:
                          # 尝试转换为数值
                          numeric_value = float(value)
                          total += numeric_value
                          count += 1
                      except (ValueError, TypeError):
                          # 忽略非数值
                          pass
          else:
              # 一对一关联
              value = related_items.get(attribute)
              if value is not None:
                  try:
                      # 尝试转换为数值
                      numeric_value = float(value)
                      total += numeric_value
                      count += 1
                  except (ValueError, TypeError):
                      # 忽略非数值
                      pass

          # 计算平均值
          average = total / count if count > 0 else 0

          # 格式化结果
          try:
              return format_str.format(value=average)
          except:
              return average

  # 计算关联模型的数量
  calculate_related_count:
    type: "python"
    code: |
      def calculate_related_count(item, related, params=None):
          """计算关联模型的数量

          Args:
              item: 主模型数据
              related: 关联模型数据
              params: 转换参数
                  model: 关联模型名称
      
                  condition: 条件（可选）
                      attribute: 属性名
                      operator: 操作符（equal, not_equal, greater, less, contains, not_contains）
                      value: 比较值

          Returns:
              统计结果
          """
          if not params:
              return 0

          model = params.get('model')
          condition = params.get('condition')

          if not model or model not in related:
              return 0

          related_items = related.get(model)

          if not related_items:
              return 0

          # 如果没有条件，直接返回数量
          if not condition or not isinstance(related_items, list):
              return len(related_items) if isinstance(related_items, list) else 1

          # 应用条件过滤
          attribute = condition.get('attribute')
          operator = condition.get('operator')
          value = condition.get('value')
          if not value and "attribute" in params:
              value = item.get(params.get('attribute'))
          if not attribute or not operator:
              return len(related_items)

          # 过滤符合条件的项
          filtered_items = []

          for item in related_items:
              if attribute not in item:
                  continue
              item_value = item.get(attribute)
              if operator == 'equal' and item_value == value:
                  filtered_items.append(item)
              elif operator == 'not_equal' and item_value != value:
                  filtered_items.append(item)
              elif operator == 'greater' and item_value > value:
                  filtered_items.append(item)
              elif operator == 'less' and item_value < value:
                  filtered_items.append(item)
              elif operator == 'contains' and value in str(item_value):
                  filtered_items.append(item)
              elif operator == 'not_contains' and value not in str(item_value):
                  filtered_items.append(item)

          return len(filtered_items)

  # 计算关联模型属性的最大值
  calculate_related_max:
    type: "python"
    code: |
      def calculate_related_max(item, related, params=None):
          """计算关联模型属性的最大值

          Args:
              item: 主模型数据
              related: 关联模型数据
              params: 转换参数
                  model: 关联模型名称
                  attribute: 要统计的属性
                  format: 格式化字符串（可选）

          Returns:
              统计结果
          """
          if not params:
              return None

          model = params.get('model')
          attribute = params.get('attribute')
          format_str = params.get('format', '{value}')

          if not model or not attribute or model not in related:
              return None

          related_items = related.get(model)

          if not related_items:
              return None

          # 查找最大值
          max_value = None

          if isinstance(related_items, list):
              # 一对多或关系表关联
              for item in related_items:
                  value = item.get(attribute)
                  if value is not None:
                      try:
                          # 尝试转换为数值
                          numeric_value = float(value)
                          if max_value is None or numeric_value > max_value:
                              max_value = numeric_value
                      except (ValueError, TypeError):
                          # 忽略非数值
                          pass
          else:
              # 一对一关联
              value = related_items.get(attribute)
              if value is not None:
                  try:
                      # 尝试转换为数值
                      max_value = float(value)
                  except (ValueError, TypeError):
                      # 忽略非数值
                      pass

          # 格式化结果
          if max_value is not None:
              try:
                  return format_str.format(value=max_value)
              except:
                  return max_value
          else:
              return None

  # 通用容量单位转换
  convert_capacity:
    type: "python"
    code: |
      def convert_capacity(item, related=None, params=None):
          """通用容量单位转换
      
          支持的单位: B, KB, MB, GB, TB, PB
      
          Args:
              item: 主模型数据或要转换的值
              related: 关联数据（不使用）
              params: 转换参数
                  from_unit: 源单位 (默认: "MB")
                  to_unit: 目标单位 (默认: "GB")
                  format: 格式化字符串 (默认: "{value}")
                  field: 要获取的字段名（可选，如果item是字典）
      
          Returns:
              转换后的值
          """
          # 处理输入值
          value = None
          if isinstance(item, dict) and params and 'field' in params:
              # 如果item是字典且指定了字段，从字典中获取值
              field = params.get('field')
              value = item.get(field)
          else:
              # 否则直接使用item作为值
              value = item
      
          if value is None:
              return None
      
          if not params:
              params = {}
      
          # 定义单位和对应的幂
          units = {
              "B": 0,
              "KB": 1,
              "MB": 2,
              "GB": 3,
              "TB": 4,
              "PB": 5
          }
      
          # 获取源单位和目标单位
          from_unit = params.get("from_unit", "MB").upper()
          to_unit = params.get("to_unit", "GB").upper()
      
          # 检查单位是否有效
          if from_unit not in units or to_unit not in units:
              return value
      
          try:
              # 将值转换为浮点数
              numeric_value = float(value)
      
              # 计算单位差异
              unit_diff = units[from_unit] - units[to_unit]
      
              # 应用转换
              # 如果 unit_diff > 0，表示从大单位转到小单位（如 GB -> MB），需要乘以 1024^unit_diff
              # 如果 unit_diff < 0，表示从小单位转到大单位（如 MB -> GB），需要除以 1024^(-unit_diff)
              if unit_diff > 0:
                  # 大转小，乘以 1024^unit_diff
                  converted_value = numeric_value * (1024 ** unit_diff)
              else:
                  # 小转大，除以 1024^(-unit_diff)
                  converted_value = numeric_value / (1024 ** (-unit_diff))
      
              # 应用格式化
              format_str = params.get("format", "{value}")
              return format_str.format(value=converted_value)
      
          except (ValueError, TypeError) as e:
              return value

  # 拼接多个字段值
  concat_fields:
    type: "python"
    code: |
      def concat_fields(item, related=None, params=None):
          """拼接多个字段的值

          Args:
              item: 数据项
              related: 关联数据（不使用）
              params: 转换参数
                  fields: 要拼接的字段列表
                  index: 取第一个字段中的第几个值（默认为None，表示取所有值）
                  ip_separator: IP地址之间的分隔符（默认为";"）
                  separator: 字段值之间的分隔符（默认为","）
                  skip_empty: 是否跳过空值（默认为True）
                  constant: 常量前缀（可选）
                  ipv4_only: 是否只返回IPv4地址（默认为False）
                  sub_split: 
                    fields: 要处理的字段列表
                    separator: 根据分割符处理字段（默认","）
                    index: 取字段中的第几个值（默认为None，表示取所有值）
                    skip_empty: 是否跳过空值（默认True）
                  format: 格式化字符串（可选，默认为"{value}"）


          Returns:
              拼接后的字符串
          """
          import re
          
          if not params or 'fields' not in params:
              return None

          fields = params.get('fields', [])
          separator = params.get('separator', ',')
          skip_empty = params.get('skip_empty', True)
          constant = params.get('constant', '')
          index = params.get('index')  # 默认为None，表示不取特定索引
          ip_separator = params.get('ip_separator', ';')  # IP地址之间的分隔符
          ipv4_only = params.get('ipv4_only', False)  # 是否只返回IPv4地址
          format_str = params.get('format', '{value}')  # 格式化字符串
          
          # 新增字段处理
          sub_split = params.get('sub_split', {})
          sub_fields = sub_split.get('fields', [])
          sub_separator = sub_split.get('separator', separator)
          sub_index = sub_split.get('index', None)
          sub_skip_empty = sub_split.get('skip_empty', True)
          
          # 清理IP地址中的特殊符号的函数
          def clean_ip_value(value):
              if value is None:
                  return ""
              
              value_str = str(value).strip()
              
              # 处理@符号包围的IP格式
              if '@' in value_str:
                  # 使用正则表达式提取@之间的IP地址
                  ip_matches = re.findall(r'@([^@]+)@', '@' + value_str + '@')
                  if ip_matches:
                      # 将提取的IP地址合并为分号分隔的字符串
                      value_str = ip_separator.join(ip_matches)
              
              # 分割IP地址
              ip_list = value_str.split(ip_separator)
              
              # 清理IP地址（去除空格）
              ip_list = [ip.strip() for ip in ip_list if ip.strip()]
              
              # 如果需要只返回IPv4地址
              if ipv4_only:
                  # IPv4地址正则表达式
                  ipv4_pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
                  # 过滤出IPv4地址
                  ipv4_list = [ip for ip in ip_list if re.match(ipv4_pattern, ip)]
                  
                  # 如果有IPv4地址，使用IPv4地址列表
                  if ipv4_list:
                      ip_list = ipv4_list
              
              # 重新组合IP地址
              return ip_separator.join(ip_list)

          values = []
          for field in fields:
              value = item.get(field)
              if value is not None and (not skip_empty or str(value).strip()):
                  value = clean_ip_value(value)

                  # 如果字段不需要特殊处理，直接添加（去重）
                  if field not in sub_fields:
                      if value not in values:
                          values.append(value)
                      continue

                  # 对需要特殊处理的字段进行分割处理
                  sub_values = [v.strip() for v in value.split(sub_separator) if (not sub_skip_empty or v.strip())]
                  if sub_index is not None:
                      try:
                          sub_index = int(sub_index)  # 确保index是整数
                          if 0 <= sub_index < len(sub_values):
                              value = sub_values[sub_index]
                      except (ValueError, TypeError, IndexError):
                          value = ''

                  # 检查处理后的值是否为空
                  if not value:
                      continue

                  # 添加处理后的值（去重）
                  if value not in values:
                      values.append(value)
          
          # 如果没有有效值，返回常量或None
          if not values:
              return constant if constant else None
          
          # 处理索引（如果指定了索引且是第一个字段的情况）
          result = None
          if index is not None and values:
              # 分割第一个字段的值
              ip_list = values[0].split(ip_separator)
              
              try:
                  # 尝试获取指定索引的值
                  index = int(index)  # 确保index是整数
                  if 0 <= index < len(ip_list):
                      result = ip_list[index]
              except (ValueError, TypeError, IndexError):
                  # 如果索引无效，使用默认行为
                  pass
          
          # 如果没有通过索引获取到结果，则使用默认的拼接行为
          if result is None:
              result = separator.join(values)
          
          # 如果有常量，将常量和分隔符添加到结果前面
          if constant:
              result = constant + separator + result
          
          # 应用格式化
          try:
              return format_str.format(value=result)
          except:
              return result

  # 计算数学表达式
  calculate_expression:
    type: "python"
    code: |
      def calculate_expression(item, related=None, params=None):
          """计算数学表达式

          Args:
              item: 数据项
              related: 关联数据
              params: 转换参数
                  expression: 数学表达式，可以引用数据项中的字段
                  fields: 要使用的字段列表（可选）
                    - name: 字段名称
                      source: 字段来源（primary 或 related，默认为 primary）
                      model: 关联模型名称（当 source 为 related 时必须）
                      default: 默认值（可选，默认为 0）
                  format: 格式化字符串（可选，默认为"{value}"）
                  default: 计算失败时的默认值（可选，默认为0）
                  convert_unit: 是否进行单位转换（可选，默认为False）
                  from_unit: 源单位（可选，默认为"MB"）
                  to_unit: 目标单位（可选，默认为"GB"）
          Returns:
              计算结果
          """
          if not params or 'expression' not in params:
              return 0

          expression = params.get('expression')
          format_str = params.get('format', '{value}')
          default = params.get('default', 0)
          fields = params.get('fields', [])
          
          # 单位转换参数
          convert_unit = params.get('convert_unit', False)
          from_unit = params.get('from_unit', 'MB')
          to_unit = params.get('to_unit', 'GB')
      
          # 创建一个变量字典，用于替换表达式中的字段引用
          variables = {}
      
          if fields:
              # 如果指定了字段列表，只处理这些字段
              for field_config in fields:
                  field_name = field_config.get('name')
                  source = field_config.get('source', 'primary')
                  model = field_config.get('model')
                  field_default = field_config.get('default', 0)
      
                  if not field_name:
                      continue
      
                  # 根据来源获取字段值
                  field_value = None
                  if source == 'primary':
                      field_value = item.get(field_name)
                  elif source == 'related' and model and related and model in related:
                      related_item = related.get(model)
                      if isinstance(related_item, list) and related_item:
                          # 一对多关系，取第一个
                          field_value = related_item[0].get(field_name)
                      elif isinstance(related_item, dict):
                          # 一对一关系
                          field_value = related_item.get(field_name)
      
                  # 处理字段值
                  if field_value is None:
                      variables[field_name] = field_default
                  else:
                      try:
                          # 尝试转换为数值
                          variables[field_name] = float(field_value)
                      except (ValueError, TypeError):
                          # 非数值字段，使用默认值
                          variables[field_name] = field_default
          else:
              # 如果没有指定字段列表，使用所有主模型字段
              for field_name, field_value in item.items():
                  if field_value is None:
                      variables[field_name] = 0
                  else:
                      try:
                          # 尝试转换为数值
                          variables[field_name] = float(field_value)
                      except (ValueError, TypeError):
                          # 非数值字段，设为0
                          variables[field_name] = 0
      
          try:
              # 使用变量字典中的值替换表达式中的字段引用
              # 这里我们使用 eval 函数，但需要注意安全性
              # 在生产环境中，应该使用更安全的方法，如 simpleeval 库
              result = eval(expression, {"__builtins__": {}}, variables)
              
              # 如果需要进行单位转换
              if convert_unit and result is not None:
                  # 定义单位和对应的幂
                  units = {
                      "B": 0,
                      "KB": 1,
                      "MB": 2,
                      "GB": 3,
                      "TB": 4,
                      "PB": 5
                  }
                  
                  # 检查单位是否有效
                  from_unit = from_unit.upper()
                  to_unit = to_unit.upper()
                  
                  if from_unit in units and to_unit in units:
                      # 计算单位差异
                      unit_diff = units[from_unit] - units[to_unit]
                      
                      # 应用转换
                      if unit_diff > 0:
                          # 大转小，乘以 1024^unit_diff
                          result = result * (1024 ** unit_diff)
                      else:
                          # 小转大，除以 1024^(-unit_diff)
                          result = result / (1024 ** (-unit_diff))

              # 应用格式化
              return format_str.format(value=result)
          except Exception as e:
              # 计算失败，返回默认值
              return default

  number_format_value:
    type: "python"
    code: |
      def number_format_value(item, related, params=None):
          """获取关联模型的属性

          Args:
              item: 主模型数据
              related: 关联模型数据
              params: 转换参数
                  model: 关联模型名称
                  attribute: 要获取的属性
                  format: 格式化字符串

          Returns:
              格式化后的属性值
          """
          if not params:
              return None

          model = params.get('model')
          attribute = params.get('attribute')
          format_str = params.get('format', '{value}')
          number = params.get('number',True)

          if not model or not attribute or model not in related:
              return None

          related_items = related.get(model)

          if not related_items:
              return None

          # 获取属性值
          values = []

          if isinstance(related_items, list):
              # 一对多或关系表关联
              for item in related_items:
                  value = item.get(attribute)
                  if value:
                      values.append(format_str.format(value=value))
          else:
              # 一对一关联
              value = related_items.get(attribute)
              if value:
                  values.append(format_str.format(value=value))

          if not number:
              return values[0] if values else None

          return ', '.join(values) if values else None

  # 比较关联字段值
  compare_relation_field:
    type: "python"
    code: |
      def compare_relation_field(item, related, params=None):
          """比较行内和云内的字段值，如果有变化则返回整条数据

          Args:
              item: 行内数据
              related: 关联数据
              params: 转换参数
                  relation_model: 关联模型名称
                  relation_key: 关联键名
                  internal_field: 行内字段名
                  cloud_field: 云内字段名

          Returns:
              如果有变化返回整条数据，否则返回None
          """
          if not params:
              return None
          
          relation_model = params.get('relation_model')
          relation_key = params.get('relation_key')
          internal_field = params.get('internal_field')
          cloud_field = params.get('cloud_field')
          
          if not all([relation_model, relation_key, internal_field, cloud_field]):
              return None

          if relation_model not in related:
              return None
          # 所有的关联关系数据  这个数据是查询关联关系出来的数据
          related_items = related.get(relation_model)
          if not related_items:
              return None
          internal_value = item.get(internal_field) # 行內数据
          relation_action = related_items[0].get('_relation')
          cloud_value = relation_action.get(cloud_field)
          if str(internal_value) != str(cloud_value):
              return relation_action
          return None

  # 从分隔符分隔的IP地址列表中获取指定位置的IP地址
  get_ip_address:
    type: "python"
    code: |
      def get_ip_from_list(item, related=None, params=None):
          """
          从IP地址列表中获取指定位置的IPv4地址
          
          支持多种IP格式:
          - 分号分隔: "***********;********"
          - @符号包围: "@***********@********@"
          - 混合IPv4和IPv6: "***********;abcd:0:0:0:0:0:0:4"

          Args:
              item: 主模型数据或要处理的值
              related: 关联数据（不使用）
              params: 参数字典，包含以下可选参数：
                  separator: IP地址之间的分隔符，默认为";"
                  index: 要获取的IP地址的索引，默认为0（第一个）
                  fallback: 如果没有找到IP，返回的默认值，默认为空字符串
                  field: 要从item中获取的字段名（如果item是字典）
                  ipv4_only: 是否只返回IPv4地址，默认为True

          Returns:
              str: 指定位置的IP地址，如果不存在则返回fallback值
          """
          import re
          
          if params is None:
              params = {}

          # 处理输入值
          value = ''  # 默认为空字符串
          if isinstance(item, dict) and params and 'field' in params:
              # 如果item是字典且指定了字段，从字典中获取值
              field = params.get('field')
              value = item.get(field, '')  # 如果字段不存在，返回空字符串
          elif isinstance(item, str):
              # 如果item是字符串，直接使用
              value = item
          
          # 如果输入为None或空，直接返回fallback值
          if value is None or (isinstance(value, str) and not value.strip()):
              return params.get('fallback', '')

          # 获取参数
          separator = params.get('separator', ';')
          index = params.get('index', 0)
          fallback = params.get('fallback', '')
          ipv4_only = params.get('ipv4_only', True)
          
          try:
              # 将输入转换为字符串
              if not isinstance(value, str):
                  value = str(value)
              
              # 处理@符号包围的IP格式
              if '@' in value:
                  # 使用正则表达式提取@之间的IP地址
                  ip_matches = re.findall(r'@([^@]+)@', '@' + value + '@')
                  if ip_matches:
                      # 将提取的IP地址合并为分号分隔的字符串
                      value = ';'.join(ip_matches)
              
              # 按分隔符拆分IP列表
              ip_list = value.split(separator)
              
              # 清理IP地址（去除空格）
              ip_list = [ip.strip() for ip in ip_list if ip.strip()]
              
              # 如果需要只返回IPv4地址
              if ipv4_only:
                  # IPv4地址正则表达式
                  ipv4_pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
                  # 过滤出IPv4地址
                  ipv4_list = [ip for ip in ip_list if re.match(ipv4_pattern, ip)]
                  
                  # 如果有IPv4地址，使用IPv4地址列表
                  if ipv4_list:
                      ip_list = ipv4_list
              
              # 如果索引超出范围，返回fallback值
              if not ip_list or index >= len(ip_list):
                  return fallback
              
              # 返回指定位置的IP地址
              return ip_list[index]
              
          except Exception as e:
              # 捕获所有异常，确保函数不会崩溃
              return fallback
  # 判断当前的数据在行内是否可以插入 判断是否存在 或者外键是否存在
  get_related_attribute_and_check_internal:
    type: "python"
    code: |
      def get_related_attribute_and_check_internal(item, related, params=None):
          """
          获取属性值，并判断该值是否存在于 internal_relted_data 的指定模型中
          支持 source 参数：
            - source=primary 时，从 item 取值
            - source=related 时，从 related 取值（原有逻辑）
          有就返回第一个匹配的值，没有就返回 None
          """
          if not params:
              return None

          source = params.get('source', 'related')
          model = params.get('model')
          attribute = params.get('attribute')
          format_str = params.get('format', '{value}')
          internal_relted_data = params.get('internal_relted_data', {})
          internal_model = params.get('internal_model')
          internal_attribute = params.get('internal_attribute')

          if not attribute or not internal_model or not internal_attribute:
              return None

          # 获取 value
          values = []
          if source == 'primary':
              value = item.get(attribute)
              if value:
                  values.append(format_str.format(value=value))
          else:  # source == 'related'
              if not model or model not in related:
                  return None
              related_items = related.get(model)
              if not related_items:
                  return None
              if isinstance(related_items, list):
                  for rel_item in related_items:
                      value = rel_item.get(attribute)
                      if value:
                          values.append(format_str.format(value=value))
              else:
                  value = related_items.get(attribute)
                  if value:
                      values.append(format_str.format(value=value))

          # 获取 internal_relted_data 里的所有属性值集合
          internal_items = internal_relted_data.get(internal_model, [])
          internal_values = set()
          for internal_item in internal_items:
              v = internal_item.get(internal_attribute)
              if v is not None:
                  internal_values.add(str(v))

          # 判断 values 是否在 internal_values 里，只返回第一个匹配
          for v in values:
              if v in internal_values:
                  return v

          return None

  # 拼接主数据和关联数据的多个字段值
  concat_primary_and_related_fields:
    type: "python"
    code: |
      def concat_primary_and_related_fields(item, related, params=None):
          """
          拼接关联数据字段和主模型字段的值（顺序：关联数据字段 + 主模型字段）

          Args:
              item: 主模型数据
              related: 关联模型数据
              params: {
                  item_fields: 主模型要拼接的字段列表
                  related_fields: 关联模型要拼接的字段列表
                  related_model: 关联模型名
                  separator: 字段值之间的分隔符（默认","）
                  skip_empty: 是否跳过空值（默认True）
                  constant: 常量前缀（可选）
              }
          Returns:
              拼接后的字符串
          """
          if not params:
              return None

          item_fields = params.get('item_fields', [])
          related_fields = params.get('related_fields', [])
          related_model = params.get('related_model', '')
          separator = params.get('separator', ',')
          skip_empty = params.get('skip_empty', True)
          constant = params.get('constant', '')

          # 关联模型字段
          related_values = []
          if related_model and related_model in related:
              related_model_data = related.get(related_model, [])
              if isinstance(related_model_data, dict):
                  related_model_data = [related_model_data]
              for related_model_item in related_model_data:
                  for related_field in related_fields:
                      related_value = related_model_item.get(related_field)
                      if related_value is not None and (not skip_empty or str(related_value).strip()):
                          related_values.append(str(related_value).strip())

          # 主模型字段
          values = []
          for field in item_fields:
              value = item.get(field)
              if value is not None and (not skip_empty or str(value).strip()):
                  values.append(str(value).strip())

          # 合并（关联数据字段 + 主模型字段）
          all_values = related_values + values
          if not all_values:
              return constant if constant else None

          result = separator.join(all_values)
          if constant:
              result = constant + separator + result
          return result
  # 拼接IP与端口（支持长度补齐）
  concat_ip_and_port:
    type: "python"
    code: |
      def concat_ip_and_port(item, related, params=None):
          """
          拼接IP与端口列表（IP:Port），自动补齐端口长度
          
          Args:
              item: 主模型数据
              related: 关联数据（未使用）
              params: 参数字典
                  ip_field: IP字段名（必填）
                  port_field: 端口字段名（必填）
                  separator: 结果分隔符（默认','）
                  default_port: 端口不足时的默认值（可选）
          
          处理逻辑：
              - IP/端口均按逗号分隔为列表
              - 端口数量不足时：有默认端口则用默认值补齐，否则复制最后一个端口
              - 无端口时直接返回IP列表
          
          Returns:
              拼接后的字符串（如"IP1:Port1,IP2:Port2"），失败返回None
          """
          if not params:
              return None

          # 解析参数
          ip_fld = params.get('ip_field','')
          port_field = params.get('port_field', '')
          sep = params.get('separator', ',')
          default_port = params.get('default_port')

          # 校验必要参数
          if not ip_fld or not port_field:
              return None
        
          # 从主模型获取并拆分IP和端口
          ip_str = item.get(ip_fld, '')
          port_str = item.get(port_field, '')  # 原代码笔误修正：port_field参数引用
        
          ip_list = [ip.strip() for ip in str(ip_str).split(',') if ip.strip()]
          port_list = [port.strip() for port in str(port_str).split(',') if port.strip()]

          # 无IP时返回None
          if not ip_list:
              return None
        
          # 处理端口列表长度不足
          if len(port_list) < len(ip_list):
              if default_port:
                  # 用默认端口补齐
                  port_list += [default_port] * (len(ip_list) - len(port_list))
              else:
                  # 复制最后一个端口补齐（无端口则返回IP列表）
                  if port_list:
                      last_port = port_list[-1]
                      port_list += [last_port] * (len(ip_list) - len(port_list))
                  else:
                      return sep.join(ip_list)
        
          # 拼接IP:Port
          result = []
          for i, ip in enumerate(ip_list):
              port = port_list[i] if i < len(port_list) else default_port
              result.append(f"{ip}:{port}" if port else ip)

          return sep.join(result)


  calculate_field_sum:
    type: "python"
    code: |
      def calculate_field_sum(item, related, params=None, transformers=None):
          """
          处理item中指定字段的值，计算其总和并进行单位转换

          Args:
              item: 主模型数据，需包含params中指定的field属性
              params: 转换参数
                  field: 要处理的字段名（例如"diskSize"）
                  unit_conversion: 单位转换因子（可选）
                  format: 格式化字符串（可选）
                  convert_unit: 是否进行单位转换（可选）
                  from_unit: 源单位（可选，默认为"MB"）
                  to_unit: 目标单位（可选，默认为"GB"）

          Returns:
              处理后的结果字符串
          """
          if not params:
              return 0
          
          # 参数默认值
          unit_conversion = params.get('unit_conversion', 1)
          format_str = params.get('format', '{value}')
          convert_unit = params.get('convert_unit', False)
          from_unit = params.get('from_unit', 'MB')
          to_unit = params.get('to_unit', 'GB')
          
          # 获取配置的字段名
          field = params.get('field')
          if not field:
              return 0

          # 获取字段值
          field_str = item.get(field, '')
          if not field_str:
              return 0

          # 处理分号分隔的数值
          values = []
          for part in field_str.split(';'):
              part = part.strip()
              if part:
                  try:
                      values.append(float(part))
                  except (ValueError, TypeError):
                      pass

          if not values:
              return 0

          # 计算总和
          total = sum(values)

          # 应用单位转换因子
          if unit_conversion != 1:
              total = total / unit_conversion

          # 进行单位转换
          if convert_unit:
              # 定义单位和对应的幂
              units = {
                  "B": 0,
                  "KB": 1,
                  "MB": 2,
                  "GB": 3,
                  "TB": 4,
                  "PB": 5
              }

              # 检查单位是否有效
              if from_unit.upper() in units and to_unit.upper() in units:
                  # 计算单位差异
                  unit_diff = units[from_unit.upper()] - units[to_unit.upper()]

                  # 应用转换
                  if unit_diff > 0:
                      total = total * (1024 ** unit_diff)
                  else:
                      total = total / (1024 ** (-unit_diff))

          # 格式化结果
          try:
              return format_str.format(value=total)
          except:
              return total