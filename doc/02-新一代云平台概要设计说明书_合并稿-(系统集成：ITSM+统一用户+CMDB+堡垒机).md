# 应用架构设计（杨+各域小组写）

### 云管

#### 行内IT管理系统集成

##### **统一用户管理系统集成**

- **集成对接准备**

1、 网络打通：收集SSO Client、SSO
Server的端口、IP等信息；配置防火墙策略分别打通二者之间的网络

2、 SSO Server证书和各地址获取：

1\) SSO Server证书：身份提供商的信任证书，支持cer、der或pem；

2\) SSO Server认证地址

配置测试环境：https://www.sso.cibt:10659/CAS4ICMS/login

生产环境：https://www.sso.cib/CAS4ICMS/login

3\) SSO Sever校验地址配置：

测试环境：https://www.sso.cibt:10659/CAS4ICMS/proxyValidate

生产环境：https://www.sso.cib/CAS4ICMS/proxyValidate

4\) SSO Sever登出地址配置：

测试环境：https://www.sso.cibt:10659/CAS4ICMS/logout

生产环境：https://www.sso.cib/CAS4ICMS/logout

- **按照标准CAS2.0来完成对接**

在云管理平台导入SSO
Server端证书，填写认证地址、校验地址、登出地址等配置数据；SSO
Client完成对SSO Server相关数据配置和检验连通性和正确性。

- **用户数据同步**

云管理平台默认不同步行内统一用户管理系统中的用户，需通过SSO
Adapter来完成用户数据的同步。

行内统一用户管理系统定期将全量用户信息导入到SFTP
Server，至少包含用户名、归属组织、手机号、邮箱地址字段；SSO
Adapter定期从SFTP
Server获取用户列表，并根据预先配置的过滤条件（按照用户所属业务部门来进行过滤），将符合条件的用户同步到云管理平台中。

![](media/image1.png){width="5.768055555555556in"
height="3.1798611111111112in"}

##### **运维服务管理系统（ITSM）集成**

ITSM作为统一流程审批等承载系统，提供资源申请、告警转工单、运维操作等一系列操作的审批，所以云上云下各个系统的操作都需要统一对接到一套ITSM系统。

云管理平台和ITSM的交互，可以抽象成如下2类交互：

1\. 流程创建：ITSM提供创建流程接口，适配层经转换后供云平台调用

2\.
流程状态查询：ITSM提供流程状态查询接口，适配层定时查询流程状态，将结果通过调用接口方式同步到云平台来决策下一步操作。

![](media/image2.png){width="5.772222222222222in" height="1.55in"}

##### **运维服务管理系统（堡垒机）集成**

行内运维操作管理系统（堡垒机），主要用于单点登录、集中授权、审计等功能的系统。云内的虚拟机、裸金属服务器等需要统一纳管到行内堡垒机中，进行中管控。

云内资源纳管到堡垒机，主要分为以下三个部分：

1.  将云内资源注册到行内堡垒机平台中，云内资源主要包括所有虚拟机和裸金属服务器。

2.  将云内资源的账户信息纳管到行内堡垒机平台。

3.  支持在行内堡垒机平台轮转云内账户的密码。

![](media/image3.png){width="5.772222222222222in"
height="3.2041666666666666in"}

- 云内主机资源纳管至堡垒机

  - 通过适配层调用云内接口获取到云内的虚拟机信息、裸金属信息。

  - 适配层调用堡垒机新增设备的接口注册云内资源信息。

> 描述：API接口提供新增主机设备
>
> 请求方式：POST
>
> URL: /deviceInfo/host/saveHostInfo

- 云内数据库资源纳管至堡垒机

  - 数据库资源由TXSQL数据库管理平台、OpenGauss数据库管理平台来调用堡垒机接口完成数据库资源的纳管。

- 云内资源账户纳管至堡垒机

  - 云内管理虚拟机和资源租户虚拟机的账户信息已经被云管理平台的统一账户管理纳管。由适配层获取到该部分的账户信息，然后注册给行内堡垒机，在清理掉云管平台纳管的账户信息。确定账户信息只被一个地方纳管。

- 堡垒机纳管的云内账户密码修改

  - 数据库类：数据库改密由数据库管理平台进行改密，密码更新到堡垒机平台。

  - 其类资源的账户在堡垒机内修改，并下发到节点。

### 云平台CMDB

#### 行内CMDB对接集成

##### **数据流向设计**

行内CMDB作为数据中心的数据集成总线，实现了对云内、云外全栈的资源建模和纳管，云管理平台需和行内CMDB进行集成，数据同步既包含云内资源数据（如云服务数据）同步到行内CMDB，也包含行内CMDB数据（如物理设备运维数据）同步到云管理平台。云内数据同步到行内CMDB需做到准实时同步。

- 同步云内资源数据到行内CMDB：云管理平台调用行内CMDB的API接口，同步如下数据到行内CMDB。

  - 云资源池：包括计算资源池的主机组和宿主机信息，网络的各类网元集群信息

    - 资源池、主机组、主机、路由器、防火墙、交换机、可用区；

  - 云服务管理面：各类云服务管理面自身的资源信息；

    - ECS、IMS、EVS

  - 云资源：各类云服务资源实例信息。

    - ECS、IMS、EVS、EIP、ELB、DCS、DMS、RDS、GaussDB

  - 应用：应用及应用组件的资源信息。

    - ???

- 同步行内CMDB数据到云内：云管理平台调用行内CMDB的API接口，从行内CMDB同步如下数据到云管理平台。

  - 物理设备运维数据：包括归属数据中心、房间、机柜、管理IP、维护责任人等信息；

  - 业务系统：同步行内业务系统信息到云管理平台，注意这里只包含业务系统（即L1层），不包含应用及应用组件信息（即L2\~L4）层。

##### **集成对接架构设计**

因云内CMDB和行内CMDB数据模型存在差异，并且两边的接口都是标准接口，不完全匹配，因此需要有一个转换层来进行模型转换和接口适配，也就是下图所示的模型转换工具。

由于云内CMDB和行内CMDB的数据同步存在双向性，因此模型转换工具需同时承担双向的数据同步。

![](media/image4.png){width="4.557031933508312in"
height="3.043869203849519in"}
