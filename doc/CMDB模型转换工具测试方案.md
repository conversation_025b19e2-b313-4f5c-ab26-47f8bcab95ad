# CMDB模型转换工具测试方案

## 一、测试需求分析

根据项目特点，我们面临以下测试挑战：

1. **环境差异**：
   - 开发环境只能访问华为侧API，无法访问行内API
   - 现场环境是内网环境，可以访问两边的API

2. **测试需求**：
   - 需要在本地开发环境快速验证功能
   - 需要在现场环境快速验证配置和连接
   - 需要测试各个模块的功能正确性
   - 需要测试端到端的同步流程

## 二、测试策略

为了应对上述挑战，我们采用以下测试策略：

### 1. 分层测试

将测试分为以下几层：

- **单元测试**：测试各个模块的功能，如配置加载、数据转换等
- **集成测试**：测试模块间的交互，如API调用与数据处理的结合
- **端到端测试**：测试完整的同步流程
- **性能测试**：测试在大数据量下的性能表现

### 2. 模拟(Mock)机制

实现API模拟机制，使得在没有实际API的情况下也能进行测试：

- 模拟云内API响应
- 模拟行内API响应
- 支持部分模拟（例如只模拟行内API，使用真实的云内API）
- 记录API调用，便于验证调用是否正确

### 3. 环境配置

支持不同环境的配置：

- 开发环境(dev)：使用真实的云内API，模拟行内API
- 测试环境(test)：可以使用全模拟或部分模拟
- 生产环境(prod)：使用真实的API，但可以进行只读测试

### 4. 测试数据管理

创建和管理测试数据：

- 为每个API端点创建标准的模拟响应数据
- 支持从文件加载测试数据
- 提供工具从真实API响应生成测试数据
- 维护测试数据版本

### 5. 快速验证工具

提供快速验证工具：

- 配置验证工具：验证配置文件的正确性
- 连接测试工具：验证API连接是否正常
- 模型验证工具：验证模型配置是否正确
- 字段映射验证工具：验证字段映射是否正确

## 三、测试架构设计

### 1. 目录结构

```
cmdb_sync/
├── config/
│   ├── base_config.yaml       # 基础配置
│   ├── environments/          # 环境特定配置
│   │   ├── dev.yaml           # 开发环境配置
│   │   ├── test.yaml          # 测试环境配置
│   │   └── prod.yaml          # 生产环境配置
│   ├── endpoints/             # API端点配置
│   │   ├── cloud_side.yaml    # 云内API端点配置
│   │   └── internal_side.yaml # 行内API端点配置
│   ├── transformers/          # 通用转换函数
│   │   └── common.yaml        # 通用转换函数配置
│   └── models/                # 模型配置目录
│       └── pc_virtual_machine.yaml  # PC虚拟机模型配置
├── src/
│   ├── main.py                # 主程序
│   ├── config_loader.py       # 配置加载器
│   ├── api_client.py          # API客户端
│   └── ...
└── tests/
    ├── mock_data/             # 模拟数据
    │   ├── cloud_side/        # 云内API模拟数据
    │   │   ├── get_vms.json   # 虚拟机数据
    │   │   ├── get_volumes.json # 卷数据
    │   │   └── ...
    │   └── internal_side/     # 行内API模拟数据
    │       ├── get_ci.json    # 配置项数据
    │       └── ...
    ├── unit/                  # 单元测试
    │   ├── test_config_loader.py # 配置加载器测试
    │   ├── test_api_client.py # API客户端测试
    │   └── ...
    ├── integration/           # 集成测试
    │   ├── test_sync_pc_virtual_machine.py # PC虚拟机同步测试
    │   └── ...
    ├── performance/           # 性能测试
    │   └── test_sync_performance.py # 同步性能测试
    ├── conftest.py            # pytest配置和通用fixture
    ├── mock_api_client.py     # 模拟API客户端
    └── run_tests.py           # 测试运行脚本
```

### 2. 环境配置

环境配置文件示例(environments/dev.yaml)：

```yaml
# 开发环境配置
environment: "dev"

# 模拟设置
mock:
  enabled: true
  cloud_side: false  # 使用真实的云内API
  internal_side: true  # 模拟行内API
  data_dir: "./tests/mock_data"

# API配置覆盖
apis:
  cloud_side:
    base_url: "https://dev-cloud-cmdb-api.example.com"
  internal_side:
    base_url: "https://mock-internal-cmdb-api"  # 模拟URL，不会实际使用
```

### 3. 模拟API客户端

模拟API客户端实现(mock_api_client.py)：

```python
import json
import os
from typing import Dict, Any, List

class MockAPIClient:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.mock_config = config.get('base', {}).get('mock', {})
        self.mock_enabled = self.mock_config.get('enabled', False)
        self.mock_data_dir = self.mock_config.get('data_dir', './tests/mock_data')
        self.calls = []  # 记录API调用
    
    def call(self, api_name: str, endpoint: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """调用API，如果启用了模拟，则返回模拟数据"""
        # 记录调用
        self.calls.append({
            'api_name': api_name,
            'endpoint': endpoint,
            'params': params
        })
        
        # 检查是否应该模拟此API
        should_mock = self.mock_enabled and self.mock_config.get(api_name, False)
        
        if should_mock:
            # 从文件加载模拟数据
            mock_file = os.path.join(self.mock_data_dir, api_name, f"{endpoint.replace('/', '_')}.json")
            try:
                with open(mock_file, 'r', encoding='utf-8') as f:
                    mock_data = json.load(f)
                return mock_data
            except Exception as e:
                raise Exception(f"Failed to load mock data from {mock_file}: {e}")
        else:
            # 调用真实API
            # 这里实现真实API调用逻辑
            pass
    
    def get_calls(self) -> List[Dict[str, Any]]:
        """获取记录的API调用"""
        return self.calls
    
    def clear_calls(self):
        """清除记录的API调用"""
        self.calls = []
```

### 4. 测试运行脚本

测试运行脚本实现(run_tests.py)：

```python
import argparse
import pytest
import os
import sys

def main():
    parser = argparse.ArgumentParser(description='运行CMDB同步工具测试')
    parser.add_argument('--env', default='dev', choices=['dev', 'test', 'prod'], help='环境')
    parser.add_argument('--mock', action='store_true', help='启用模拟')
    parser.add_argument('--unit', action='store_true', help='运行单元测试')
    parser.add_argument('--integration', action='store_true', help='运行集成测试')
    parser.add_argument('--performance', action='store_true', help='运行性能测试')
    parser.add_argument('--model', help='指定要测试的模型')
    parser.add_argument('--report', action='store_true', help='生成测试报告')
    args = parser.parse_args()
    
    # 设置环境变量
    os.environ['CMDB_SYNC_ENV'] = args.env
    if args.mock:
        os.environ['CMDB_SYNC_MOCK'] = 'true'
    
    # 构建pytest参数
    pytest_args = []
    
    # 添加测试目录
    if args.unit:
        pytest_args.append('tests/unit')
    if args.integration:
        pytest_args.append('tests/integration')
    if args.performance:
        pytest_args.append('tests/performance')
    
    # 如果没有指定任何测试类型，运行所有测试
    if not (args.unit or args.integration or args.performance):
        pytest_args.append('tests')
    
    # 添加模型过滤
    if args.model:
        pytest_args.append(f'-k {args.model}')
    
    # 添加报告参数
    if args.report:
        pytest_args.append('--html=report.html')
    
    # 运行测试
    sys.exit(pytest.main(pytest_args))

if __name__ == '__main__':
    main()
```

## 四、测试用例设计

### 1. 单元测试

#### 配置加载器测试

```python
def test_config_loader_loads_base_config():
    """测试配置加载器能够正确加载基础配置"""
    config_loader = ConfigLoader('./config/base_config.yaml')
    config = config_loader.load_config()
    assert 'base' in config
    assert 'global' in config['base']

def test_config_loader_loads_models():
    """测试配置加载器能够正确加载模型配置"""
    config_loader = ConfigLoader('./config/base_config.yaml')
    config = config_loader.load_config()
    assert 'models' in config
    assert 'pc_virtual_machine' in config['models']
```

#### API客户端测试

```python
def test_api_client_calls_endpoint():
    """测试API客户端能够正确调用端点"""
    config = {
        'base': {
            'apis': {
                'cloud_side': {
                    'base_url': 'https://example.com',
                    'auth': {'type': 'none'}
                }
            },
            'mock': {
                'enabled': True,
                'cloud_side': True,
                'data_dir': './tests/mock_data'
            }
        },
        'endpoints': {
            'cloud_side': {
                'get_vms': '/api/v1/vms'
            }
        }
    }
    client = MockAPIClient(config)
    result = client.call('cloud_side', 'get_vms')
    assert result is not None
    calls = client.get_calls()
    assert len(calls) == 1
    assert calls[0]['api_name'] == 'cloud_side'
    assert calls[0]['endpoint'] == 'get_vms'
```

#### 数据转换测试

```python
def test_transform_datetime():
    """测试日期时间转换函数"""
    from transformers import format_datetime
    result = format_datetime('2023-04-01T12:34:56Z')
    assert result == '2023/04/01'

def test_transform_status():
    """测试状态映射转换函数"""
    from transformers import map_status
    assert map_status('ACTIVE') == '00'
    assert map_status('SHUTOFF') == '01'
    assert map_status('UNKNOWN') == '04'  # 默认值
```

### 2. 集成测试

#### PC虚拟机同步测试

```python
def test_sync_pc_virtual_machine():
    """测试PC虚拟机同步流程"""
    # 设置模拟环境
    os.environ['CMDB_SYNC_ENV'] = 'test'
    os.environ['CMDB_SYNC_MOCK'] = 'true'
    
    # 加载配置
    config_loader = ConfigLoader('./config/base_config.yaml')
    config = config_loader.load_config()
    
    # 创建模拟API客户端
    api_client = MockAPIClient(config)
    
    # 执行同步
    model_id = 'pc_virtual_machine'
    model_config = config['models'][model_id]
    result = sync_model(model_id, model_config, config, api_client)
    
    # 验证结果
    assert result['success'] is True
    assert result['synced_count'] > 0
    
    # 验证API调用
    calls = api_client.get_calls()
    assert any(call['endpoint'] == 'get_vms' for call in calls)
    assert any(call['endpoint'] == 'get_volumes' for call in calls)
    assert any(call['endpoint'] == 'update_ci' for call in calls)
```

### 3. 性能测试

```python
def test_sync_performance_with_large_dataset():
    """测试大数据量下的同步性能"""
    # 设置模拟环境，使用大数据集
    os.environ['CMDB_SYNC_ENV'] = 'performance'
    os.environ['CMDB_SYNC_MOCK'] = 'true'
    
    # 加载配置
    config_loader = ConfigLoader('./config/base_config.yaml')
    config = config_loader.load_config()
    
    # 创建模拟API客户端
    api_client = MockAPIClient(config)
    
    # 记录开始时间
    start_time = time.time()
    
    # 执行同步
    model_id = 'pc_virtual_machine'
    model_config = config['models'][model_id]
    result = sync_model(model_id, model_config, config, api_client)
    
    # 记录结束时间
    end_time = time.time()
    duration = end_time - start_time
    
    # 验证性能
    assert duration < 60  # 同步应该在60秒内完成
    assert result['synced_count'] >= 1000  # 应该能同步至少1000条记录
```

## 五、测试数据准备

### 1. 模拟数据生成

提供工具从真实API响应生成模拟数据：

```python
def generate_mock_data(api_name, endpoint, params=None, output_dir='./tests/mock_data'):
    """从真实API响应生成模拟数据"""
    # 创建API客户端
    client = APIClient(load_config())
    
    # 调用真实API
    response = client.call(api_name, endpoint, params)
    
    # 确保输出目录存在
    os.makedirs(os.path.join(output_dir, api_name), exist_ok=True)
    
    # 保存响应数据
    output_file = os.path.join(output_dir, api_name, f"{endpoint.replace('/', '_')}.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(response, f, ensure_ascii=False, indent=2)
    
    print(f"Generated mock data: {output_file}")
```

### 2. 标准测试数据集

为每个模型创建标准的测试数据集，包括：

- 基本数据集：包含基本字段和关系
- 边界数据集：包含边界值和特殊情况
- 错误数据集：包含错误数据，用于测试错误处理

## 六、快速验证工具

### 1. 配置验证工具

```python
def validate_config(config_path='./config/base_config.yaml'):
    """验证配置文件的正确性"""
    try:
        # 加载配置
        config_loader = ConfigLoader(config_path)
        config = config_loader.load_config()
        
        # 验证基础配置
        validate_base_config(config['base'])
        
        # 验证模型配置
        for model_id, model_config in config['models'].items():
            validate_model_config(model_id, model_config)
        
        print("配置验证通过")
        return True
    except Exception as e:
        print(f"配置验证失败: {e}")
        return False
```

### 2. 连接测试工具

```python
def test_connection(api_name, config_path='./config/base_config.yaml'):
    """测试API连接是否正常"""
    try:
        # 加载配置
        config_loader = ConfigLoader(config_path)
        config = config_loader.load_config()
        
        # 创建API客户端
        client = APIClient(config)
        
        # 测试连接
        response = client.test_connection(api_name)
        
        print(f"连接测试成功: {api_name}")
        return True
    except Exception as e:
        print(f"连接测试失败: {api_name}, 错误: {e}")
        return False
```

### 3. 模型验证工具

```python
def validate_model(model_id, config_path='./config/base_config.yaml'):
    """验证模型配置是否正确"""
    try:
        # 加载配置
        config_loader = ConfigLoader(config_path)
        config = config_loader.load_config()
        
        # 获取模型配置
        model_config = config['models'].get(model_id)
        if not model_config:
            raise ValueError(f"模型配置不存在: {model_id}")
        
        # 验证模型配置
        validate_model_config(model_id, model_config)
        
        # 验证字段映射
        validate_field_mappings(model_id, model_config)
        
        print(f"模型验证通过: {model_id}")
        return True
    except Exception as e:
        print(f"模型验证失败: {model_id}, 错误: {e}")
        return False
```

## 七、测试环境设置

### 1. 开发环境

在开发环境中，我们使用以下设置：

- 使用真实的云内API
- 模拟行内API
- 使用本地缓存
- 启用详细日志

### 2. 测试环境

在测试环境中，我们使用以下设置：

- 可以选择使用真实API或模拟API
- 使用测试数据库
- 启用性能监控

### 3. 生产环境

在生产环境中，我们使用以下设置：

- 使用真实API
- 可以进行只读测试
- 禁用详细日志
- 启用错误告警

## 八、测试执行流程

### 1. 本地开发测试流程

1. 设置开发环境配置
2. 准备模拟数据
3. 运行单元测试
4. 运行集成测试
5. 验证结果

示例命令：
```bash
# 运行所有单元测试
python tests/run_tests.py --env dev --mock --unit

# 运行特定模型的集成测试
python tests/run_tests.py --env dev --mock --integration --model pc_virtual_machine

# 验证配置
python tools/validate_config.py
```

### 2. 现场测试流程

1. 设置现场环境配置
2. 测试API连接
3. 验证配置
4. 运行单元测试
5. 运行集成测试
6. 验证结果

示例命令：
```bash
# 测试API连接
python tools/test_connection.py --api cloud_side
python tools/test_connection.py --api internal_side

# 验证模型配置
python tools/validate_model.py --model pc_virtual_machine

# 运行集成测试
python tests/run_tests.py --env prod --integration --model pc_virtual_machine
```

## 九、测试报告

测试运行后，生成测试报告，包含以下内容：

- 测试摘要：测试总数、通过数、失败数
- 测试详情：每个测试的结果和错误信息
- 性能指标：执行时间、内存使用等
- 覆盖率报告：代码覆盖率统计

示例命令：
```bash
# 生成HTML测试报告
python tests/run_tests.py --env dev --mock --unit --report

# 生成覆盖率报告
python tests/run_tests.py --env dev --mock --unit --coverage
```

## 十、持续集成

将测试集成到CI/CD流程中：

1. 每次提交代码时运行单元测试
2. 每次合并请求时运行集成测试
3. 定期运行性能测试
4. 自动生成测试报告

## 十一、总结

本测试方案提供了一个全面的测试策略，能够在不同环境下进行测试，并且可以选择性地模拟API响应。通过这种方式，我们可以：

1. 在开发环境中快速验证功能，即使无法访问行内API
2. 在现场环境中快速验证配置和连接
3. 确保各个模块的功能正确性
4. 验证端到端的同步流程
5. 监控性能和资源使用

这种测试策略将大大提高开发效率，减少现场问题，确保系统的可靠性和稳定性。
