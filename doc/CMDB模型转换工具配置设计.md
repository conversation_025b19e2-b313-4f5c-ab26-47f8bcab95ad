# CMDB模型转换工具配置设计

根据需求，我们需要设计一个配置化的模型同步工具，用于周期性调用两边接口获取资源模型数据并缓存，支持配置单向字段同步，处理几十个模型的同步，处理字段关联性，并能处理需要调用其他接口获取的字段。

为了提高可维护性和便于定位问题，我们将基础配置和模型配置分开存放。

## 一、目录结构设计

```
cmdb_sync/
├── config/
│   ├── base_config.yaml       # 基础配置
│   ├── endpoints/             # API端点配置
│   │   ├── cloud_side.yaml    # 云内API端点配置
│   │   └── internal_side.yaml # 行内API端点配置
│   ├── transformers/          # 通用转换函数
│   │   ├── common.yaml        # 通用转换函数配置
│   │   └── ...
│   └── models/                # 模型配置目录
│       ├── pc_virtual_machine.yaml  # PC虚拟机模型配置
│       ├── physical_server.yaml     # 物理服务器模型配置
│       └── ...
├── cache/                     # 缓存目录
├── logs/                      # 日志目录
└── src/                       # 源代码目录
    ├── main.py                # 主程序
    ├── config_loader.py       # 配置加载器
    ├── api_client.py          # API客户端
    └── ...
```

## 二、配置文件设计

### 1. 基础配置文件 (base_config.yaml)

基础配置文件包含全局设置、API基本配置、缓存配置、日志配置等。

```yaml
# 全局配置
global:
  cache_dir: "./cache"
  log_dir: "./logs"
  models_dir: "./config/models"
  endpoints_dir: "./config/endpoints"
  transformers_dir: "./config/transformers"
  default_sync_interval: 3600  # 默认同步间隔（秒）
  max_retries: 3               # 最大重试次数
  retry_interval: 5            # 重试间隔（秒）

# API基本配置
apis:
  cloud_side:
    base_url: "https://cloud-cmdb-api.example.com"
    auth:
      type: "token"
      token_url: "/auth/token"
      username: "${CLOUD_USERNAME}"
      password: "${CLOUD_PASSWORD}"
      token_expiry: 3600  # Token过期时间（秒）
    headers:
      Content-Type: "application/json"
      Accept: "application/json"
    timeout: 30  # 请求超时时间（秒）

  internal_side:
    base_url: "https://internal-cmdb-api.example.com"
    auth:
      type: "basic"
      username: "${INTERNAL_USERNAME}"
      password: "${INTERNAL_PASSWORD}"
    headers:
      Content-Type: "application/json"
      Accept: "application/json"
    timeout: 30  # 请求超时时间（秒）

# 缓存配置
cache:
  type: "file"  # file或database
  file:
    format: "json"
    path: "./cache"
  database:
    type: "sqlite"
    path: "./cache/cmdb_cache.db"
  expiry: 86400  # 缓存过期时间（秒）
  cleanup_interval: 3600  # 缓存清理间隔（秒）

# 增量同步配置
incremental_sync:
  enabled: true  # 是否启用增量同步
  default_interval: 3600  # 默认增量同步间隔（秒）
  max_history_days: 7  # 最大历史数据保留天数
  retry_on_failure: true  # 同步失败时是否重试
  max_retries: 3  # 最大重试次数
  full_sync_interval: 86400  # 全量同步间隔（秒），用于确保数据一致性
  timezone: "Asia/Shanghai"  # 时区设置
  initial_sync:
    mode: "full"  # 首次同步模式：full（全量）或incremental（增量）
    start_time: "2023-01-01 00:00:00"  # 首次增量同步的起始时间
  cache:
    last_sync_time_key: "last_sync_time_{model_id}"  # 上次同步时间的缓存键模板
  conflict_resolution:
    strategy: "latest_wins"  # 冲突解决策略：latest_wins（最新胜出）、source_wins（源系统胜出）、target_wins（目标系统胜出）
  deletion_detection:
    enabled: true  # 是否启用删除检测
    method: "full_comparison"  # 删除检测方法：full_comparison（全量比对）、soft_delete（软删除标记）

# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file:
    enabled: true
    path: "./logs/cmdb_sync.log"
    max_size: 10485760  # 10MB
    backup_count: 5
  console:
    enabled: true

# 监控配置
monitoring:
  enabled: true
  metrics:
    - name: "sync_success_rate"
      type: "gauge"
      description: "同步成功率"
    - name: "sync_duration"
      type: "histogram"
      description: "同步耗时"
    - name: "api_call_count"
      type: "counter"
      description: "API调用次数"
    - name: "sync_data_count"  # 同步数据量
      type: "counter"
      description: "同步数据量"
    - name: "sync_failure_count"  # 同步失败次数
      type: "counter"
      description: "同步失败次数"

# 告警配置
alerting:
  enabled: true
  channels:
    email:
      enabled: true
      smtp_server: "smtp.example.com"
      smtp_port: 587
      username: "${SMTP_USERNAME}"
      password: "${SMTP_PASSWORD}"
      from_address: "<EMAIL>"
      to_addresses: ["<EMAIL>"]
  alerts:
    - name: "sync_failure"  # 同步失败告警
      condition: "sync_failure_count > 3"  # 告警条件
      channels: ["email", "sms"]  # 告警渠道
```

### 2. API端点配置 (endpoints/cloud_side.yaml)

API端点配置文件包含特定API的端点定义。

```yaml
# 云内API端点配置
endpoints:
  get_vms: "/api/v1/vms"
  get_volumes: "/api/v1/volumes"
  get_flavors: "/api/v1/flavors"
  get_hosts: "/api/v1/hosts"
  get_networks: "/api/v1/networks"
  get_ports: "/api/v1/ports"
  get_service_ips: "/api/v1/service_ips"
```

### 3. API端点配置 (endpoints/internal_side.yaml)

```yaml
# 行内API端点配置
endpoints:
  get_ci: "/cmdb/api/class/getCommonDynamicData"
  update_ci: "/cmdb/api/class/saveOrUpdateCIData"
  get_relations: "/cmdb/api/relation/getRelations"
  create_relation: "/cmdb/api/relation/createRelation"
```

### 4. 通用转换函数配置 (transformers/common.yaml)

通用转换函数配置文件包含可被多个模型共用的转换函数。

```yaml
# 通用转换函数配置
transformers:
  format_datetime:
    type: "python"
    code: |
      def format_datetime(dt_str):
          from datetime import datetime
          if not dt_str:
              return ""
          try:
              dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
              return dt.strftime('%Y/%m/%d')
          except:
              return dt_str

  format_ip_info:
    type: "python"
    code: |
      def format_ip_info(vm, related):
          ips = []
          if vm.get("ipAddress"):
              ips.append(f"私网IP: {vm['ipAddress']}")
          if vm.get("floatingIp"):
              ips.append(f"公网IP: {vm['floatingIp']}")
          return ", ".join(ips)

  map_status:
    type: "mapping"
    mapping:
      "ACTIVE": "00"
      "SHUTOFF": "01"
      "SUSPENDED": "02"
      "DELETED": "03"
      "default": "04"
```

### 5. 模型配置文件 (models/pc_virtual_machine.yaml)

每个模型有一个单独的配置文件，包含模型的基本信息、云内侧配置、行内侧配置、字段映射配置和模型特有的转换函数。

### 6. 增量同步配置

增量同步配置用于定义如何通过时间戳字段进行增量数据同步，减少数据传输量和处理时间。

#### 6.1 基础配置文件中的增量同步配置

在基础配置文件 (base_config.yaml) 中，可以添加全局的增量同步配置：

```yaml
# 增量同步配置
incremental_sync:
  enabled: true  # 是否启用增量同步
  default_interval: 3600  # 默认增量同步间隔（秒）
  max_history_days: 7  # 最大历史数据保留天数
  retry_on_failure: true  # 同步失败时是否重试
  max_retries: 3  # 最大重试次数
  full_sync_interval: 86400  # 全量同步间隔（秒），用于确保数据一致性
  timezone: "Asia/Shanghai"  # 时区设置
  initial_sync:
    mode: "full"  # 首次同步模式：full（全量）或incremental（增量）
    start_time: "2023-01-01 00:00:00"  # 首次增量同步的起始时间
  cache:
    last_sync_time_key: "last_sync_time_{model_id}"  # 上次同步时间的缓存键模板
  conflict_resolution:
    strategy: "latest_wins"  # 冲突解决策略：latest_wins（最新胜出）、source_wins（源系统胜出）、target_wins（目标系统胜出）
  deletion_detection:
    enabled: true  # 是否启用删除检测
    method: "full_comparison"  # 删除检测方法：full_comparison（全量比对）、soft_delete（软删除标记）
```

#### 6.2 模型配置文件中的增量同步配置

在模型配置文件 (models/xxx.yaml) 中，可以添加模型特定的增量同步配置：

```yaml
# 增量同步配置
incremental_sync:
  enabled: true  # 是否启用该模型的增量同步
  interval: 1800  # 模型特定的增量同步间隔（秒）
  full_sync_interval: 86400  # 模型特定的全量同步间隔（秒）

  # 云内侧增量同步配置
  cloud_side:
    timestamp_field: "last_Modified"  # 时间戳字段名
    timestamp_format: "epoch_millis"  # 时间戳格式（毫秒级时间戳）
    query_param: "condition"  # 查询参数名
    query_format: "{\"constraint\":[{\"simple\":{\"name\":\"last_Modified\",\"value\":\"{last_sync_time}\",\"operator\":\"greater\"}}]}"  # 查询格式
    time_buffer: 60  # 时间缓冲（秒），避免因时间精度问题漏掉数据
    index_field: "id"  # 索引字段，用于唯一标识记录

  # 行内侧增量同步配置
  internal_side:
    timestamp_field: "beginDate"  # 时间戳字段名
    timestamp_format: "yyyy-MM-dd HH:mm:ss"  # 时间戳格式
    query_param: "beginDate"  # 查询参数名
    query_format: "{last_sync_time}"  # 查询格式
    time_buffer: 60  # 时间缓冲（秒），避免因时间精度问题漏掉数据
    index_field: "Id"  # 索引字段，用于唯一标识记录
```

#### 6.3 增量同步字段说明

1. **华为CMDB的lastmodify字段**：
   - 字段名：last_Modified
   - 类型：毫秒级时间戳（例如：1496321202457）
   - 用途：记录资源实例的最后修改时间，可用于增量查询最近更新的数据
   - 查询方式：通过condition参数构造查询条件，例如：`{"constraint":[{"simple":{"name":"last_Modified","value":"1496321202457","operator":"greater"}}]}`

2. **行内CMDB的begindate字段**：
   - 字段名：beginDate
   - 类型：格式化的日期时间字符串（例如："2023-08-07 16:43:52"）
   - 用途：指定查询的起始时间，只返回该时间之后更新的数据
   - 查询方式：直接作为请求参数传递，例如：`"beginDate": "2023-08-07 16:43:52"`

#### 6.4 增量同步实现建议

1. **时间格式转换**：
   - 在同步过程中，需要进行时间格式转换，确保两个系统使用的时间格式兼容
   - 可以使用转换函数将毫秒级时间戳转换为格式化的日期时间字符串，反之亦然

2. **时区处理**：
   - 确保两个系统使用相同的时区，或者在配置中指定时区转换规则
   - 默认使用"Asia/Shanghai"时区

3. **缓存上次同步时间**：
   - 在缓存中保存上次同步的时间，以便下次增量同步时使用
   - 缓存键可以使用模板，例如："last_sync_time_{model_id}"

4. **首次同步处理**：
   - 首次同步时没有上次同步时间，可以选择进行全量同步或者指定一个起始时间
   - 可以在配置中指定首次同步模式和起始时间

5. **同步失败处理**：
   - 如果同步失败，可以配置重试机制，并且不更新上次同步时间
   - 可以设置最大重试次数和重试间隔

6. **数据一致性保证**：
   - 增量同步可能会导致数据不一致，可以配置定期进行全量同步来确保数据一致性
   - 可以设置全量同步的间隔时间

7. **冲突解决**：
   - 在增量同步过程中，可能会出现数据冲突，需要定义冲突解决策略
   - 可以选择以最新的数据为准，或者以源系统或目标系统的数据为准

8. **删除数据处理**：
   - 增量同步通常只能获取新增和更新的数据，需要有机制来处理删除的数据
   - 可以通过全量比对或者软删除标记来检测删除的数据

9. **性能优化**：
   - 增量同步可以减少数据传输量和处理时间，但也需要考虑查询条件的性能影响
   - 可以使用索引字段来提高查询性能

10. **监控和告警**：
    - 监控增量同步的执行情况，包括同步时间、同步数据量、失败次数等
    - 在出现异常时发出告警，支持多种告警渠道

```yaml
# PC虚拟机模型配置
model_id: "pc_virtual_machine"
model_name: "PC虚拟机"
description: "PC虚拟机模型同步配置"
sync_interval: 1800  # 模型特定的同步间隔（秒）
batch_size: 100      # 批量处理大小
enabled: true        # 是否启用该模型同步

# 增量同步配置
incremental_sync:
  enabled: true  # 是否启用该模型的增量同步
  interval: 1800  # 模型特定的增量同步间隔（秒）
  full_sync_interval: 86400  # 模型特定的全量同步间隔（秒）

  # 云内侧增量同步配置
  cloud_side:
    timestamp_field: "last_Modified"  # 时间戳字段名
    timestamp_format: "epoch_millis"  # 时间戳格式（毫秒级时间戳）
    query_param: "condition"  # 查询参数名
    query_format: "{\"constraint\":[{\"simple\":{\"name\":\"last_Modified\",\"value\":\"{last_sync_time}\",\"operator\":\"greater\"}}]}"  # 查询格式
    time_buffer: 60  # 时间缓冲（秒），避免因时间精度问题漏掉数据
    index_field: "id"  # 索引字段，用于唯一标识记录

  # 行内侧增量同步配置
  internal_side:
    timestamp_field: "beginDate"  # 时间戳字段名
    timestamp_format: "yyyy-MM-dd HH:mm:ss"  # 时间戳格式
    query_param: "beginDate"  # 查询参数名
    query_format: "{last_sync_time}"  # 查询格式
    time_buffer: 60  # 时间缓冲（秒），避免因时间精度问题漏掉数据
    index_field: "Id"  # 索引字段，用于唯一标识记录

# 云内侧配置
cloud_side:
  primary_model: "vm"
  primary_endpoint: "get_vms"
  primary_key: "id"
  related_models:
    - model: "volume"
      endpoint: "get_volumes"
      join_key: "vmId"
      foreign_key: "id"
      relation_type: "one_to_many"
    - model: "flavor"
      endpoint: "get_flavors"
      join_key: "flavorId"
      foreign_key: "id"
      relation_type: "one_to_one"
    - model: "host"
      endpoint: "get_hosts"
      join_key: "hostId"
      foreign_key: "id"
      relation_type: "one_to_one"

# 行内侧配置
internal_side:
  model: "PCVirtualHost"
  primary_key: "Code"
  get_endpoint: "get_ci"
  update_endpoint: "update_ci"
  get_params:
    className: "PCVirtualHost"
    itemNames: []
    attribute: []
  update_params:
    className: "PCVirtualHost"

# 字段映射配置
field_mappings:
  - internal_field: "Description"
    cloud_field: "name"
    sync_direction: "cloud_to_internal"
    transform: null
    required: true

  - internal_field: "Code"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "lambda vm: f'PCVirtualHost_{vm.get(\"floatingIp\", vm.get(\"ipAddress\", \"\"))}'"
    required: true

  - internal_field: "Notes"
    cloud_field: "remark"
    sync_direction: "cloud_to_internal"
    transform: null
    required: false

  - internal_field: "CreatedTime"
    cloud_field: "createdAt"
    sync_direction: "cloud_to_internal"
    transform: "format_datetime"
    required: true

  - internal_field: "HostRunType"
    cloud_field: "status"
    sync_direction: "cloud_to_internal"
    transform: "map_status"
    required: true

  - internal_field: "PrdIPAddr"
    cloud_field: "floatingIp"
    sync_direction: "cloud_to_internal"
    transform: null
    required: false

  - internal_field: "OS"
    cloud_field: "osType"
    sync_direction: "cloud_to_internal"
    transform: null
    required: false

  - internal_field: "DiskCapacity"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "lambda vm, related: sum([vol.get('size', 0) for vol in related.get('volume', [])]) + related.get('flavor', {}).get('diskSize', 0)"
    required: false

  - internal_field: "CPUCoreNumber"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "lambda vm, related: related.get('flavor', {}).get('vcpuSize', 0)"
    required: false

  - internal_field: "Memory"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "lambda vm, related: related.get('flavor', {}).get('ramSize', 0)"
    required: false

  - internal_field: "HostedServer"
    cloud_field: "hostId"
    sync_direction: "cloud_to_internal"
    transform: "get_host_name"
    required: false

  - internal_field: "IPInfo"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "format_ip_info"
    required: false

  - internal_field: "ManageIPAddr"
    cloud_field: "ipAddress"
    sync_direction: "cloud_to_internal"
    transform: null
    required: false

  - internal_field: "ServiceIPAddr"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_service_ip"
    required: false

  - internal_field: "PrivateIPAddr"
    cloud_field: "ipAddress"
    sync_direction: "cloud_to_internal"
    transform: null
    required: false

  - internal_field: "ApplicationSystem"
    cloud_field: "custom"
    sync_direction: "cloud_to_internal"
    transform: "get_application_system"
    required: false
    api_call:
      api: "internal_side"
      endpoint: "get_relations"
      params:
        sourceClassName: "PCVirtualHost"
        sourceItemName: "{Code}"
        targetClassName: "ApplicationSystem"
      result_path: "data[0].targetItemName"

  - internal_field: "LastModifiedTime"
    cloud_field: "last_Modified"
    sync_direction: "cloud_to_internal"
    transform: "format_timestamp"
    required: false

# 模型特有的转换函数
transformers:
  get_host_name:
    type: "python"
    code: |
      def get_host_name(host_id, related):
          host = related.get('host', {})
          return host.get('name', host_id) if host else host_id

  get_service_ip:
    type: "api_call"
    api: "cloud_side"
    endpoint: "get_service_ips"
    params:
      vm_id: "{id}"
    result_path: "data.serviceIp"

  get_application_system:
    type: "api_call"
    api: "internal_side"
    endpoint: "get_relations"
    params:
      sourceClassName: "PCVirtualHost"
      sourceItemName: "{Code}"
      targetClassName: "ApplicationSystem"
    result_path: "data[0].targetItemName"

  format_timestamp:
    type: "python"
    code: |
      def format_timestamp(timestamp):
          from datetime import datetime
          if not timestamp:
              return ""
          try:
              dt = datetime.fromtimestamp(timestamp / 1000.0)
              return dt.strftime('%Y-%m-%d %H:%M:%S')
          except:
              return timestamp
```

## 三、配置加载机制

配置加载器需要实现以下功能：

1. 加载基础配置文件
2. 加载API端点配置文件
3. 加载通用转换函数配置文件
4. 加载所有模型配置文件
5. 将它们组合成一个完整的配置对象

```python
import os
import yaml
import logging
from typing import Dict, Any, List

class ConfigLoader:
    def __init__(self, base_config_path: str):
        self.base_config_path = base_config_path
        self.base_config = {}
        self.endpoints = {}
        self.transformers = {}
        self.models = {}
        self.logger = logging.getLogger(__name__)

    def load_config(self) -> Dict[str, Any]:
        """加载所有配置并组合成一个完整的配置对象"""
        self._load_base_config()
        self._load_endpoints()
        self._load_transformers()
        self._load_models()

        # 组合配置
        config = {
            "base": self.base_config,
            "endpoints": self.endpoints,
            "transformers": self.transformers,
            "models": self.models
        }

        return config

    def _load_base_config(self):
        """加载基础配置文件"""
        try:
            with open(self.base_config_path, 'r', encoding='utf-8') as f:
                self.base_config = yaml.safe_load(f)
            self.logger.info(f"Loaded base config from {self.base_config_path}")
        except Exception as e:
            self.logger.error(f"Failed to load base config: {e}")
            raise

    def _load_endpoints(self):
        """加载API端点配置文件"""
        endpoints_dir = self.base_config.get('global', {}).get('endpoints_dir', './config/endpoints')

        for api_name in ['cloud_side', 'internal_side']:
            endpoint_path = os.path.join(endpoints_dir, f"{api_name}.yaml")
            try:
                with open(endpoint_path, 'r', encoding='utf-8') as f:
                    self.endpoints[api_name] = yaml.safe_load(f).get('endpoints', {})
                self.logger.info(f"Loaded endpoints for {api_name} from {endpoint_path}")
            except Exception as e:
                self.logger.error(f"Failed to load endpoints for {api_name}: {e}")
                self.endpoints[api_name] = {}

    def _load_transformers(self):
        """加载通用转换函数配置文件"""
        transformers_dir = self.base_config.get('global', {}).get('transformers_dir', './config/transformers')

        for file_name in os.listdir(transformers_dir):
            if file_name.endswith('.yaml'):
                transformer_path = os.path.join(transformers_dir, file_name)
                try:
                    with open(transformer_path, 'r', encoding='utf-8') as f:
                        transformers = yaml.safe_load(f).get('transformers', {})
                        self.transformers.update(transformers)
                    self.logger.info(f"Loaded transformers from {transformer_path}")
                except Exception as e:
                    self.logger.error(f"Failed to load transformers from {transformer_path}: {e}")

    def _load_models(self):
        """加载所有模型配置文件"""
        models_dir = self.base_config.get('global', {}).get('models_dir', './config/models')

        for file_name in os.listdir(models_dir):
            if file_name.endswith('.yaml'):
                model_path = os.path.join(models_dir, file_name)
                try:
                    with open(model_path, 'r', encoding='utf-8') as f:
                        model_config = yaml.safe_load(f)
                        model_id = model_config.get('model_id')
                        if model_id:
                            # 添加模型特有的转换函数到通用转换函数中，并添加前缀避免冲突
                            model_transformers = model_config.get('transformers', {})
                            for name, func in model_transformers.items():
                                prefixed_name = f"{model_id}_{name}"
                                self.transformers[prefixed_name] = func
                                # 更新模型配置中的转换函数引用
                                for mapping in model_config.get('field_mappings', []):
                                    if mapping.get('transform') == name:
                                        mapping['transform'] = prefixed_name

                            # 移除模型配置中的转换函数部分，避免重复
                            model_config.pop('transformers', None)

                            self.models[model_id] = model_config
                            self.logger.info(f"Loaded model config for {model_id} from {model_path}")
                        else:
                            self.logger.warning(f"Model config in {model_path} has no model_id, skipping")
                except Exception as e:
                    self.logger.error(f"Failed to load model config from {model_path}: {e}")
```

## 四、配置使用示例

以下是如何使用配置的示例代码：

```python
def main():
    # 初始化配置加载器
    config_loader = ConfigLoader('./config/base_config.yaml')

    # 加载所有配置
    config = config_loader.load_config()

    # 获取基础配置
    base_config = config['base']

    # 获取API端点配置
    cloud_endpoints = config['endpoints']['cloud_side']
    internal_endpoints = config['endpoints']['internal_side']

    # 获取转换函数
    transformers = config['transformers']

    # 获取所有模型配置
    models = config['models']

    # 遍历所有模型，执行同步
    for model_id, model_config in models.items():
        if model_config.get('enabled', True):
            sync_model(model_id, model_config, base_config, transformers)

def sync_model(model_id, model_config, base_config, transformers):
    # 实现模型同步逻辑
    pass
```

## 五、配置设计优势

1. **模块化**：基础配置和模型配置分开，便于管理和维护
2. **可扩展性**：可以轻松添加新的模型配置，而不影响现有配置
3. **可维护性**：每个模型有独立的配置文件，便于定位问题和修改
4. **灵活性**：支持模型特有的转换函数和通用转换函数
5. **可重用性**：通用转换函数可以被多个模型共用
6. **配置驱动**：通过配置文件定义模型同步行为，无需修改代码
7. **增量同步**：支持通过时间戳字段进行增量同步，减少数据传输量和处理时间

## 六、配置管理建议

1. **版本控制**：将配置文件纳入版本控制系统，跟踪配置变更
2. **配置验证**：实现配置验证机制，确保配置文件格式和内容正确
3. **配置热加载**：支持配置热加载，无需重启应用即可应用配置变更
4. **配置备份**：定期备份配置文件，防止意外丢失
5. **配置文档**：为每个配置项提供详细的文档说明
6. **配置模板**：提供配置模板，便于创建新的模型配置
7. **增量同步管理**：定期检查增量同步的效果，必要时进行全量同步以确保数据一致性

## 七、同步实现流程

### 7.1 全量获取流程

全量获取是系统启动时执行的一次性初始化过程，用于建立数据基础。具体流程如下：

#### 7.1.1 启动时全量获取

- 系统启动后，首先执行一次全量数据获取作为初始化
- 全量获取仅在系统启动时执行一次，不会定期重复执行
- 全量获取完成后，后续所有同步都使用增量方式进行

#### 7.1.2 云内侧全量获取

- 调用华为CMDB API获取所有模型数据
- 不添加`last_Modified`过滤条件，获取所有数据
- 示例API调用：
  ```
  GET /rest/cmdb/v1/instances/{className}?pageSize={batch_size}&pageNo={page_no}
  ```
- 使用分页机制处理大量数据，页大小由`batch_size`配置决定

#### 7.1.3 行内侧全量获取

- 调用行内CMDB API获取所有模型数据
- 不添加`beginDate`参数，获取所有数据
- 示例API调用：
  ```
  POST /cmdb/api/class/getCommonDynamicData
  {
    "className": "PCVirtualHost",
    "itemNames": "...",
    "pageNum": "1",
    "pageSize": "100"
  }
  ```
- 使用分页机制处理大量数据，页大小由`batch_size`配置决定

### 7.2 级联结构处理

全量获取完成后，数据将形成级联结构，确保模型之间的关联关系得到正确处理。

#### 7.2.1 模型依赖关系

- 模型之间存在依赖关系，在模型配置中通过`related_models`定义：
  ```yaml
  related_models:
    - model: "volume"
      endpoint: "get_volumes"
      join_key: "vmId"
      foreign_key: "id"
      relation_type: "one_to_many"
    - model: "flavor"
      endpoint: "get_flavors"
      join_key: "flavorId"
      foreign_key: "id"
      relation_type: "one_to_one"
  ```
- 支持一对一、一对多等多种关系类型
- 通过`join_key`和`foreign_key`定义关联字段

#### 7.2.2 同步顺序

- 系统根据模型依赖关系确定同步顺序
- 先同步基础模型（如物理服务器、网络设备等）
- 再同步依赖于基础模型的高级模型（如虚拟机、应用系统等）
- 对于循环依赖，使用多次迭代的方式处理

#### 7.2.3 级联数据组装

- 获取主模型数据后，再获取关联模型数据
- 根据关联关系将数据组装成完整的结构
- 例如，获取虚拟机数据后，再获取其关联的卷、规格、宿主机等数据
- 组装后的数据包含主模型和所有关联模型的信息，形成完整的级联结构

### 7.3 同步流程示例代码

```python
def initialize_sync(config):
    """系统启动时执行的初始化同步"""
    # 获取所有模型配置
    models = config['models']

    # 确定模型同步顺序
    model_order = determine_model_order(models)

    # 按顺序执行全量同步
    for model_id in model_order:
        model_config = models[model_id]
        if model_config.get('enabled', True):
            # 执行全量同步
            full_sync_model(model_id, model_config, config['base'], config['transformers'])

    # 初始化完成后，设置标志，后续使用增量同步
    set_initialization_completed(True)

def full_sync_model(model_id, model_config, base_config, transformers):
    """执行模型全量同步"""
    # 获取云内侧所有数据
    cloud_data = get_all_cloud_data(model_config)

    # 获取行内侧所有数据
    internal_data = get_all_internal_data(model_config)

    # 获取关联模型数据
    related_data = {}
    for related_model in model_config['cloud_side'].get('related_models', []):
        related_model_data = get_related_model_data(related_model, cloud_data)
        related_data[related_model['model']] = related_model_data

    # 组装级联数据
    assembled_data = []
    for item in cloud_data:
        # 为每个主模型项目添加关联数据
        item_with_related = {
            'primary': item,
            'related': {}
        }

        # 添加关联模型数据
        for related_model in model_config['cloud_side'].get('related_models', []):
            model_name = related_model['model']
            join_key = related_model['join_key']
            foreign_key = related_model['foreign_key']
            relation_type = related_model['relation_type']

            if relation_type == 'one_to_one':
                # 一对一关系
                related_item = next((r for r in related_data[model_name]
                                    if r[foreign_key] == item[join_key]), None)
                item_with_related['related'][model_name] = related_item
            elif relation_type == 'one_to_many':
                # 一对多关系
                related_items = [r for r in related_data[model_name]
                                if r[foreign_key] == item[join_key]]
                item_with_related['related'][model_name] = related_items

        assembled_data.append(item_with_related)

    # 处理数据并同步
    process_and_sync_data(assembled_data, internal_data, model_config, transformers)

    # 记录同步时间，用于后续增量同步
    cache_key = base_config['incremental_sync']['cache']['last_sync_time_key'].format(model_id=model_id)
    update_cache(cache_key, current_time())

def get_all_cloud_data(model_config):
    """获取云内侧所有数据"""
    all_data = []
    page_no = 1
    batch_size = model_config.get('batch_size', 100)

    while True:
        # 调用API获取当前页数据
        endpoint = model_config['cloud_side']['primary_endpoint']
        params = {
            'pageSize': batch_size,
            'pageNo': page_no
        }

        response = call_cloud_api(endpoint, params)
        data = response.get('objList', [])

        if not data:
            break

        all_data.extend(data)

        # 检查是否还有下一页
        total_pages = response.get('totalPageNo', 1)
        if page_no >= total_pages:
            break

        page_no += 1

    return all_data

def get_all_internal_data(model_config):
    """获取行内侧所有数据"""
    all_data = []
    page_num = 1
    batch_size = model_config.get('batch_size', 100)

    while True:
        # 调用API获取当前页数据
        endpoint = model_config['internal_side']['get_endpoint']
        params = {
            'className': model_config['internal_side']['model'],
            'itemNames': model_config['internal_side']['get_params'].get('itemNames', []),
            'attribute': model_config['internal_side']['get_params'].get('attribute', []),
            'pageNum': str(page_num),
            'pageSize': str(batch_size)
        }

        response = call_internal_api(endpoint, params)
        data = response.get('data', [])

        if not data:
            break

        all_data.extend(data)

        # 检查是否还有下一页
        total_num = response.get('totalNum', 0)
        if page_num * batch_size >= total_num:
            break

        page_num += 1

    return all_data
```

### 7.4 增量同步流程

在完成初始全量同步后，系统将使用增量同步方式更新数据：

1. 使用上次同步时间作为基准，只获取新增或修改的数据
2. 对于云内侧，使用`last_Modified`字段过滤数据
3. 对于行内侧，使用`beginDate`参数过滤数据
4. 增量同步也会处理级联结构，确保关联数据的一致性

通过这种方式，系统可以在启动时进行一次全量获取建立数据基础，之后通过增量同步保持数据的最新状态，同时保持级联结构的完整性。
