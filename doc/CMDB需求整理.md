# 新一代云平台CMDB需求整理

## 一、整体需求

根据《新一代云平台概要设计说明书》中的描述，CMDB系统作为数据中心的数据集成总线，主要实现以下功能：

1. 对云内、云外全栈资源进行建模和纳管
2. 与云管理平台进行集成，实现数据双向同步
3. 提供标准API接口，支持第三方系统调用

## 二、数据流向设计

### 1. 云管理平台数据同步到CMDB（云内→行内）

云管理平台需调用CMDB的API接口，同步以下数据到CMDB：

#### 1.1 云资源池数据
- 资源池信息
- 主机组信息
- 宿主机信息
- 网络网元集群信息（路由器、防火墙、交换机）
- 可用区信息

#### 1.2 云服务管理面数据
- ECS服务自身资源信息
- IMS服务自身资源信息
- EVS服务自身资源信息

#### 1.3 云资源实例数据
- ECS（弹性云服务器）实例
- IMS（镜像服务）实例
- EVS（云硬盘）实例
- EIP（弹性公网IP）实例
- ELB（弹性负载均衡）实例
- DCS（分布式缓存服务）实例
- DMS（分布式消息服务）实例
- RDS（关系型数据库）实例
- GaussDB实例

#### 1.4 应用数据
- 应用及应用组件资源信息

### 2. CMDB数据同步到云管理平台（行内→云内）

CMDB需提供API接口，用于云管理平台同步以下数据：

#### 2.1 物理设备运维数据
- 归属数据中心信息
- 房间信息
- 机柜信息
- 管理IP信息
- 维护责任人信息

#### 2.2 业务系统数据
- L1层业务系统信息（不包含L2~L4层的应用及应用组件信息）

## 三、集成对接架构

由于云内CMDB和行内CMDB数据模型存在差异，并且双方接口为标准接口但不完全匹配，因此需要设计一个转换层来进行：

1. 模型转换：将云内CMDB模型转换为行内CMDB模型，反之亦然
2. 接口适配：适配双方接口的调用方式和参数差异

### 核心组件：模型转换工具

该工具需要承担以下职责：
- 支持双向数据同步
- 执行数据模型的转换
- 适配接口的调用

## 四、技术要求

1. **数据同步时效性**：云内数据同步到行内CMDB需做到准实时同步
2. **接口标准化**：提供标准化的RESTful API接口
3. **安全性**：需要保证数据传输的安全，包括认证机制和数据加密

## 五、后续工作

1. **详细模型设计**：需要进一步设计云内和行内CMDB的详细模型映射关系
2. **接口规范制定**：需要制定详细的接口规范文档，包括接口URL、参数、返回值等
3. **同步策略确定**：确定数据同步的触发方式、频率、冲突处理等策略
4. **模型转换工具开发**：开发适配云内和行内CMDB的模型转换工具 