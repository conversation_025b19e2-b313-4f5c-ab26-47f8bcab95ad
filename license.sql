CREATE TABLE IF NOT EXISTS t_license (
    id              varchar(32)                         NOT NULL COMMENT '唯一标识',
    name            varchar(50)                         NULL COMMENT '许可证名称',
    data            text                                NOT NULL COMMENT '许可证内容',
    upload_time     timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '上传时间',
    activation_time timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '激活时间',
    type            varchar(50)                         NULL COMMENT '许可证类型',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='许可证信息表' ROW_FORMAT=DYNAMIC;