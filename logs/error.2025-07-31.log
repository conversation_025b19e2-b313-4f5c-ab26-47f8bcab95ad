2025-07-31 10:22:47,106 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2286] - ERROR: Request error occurred during password authentication for cloud_source_1: HTTPSConnectionPool(host='*************', port=26335): Max retries exceeded with url: /rest/plat/smapp/v1/sessions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000023966E4CD00>: Failed to establish a new connection: [WinError 10060] �������ӷ���һ��ʱ���û����ȷ�𸴻����ӵ�����û�з�Ӧ�����ӳ���ʧ�ܡ�'))
2025-07-31 10:23:34,201 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2286] - ERROR: Request error occurred during password authentication for cloud_source_1: HTTPSConnectionPool(host='*************', port=26335): Max retries exceeded with url: /rest/plat/smapp/v1/sessions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000023966E78E80>: Failed to establish a new connection: [WinError 10060] �������ӷ���һ��ʱ���û����ȷ�𸴻����ӵ�����û�з�Ӧ�����ӳ���ʧ�ܡ�'))
2025-07-31 10:24:21,315 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2286] - ERROR: Request error occurred during password authentication for cloud_source_1: HTTPSConnectionPool(host='*************', port=26335): Max retries exceeded with url: /rest/plat/smapp/v1/sessions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000023966E79C40>: Failed to establish a new connection: [WinError 10060] �������ӷ���һ��ʱ���û����ȷ�𸴻����ӵ�����û�з�Ӧ�����ӳ���ʧ�ܡ�'))
2025-07-31 10:25:08,381 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2286] - ERROR: Request error occurred during password authentication for cloud_source_1: HTTPSConnectionPool(host='*************', port=26335): Max retries exceeded with url: /rest/plat/smapp/v1/sessions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000023966E49730>: Failed to establish a new connection: [WinError 10060] �������ӷ���һ��ʱ���û����ȷ�𸴻����ӵ�����û�з�Ӧ�����ӳ���ʧ�ܡ�'))
2025-07-31 10:25:29,406 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1924] - ERROR: �ƶ����Ӵ����Ѵﵽ������Դ���: HTTPSConnectionPool(host='*************', port=26335): Max retries exceeded with url: /rest/cmdb/v1/instances/CLOUD_VM_NOVA?pageNo=1&pageSize=100 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000023966E49160>: Failed to establish a new connection: [WinError 10060] �������ӷ���һ��ʱ���û����ȷ�𸴻����ӵ�����û�з�Ӧ�����ӳ���ʧ�ܡ�'))
2025-07-31 10:25:29,422 - 18436-Thread-9 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\CLOUD_VM_NOVA.json
2025-07-31 10:25:34,431 - 18436-Thread-10 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\CLOUD_VM_NOVA.json
2025-07-31 10:25:39,440 - 18436-Thread-11 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\CLOUD_VM_NOVA.json
2025-07-31 10:25:44,448 - 18436-Thread-12 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\CLOUD_VM_NOVA.json
2025-07-31 10:25:44,450 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2081] - ERROR: HTTP���󣬲�������: 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-07-31 10:25:44,451 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4645] - ERROR: ��ȡ�ڲ�����ʧ��: API���÷���None�����������糬ʱ�����񲻿��û���֤ʧ�ܣ�
2025-07-31 10:25:44,455 - 18436-Thread-13 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-07-31 10:25:49,464 - 18436-Thread-14 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-07-31 10:25:54,471 - 18436-Thread-15 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-07-31 10:25:59,479 - 18436-Thread-16 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-07-31 10:25:59,482 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2081] - ERROR: HTTP���󣬲�������: 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-07-31 10:25:59,482 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4645] - ERROR: ��ȡ�ڲ�����ʧ��: API���÷���None�����������糬ʱ�����񲻿��û���֤ʧ�ܣ�
2025-07-31 10:25:59,489 - 18436-Thread-17 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-07-31 10:26:04,498 - 18436-Thread-18 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-07-31 10:26:09,507 - 18436-Thread-19 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-07-31 10:26:14,515 - 18436-Thread-20 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-07-31 10:26:14,517 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2081] - ERROR: HTTP���󣬲�������: 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-07-31 10:26:14,518 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4645] - ERROR: ��ȡ�ڲ�����ʧ��: API���÷���None�����������糬ʱ�����񲻿��û���֤ʧ�ܣ�
