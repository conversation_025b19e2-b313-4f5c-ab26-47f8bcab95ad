2025-07-31 10:05:34,636 - 16648-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:279] - INFO: ********************************************************************************
2025-07-31 10:05:34,637 - 16648-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:280] - INFO: ��־ϵͳ�ѳ�ʼ������־Ŀ¼: E:\File\repository\xycmdbsync\logs
2025-07-31 10:05:34,638 - 16648-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:281] - INFO: ��־ϵͳ�ѳ�ʼ������־��ת��СΪ: 300M
2025-07-31 10:05:34,638 - 16648-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:282] - INFO: ��־ϵͳ�ѳ�ʼ������־������СΪ50���ļ�
2025-07-31 10:05:34,638 - 16648-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:283] - INFO: ��־ϵͳ�ѳ�ʼ������ǰ��־����Ϊ��20
2025-07-31 10:05:34,639 - 16648-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:284] - INFO: ********************************************************************************
2025-07-31 10:05:34,703 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:44] - INFO: ��ʼ��������...
2025-07-31 10:05:34,748 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:69] - INFO: Loaded base config from E:\File\repository\xycmdbsync\config\base_config.yaml
2025-07-31 10:05:34,751 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:88] - INFO: Found cloud sources: source_1, source_2
2025-07-31 10:05:34,751 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for cloud_side from E:\File\repository\xycmdbsync\config\endpoints\cloud_side.yaml
2025-07-31 10:05:34,759 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for internal_side from E:\File\repository\xycmdbsync\config\endpoints\internal_side.yaml
2025-07-31 10:05:34,900 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:106] - INFO: Loaded transformers from E:\File\repository\xycmdbsync\config\transformers\common.yaml
2025-07-31 10:05:34,946 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_AZONE from E:\File\repository\xycmdbsync\config\models\cloud_azone.yaml
2025-07-31 10:05:35,089 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_BMS from E:\File\repository\xycmdbsync\config\models\cloud_bms.yaml
2025-07-31 10:05:35,159 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cce_cluster.yaml
2025-07-31 10:05:35,212 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CONTAINER from E:\File\repository\xycmdbsync\config\models\cloud_cce_container.yaml
2025-07-31 10:05:35,252 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NAMESPACE from E:\File\repository\xycmdbsync\config\models\cloud_cce_namespace.yaml
2025-07-31 10:05:35,318 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NODE from E:\File\repository\xycmdbsync\config\models\cloud_cce_node.yaml
2025-07-31 10:05:35,376 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_POD from E:\File\repository\xycmdbsync\config\models\cloud_cce_pod.yaml
2025-07-31 10:05:35,443 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_SERVICE from E:\File\repository\xycmdbsync\config\models\cloud_cce_service.yaml
2025-07-31 10:05:35,552 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudCronJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_cronjob.yaml
2025-07-31 10:05:35,662 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDaemonset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_daemonset.yaml
2025-07-31 10:05:35,779 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDeployment from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_deployment.yaml
2025-07-31 10:05:35,904 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_job.yaml
2025-07-31 10:05:36,023 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudStatefulset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_statefulset.yaml
2025-07-31 10:05:36,086 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cluster.yaml
2025-07-31 10:05:36,320 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_HOST from E:\File\repository\xycmdbsync\config\models\cloud_host.yaml
2025-07-31 10:05:36,370 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_OBS_BUCKET from E:\File\repository\xycmdbsync\config\models\cloud_obs_bucket.yaml
2025-07-31 10:05:36,420 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_SFS_SHARE from E:\File\repository\xycmdbsync\config\models\cloud_sfs_share.yaml
2025-07-31 10:05:36,538 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VM_NOVA from E:\File\repository\xycmdbsync\config\models\cloud_vm_nova.yaml
2025-07-31 10:05:36,596 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VOLUME from E:\File\repository\xycmdbsync\config\models\cloud_volume.yaml
2025-07-31 10:05:36,726 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_kafka from E:\File\repository\xycmdbsync\config\models\middleware_kafka.yaml
2025-07-31 10:05:36,780 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_nacos from E:\File\repository\xycmdbsync\config\models\middleware_nacos.yaml
2025-07-31 10:05:36,848 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_redis from E:\File\repository\xycmdbsync\config\models\middleware_redis.yaml
2025-07-31 10:05:36,982 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_rocketmq from E:\File\repository\xycmdbsync\config\models\middleware_rocket_mq.yaml
2025-07-31 10:05:37,097 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_optical_switch from E:\File\repository\xycmdbsync\config\models\network_access_optical_switch.yaml
2025-07-31 10:05:37,207 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_switch from E:\File\repository\xycmdbsync\config\models\network_access_switch.yaml
2025-07-31 10:05:37,308 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for back_bone_router from E:\File\repository\xycmdbsync\config\models\network_backbone_router.yaml
2025-07-31 10:05:37,352 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_bandwidths from E:\File\repository\xycmdbsync\config\models\network_cloud_bandwidths.yaml
2025-07-31 10:05:37,401 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_floating_ips from E:\File\repository\xycmdbsync\config\models\network_cloud_floating_ips.yaml
2025-07-31 10:05:37,444 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_network from E:\File\repository\xycmdbsync\config\models\network_cloud_network.yaml
2025-07-31 10:05:37,499 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_port from E:\File\repository\xycmdbsync\config\models\network_cloud_port.yaml
2025-07-31 10:05:37,551 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_router from E:\File\repository\xycmdbsync\config\models\network_cloud_router.yaml
2025-07-31 10:05:37,600 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_subnet from E:\File\repository\xycmdbsync\config\models\network_cloud_subnet.yaml
2025-07-31 10:05:37,710 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_optical_switch from E:\File\repository\xycmdbsync\config\models\network_core_optical_switch.yaml
2025-07-31 10:05:37,820 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_switch from E:\File\repository\xycmdbsync\config\models\network_core_switch.yaml
2025-07-31 10:05:37,932 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for network_device_port from E:\File\repository\xycmdbsync\config\models\network_device_port.yaml
2025-07-31 10:05:38,032 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall from E:\File\repository\xycmdbsync\config\models\network_fire_wall.yaml
2025-07-31 10:05:38,122 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall_policy_log from E:\File\repository\xycmdbsync\config\models\network_fire_wall_policy_log.yaml
2025-07-31 10:05:38,225 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for land_balance_device from E:\File\repository\xycmdbsync\config\models\network_land_balance_device.yaml
2025-07-31 10:05:38,263 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool.yaml
2025-07-31 10:05:38,302 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool_men from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool_men.yaml
2025-07-31 10:05:38,347 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_service from E:\File\repository\xycmdbsync\config\models\network_load_balance_service.yaml
2025-07-31 10:05:38,445 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for non_backbone_router from E:\File\repository\xycmdbsync\config\models\network_non_backbone_router.yaml
2025-07-31 10:05:38,502 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for sys_deviceLink from E:\File\repository\xycmdbsync\config\models\network_sys_devicelink.yaml
2025-07-31 10:05:38,576 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_waf_engine from E:\File\repository\xycmdbsync\config\models\secure_cloud_waf_engine.yaml
2025-07-31 10:05:38,650 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_BusinessRegion from E:\File\repository\xycmdbsync\config\models\sys_businessregion.yaml
2025-07-31 10:05:38,674 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DataCenter from E:\File\repository\xycmdbsync\config\models\sys_datacenter.yaml
2025-07-31 10:05:38,718 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DistributedStorage from E:\File\repository\xycmdbsync\config\models\sys_distributedstorage.yaml
2025-07-31 10:05:38,845 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_LUN from E:\File\repository\xycmdbsync\config\models\sys_lun.yaml
2025-07-31 10:05:38,991 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_PhysicalServer from E:\File\repository\xycmdbsync\config\models\sys_physicalserver.yaml
2025-07-31 10:05:39,033 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack from E:\File\repository\xycmdbsync\config\models\sys_rack.yaml
2025-07-31 10:05:39,076 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_IntegratedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_integratedcabinet.yaml
2025-07-31 10:05:39,116 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_ShieldedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_shieldedcabinet.yaml
2025-07-31 10:05:39,154 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_ResourcePool from E:\File\repository\xycmdbsync\config\models\sys_resourcepool.yaml
2025-07-31 10:05:39,176 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Room from E:\File\repository\xycmdbsync\config\models\sys_room.yaml
2025-07-31 10:05:39,219 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageCluster from E:\File\repository\xycmdbsync\config\models\sys_storagecluster.yaml
2025-07-31 10:05:39,267 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageNode from E:\File\repository\xycmdbsync\config\models\sys_storagenode.yaml
2025-07-31 10:05:39,326 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StoragePool from E:\File\repository\xycmdbsync\config\models\sys_storagepool.yaml
2025-07-31 10:05:39,478 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorDevice from E:\File\repository\xycmdbsync\config\models\sys_stordevice.yaml
2025-07-31 10:05:39,673 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_X86Server from E:\File\repository\xycmdbsync\config\models\sys_x86server.yaml
2025-07-31 10:05:39,673 - 16648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:50] - INFO: ���ü������
2025-07-31 10:18:30,345 - 18436-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:279] - INFO: ********************************************************************************
2025-07-31 10:18:30,345 - 18436-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:280] - INFO: ��־ϵͳ�ѳ�ʼ������־Ŀ¼: E:\File\repository\xycmdbsync\logs
2025-07-31 10:18:30,345 - 18436-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:281] - INFO: ��־ϵͳ�ѳ�ʼ������־��ת��СΪ: 300M
2025-07-31 10:18:30,346 - 18436-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:282] - INFO: ��־ϵͳ�ѳ�ʼ������־������СΪ50���ļ�
2025-07-31 10:18:30,346 - 18436-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:283] - INFO: ��־ϵͳ�ѳ�ʼ������ǰ��־����Ϊ��20
2025-07-31 10:18:30,346 - 18436-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:284] - INFO: ********************************************************************************
2025-07-31 10:18:30,400 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:44] - INFO: ��ʼ��������...
2025-07-31 10:18:30,444 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:69] - INFO: Loaded base config from E:\File\repository\xycmdbsync\config\base_config.yaml
2025-07-31 10:18:30,447 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:88] - INFO: Found cloud sources: source_1, source_2
2025-07-31 10:18:30,448 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for cloud_side from E:\File\repository\xycmdbsync\config\endpoints\cloud_side.yaml
2025-07-31 10:18:30,455 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for internal_side from E:\File\repository\xycmdbsync\config\endpoints\internal_side.yaml
2025-07-31 10:18:30,596 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:106] - INFO: Loaded transformers from E:\File\repository\xycmdbsync\config\transformers\common.yaml
2025-07-31 10:18:30,641 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_AZONE from E:\File\repository\xycmdbsync\config\models\cloud_azone.yaml
2025-07-31 10:18:30,794 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_BMS from E:\File\repository\xycmdbsync\config\models\cloud_bms.yaml
2025-07-31 10:18:30,857 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cce_cluster.yaml
2025-07-31 10:18:30,910 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CONTAINER from E:\File\repository\xycmdbsync\config\models\cloud_cce_container.yaml
2025-07-31 10:18:30,948 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NAMESPACE from E:\File\repository\xycmdbsync\config\models\cloud_cce_namespace.yaml
2025-07-31 10:18:31,014 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NODE from E:\File\repository\xycmdbsync\config\models\cloud_cce_node.yaml
2025-07-31 10:18:31,078 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_POD from E:\File\repository\xycmdbsync\config\models\cloud_cce_pod.yaml
2025-07-31 10:18:31,143 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_SERVICE from E:\File\repository\xycmdbsync\config\models\cloud_cce_service.yaml
2025-07-31 10:18:31,253 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudCronJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_cronjob.yaml
2025-07-31 10:18:31,373 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDaemonset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_daemonset.yaml
2025-07-31 10:18:31,488 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDeployment from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_deployment.yaml
2025-07-31 10:18:31,606 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_job.yaml
2025-07-31 10:18:31,717 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudStatefulset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_statefulset.yaml
2025-07-31 10:18:31,774 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cluster.yaml
2025-07-31 10:18:32,011 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_HOST from E:\File\repository\xycmdbsync\config\models\cloud_host.yaml
2025-07-31 10:18:32,068 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_OBS_BUCKET from E:\File\repository\xycmdbsync\config\models\cloud_obs_bucket.yaml
2025-07-31 10:18:32,128 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_SFS_SHARE from E:\File\repository\xycmdbsync\config\models\cloud_sfs_share.yaml
2025-07-31 10:18:32,270 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VM_NOVA from E:\File\repository\xycmdbsync\config\models\cloud_vm_nova.yaml
2025-07-31 10:18:32,322 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VOLUME from E:\File\repository\xycmdbsync\config\models\cloud_volume.yaml
2025-07-31 10:18:32,464 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_kafka from E:\File\repository\xycmdbsync\config\models\middleware_kafka.yaml
2025-07-31 10:18:32,521 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_nacos from E:\File\repository\xycmdbsync\config\models\middleware_nacos.yaml
2025-07-31 10:18:32,595 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_redis from E:\File\repository\xycmdbsync\config\models\middleware_redis.yaml
2025-07-31 10:18:32,740 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_rocketmq from E:\File\repository\xycmdbsync\config\models\middleware_rocket_mq.yaml
2025-07-31 10:18:32,863 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_optical_switch from E:\File\repository\xycmdbsync\config\models\network_access_optical_switch.yaml
2025-07-31 10:18:32,989 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_switch from E:\File\repository\xycmdbsync\config\models\network_access_switch.yaml
2025-07-31 10:18:33,093 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for back_bone_router from E:\File\repository\xycmdbsync\config\models\network_backbone_router.yaml
2025-07-31 10:18:33,133 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_bandwidths from E:\File\repository\xycmdbsync\config\models\network_cloud_bandwidths.yaml
2025-07-31 10:18:33,179 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_floating_ips from E:\File\repository\xycmdbsync\config\models\network_cloud_floating_ips.yaml
2025-07-31 10:18:33,224 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_network from E:\File\repository\xycmdbsync\config\models\network_cloud_network.yaml
2025-07-31 10:18:33,285 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_port from E:\File\repository\xycmdbsync\config\models\network_cloud_port.yaml
2025-07-31 10:18:33,340 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_router from E:\File\repository\xycmdbsync\config\models\network_cloud_router.yaml
2025-07-31 10:18:33,388 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_subnet from E:\File\repository\xycmdbsync\config\models\network_cloud_subnet.yaml
2025-07-31 10:18:33,501 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_optical_switch from E:\File\repository\xycmdbsync\config\models\network_core_optical_switch.yaml
2025-07-31 10:18:33,609 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_switch from E:\File\repository\xycmdbsync\config\models\network_core_switch.yaml
2025-07-31 10:18:33,716 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for network_device_port from E:\File\repository\xycmdbsync\config\models\network_device_port.yaml
2025-07-31 10:18:33,821 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall from E:\File\repository\xycmdbsync\config\models\network_fire_wall.yaml
2025-07-31 10:18:33,900 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall_policy_log from E:\File\repository\xycmdbsync\config\models\network_fire_wall_policy_log.yaml
2025-07-31 10:18:34,010 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for land_balance_device from E:\File\repository\xycmdbsync\config\models\network_land_balance_device.yaml
2025-07-31 10:18:34,044 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool.yaml
2025-07-31 10:18:34,080 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool_men from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool_men.yaml
2025-07-31 10:18:34,123 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_service from E:\File\repository\xycmdbsync\config\models\network_load_balance_service.yaml
2025-07-31 10:18:34,221 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for non_backbone_router from E:\File\repository\xycmdbsync\config\models\network_non_backbone_router.yaml
2025-07-31 10:18:34,281 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for sys_deviceLink from E:\File\repository\xycmdbsync\config\models\network_sys_devicelink.yaml
2025-07-31 10:18:34,361 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_waf_engine from E:\File\repository\xycmdbsync\config\models\secure_cloud_waf_engine.yaml
2025-07-31 10:18:34,447 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_BusinessRegion from E:\File\repository\xycmdbsync\config\models\sys_businessregion.yaml
2025-07-31 10:18:34,474 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DataCenter from E:\File\repository\xycmdbsync\config\models\sys_datacenter.yaml
2025-07-31 10:18:34,519 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DistributedStorage from E:\File\repository\xycmdbsync\config\models\sys_distributedstorage.yaml
2025-07-31 10:18:34,630 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_LUN from E:\File\repository\xycmdbsync\config\models\sys_lun.yaml
2025-07-31 10:18:34,775 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_PhysicalServer from E:\File\repository\xycmdbsync\config\models\sys_physicalserver.yaml
2025-07-31 10:18:34,812 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack from E:\File\repository\xycmdbsync\config\models\sys_rack.yaml
2025-07-31 10:18:34,849 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_IntegratedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_integratedcabinet.yaml
2025-07-31 10:18:34,885 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_ShieldedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_shieldedcabinet.yaml
2025-07-31 10:18:34,919 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_ResourcePool from E:\File\repository\xycmdbsync\config\models\sys_resourcepool.yaml
2025-07-31 10:18:34,939 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Room from E:\File\repository\xycmdbsync\config\models\sys_room.yaml
2025-07-31 10:18:34,979 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageCluster from E:\File\repository\xycmdbsync\config\models\sys_storagecluster.yaml
2025-07-31 10:18:35,024 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageNode from E:\File\repository\xycmdbsync\config\models\sys_storagenode.yaml
2025-07-31 10:18:35,081 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StoragePool from E:\File\repository\xycmdbsync\config\models\sys_storagepool.yaml
2025-07-31 10:18:35,231 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorDevice from E:\File\repository\xycmdbsync\config\models\sys_stordevice.yaml
2025-07-31 10:18:35,424 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_X86Server from E:\File\repository\xycmdbsync\config\models\sys_x86server.yaml
2025-07-31 10:18:35,425 - 18436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:50] - INFO: ���ü������
2025-07-31 10:18:35,612 - 18436-MainThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:45] - INFO: MySQL���ӳس�ʼ���ɹ�����С: 5
2025-07-31 10:18:35,615 - 18436-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:120] - INFO: Initialized cloud API client for source: source_1
2025-07-31 10:18:35,615 - 18436-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:120] - INFO: Initialized cloud API client for source: source_2
2025-07-31 10:18:35,616 - 18436-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:134] - INFO: Initialized cloud API client for source: source_1
2025-07-31 10:18:35,616 - 18436-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:148] - INFO: Initialized internal API client
2025-07-31 10:18:35,616 - 18436-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:210] - INFO: Loading data from cache
2025-07-31 10:18:35,630 - 18436-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-07-31 10:18:35,639 - 18436-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-07-31 10:18:35,648 - 18436-RestApiThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:47] - INFO: �����߳�: restful_api��Դ�ϱ��ӿ�.
2025-07-31 10:18:35,649 - 18436-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:46] - WARNING: ��� 5 Сʱ��������24Сʱ�����ܵ���ִ��ʱ�䲻����
2025-07-31 10:18:35,650 - 18436-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:34] - INFO: ��������ʼ�����: ���=5Сʱ, ��ʼ=1��
2025-07-31 10:18:35,650 - 18436-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:35] - INFO: ִ��ʱ���: ['01:00', '06:00', '11:00', '16:00', '21:00']
2025-07-31 10:18:35,651 - 18436-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:84] - INFO: �����߳�: async_model��ʱ����ͬ������
2025-07-31 10:18:35,651 - 18436-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:85] - INFO: ��������: ÿ5Сʱִ��һ�Σ���01:00��ʼ
2025-07-31 10:18:35,651 - 18436-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:86] - INFO: ִ��ʱ���: 01:00, 06:00, 11:00, 16:00, 21:00
2025-07-31 10:18:35,652 - 18436-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:87] - INFO: ÿ��ִ�д���: 5 ��
2025-07-31 10:18:35,652 - 18436-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-07-31 11:00:00, ���뻹�� 2485.00 �룬������...
2025-07-31 10:22:26,040 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:322] - INFO: Fetching cloud-side data for model: CLOUD_VM_NOVA
2025-07-31 10:22:26,040 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:327] - INFO: Using specified data source: source_1
2025-07-31 10:22:26,040 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:359] - INFO: Fetching cloud-side data for model: CLOUD_VM_NOVA
2025-07-31 10:22:26,041 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:376] - INFO: Fetching data for model CLOUD_VM_NOVA from source source_1
2025-07-31 10:22:47,106 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2286] - ERROR: Request error occurred during password authentication for cloud_source_1: HTTPSConnectionPool(host='*************', port=26335): Max retries exceeded with url: /rest/plat/smapp/v1/sessions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000023966E4CD00>: Failed to establish a new connection: [WinError 10060] �������ӷ���һ��ʱ���û����ȷ�𸴻����ӵ�����û�з�Ӧ�����ӳ���ʧ�ܡ�'))
2025-07-31 10:23:08,151 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1918] - WARNING: �ƶ����Ӵ��� (���� 1/4): HTTPSConnectionPool(host='*************', port=26335): Max retries exceeded with url: /rest/cmdb/v1/instances/CLOUD_VM_NOVA?pageNo=1&pageSize=100 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000023966E78430>: Failed to establish a new connection: [WinError 10060] �������ӷ���һ��ʱ���û����ȷ�𸴻����ӵ�����û�з�Ӧ�����ӳ���ʧ�ܡ�'))
2025-07-31 10:23:08,151 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1920] - INFO: ����ʧ�ܣ��ȴ� 5 �������...
2025-07-31 10:23:34,201 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2286] - ERROR: Request error occurred during password authentication for cloud_source_1: HTTPSConnectionPool(host='*************', port=26335): Max retries exceeded with url: /rest/plat/smapp/v1/sessions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000023966E78E80>: Failed to establish a new connection: [WinError 10060] �������ӷ���һ��ʱ���û����ȷ�𸴻����ӵ�����û�з�Ӧ�����ӳ���ʧ�ܡ�'))
2025-07-31 10:23:55,257 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1918] - WARNING: �ƶ����Ӵ��� (���� 2/4): HTTPSConnectionPool(host='*************', port=26335): Max retries exceeded with url: /rest/cmdb/v1/instances/CLOUD_VM_NOVA?pageNo=1&pageSize=100 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000023966E791C0>: Failed to establish a new connection: [WinError 10060] �������ӷ���һ��ʱ���û����ȷ�𸴻����ӵ�����û�з�Ӧ�����ӳ���ʧ�ܡ�'))
2025-07-31 10:23:55,257 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1920] - INFO: ����ʧ�ܣ��ȴ� 5 �������...
2025-07-31 10:24:21,315 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2286] - ERROR: Request error occurred during password authentication for cloud_source_1: HTTPSConnectionPool(host='*************', port=26335): Max retries exceeded with url: /rest/plat/smapp/v1/sessions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000023966E79C40>: Failed to establish a new connection: [WinError 10060] �������ӷ���һ��ʱ���û����ȷ�𸴻����ӵ�����û�з�Ӧ�����ӳ���ʧ�ܡ�'))
2025-07-31 10:24:42,351 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1918] - WARNING: �ƶ����Ӵ��� (���� 3/4): HTTPSConnectionPool(host='*************', port=26335): Max retries exceeded with url: /rest/cmdb/v1/instances/CLOUD_VM_NOVA?pageNo=1&pageSize=100 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000023966E78F40>: Failed to establish a new connection: [WinError 10060] �������ӷ���һ��ʱ���û����ȷ�𸴻����ӵ�����û�з�Ӧ�����ӳ���ʧ�ܡ�'))
2025-07-31 10:24:42,351 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1920] - INFO: ����ʧ�ܣ��ȴ� 5 �������...
2025-07-31 10:25:08,381 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2286] - ERROR: Request error occurred during password authentication for cloud_source_1: HTTPSConnectionPool(host='*************', port=26335): Max retries exceeded with url: /rest/plat/smapp/v1/sessions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000023966E49730>: Failed to establish a new connection: [WinError 10060] �������ӷ���һ��ʱ���û����ȷ�𸴻����ӵ�����û�з�Ӧ�����ӳ���ʧ�ܡ�'))
2025-07-31 10:25:29,405 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1918] - WARNING: �ƶ����Ӵ��� (���� 4/4): HTTPSConnectionPool(host='*************', port=26335): Max retries exceeded with url: /rest/cmdb/v1/instances/CLOUD_VM_NOVA?pageNo=1&pageSize=100 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000023966E49160>: Failed to establish a new connection: [WinError 10060] �������ӷ���һ��ʱ���û����ȷ�𸴻����ӵ�����û�з�Ӧ�����ӳ���ʧ�ܡ�'))
2025-07-31 10:25:29,406 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1924] - ERROR: �ƶ����Ӵ����Ѵﵽ������Դ���: HTTPSConnectionPool(host='*************', port=26335): Max retries exceeded with url: /rest/cmdb/v1/instances/CLOUD_VM_NOVA?pageNo=1&pageSize=100 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000023966E49160>: Failed to establish a new connection: [WinError 10060] �������ӷ���һ��ʱ���û����ȷ�𸴻����ӵ�����û�з�Ӧ�����ӳ���ʧ�ܡ�'))
2025-07-31 10:25:29,406 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:393] - WARNING: No data found for model CLOUD_VM_NOVA from source source_1, skipping
2025-07-31 10:25:29,407 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:512] - INFO: Fetching internal-side data for model: CLOUD_VM_NOVA
2025-07-31 10:25:29,407 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:539] - INFO: Fetching internal-side data for model: CLOUD_VM_NOVA
2025-07-31 10:25:29,417 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2277] - INFO: Bearer authentication successful for internal_side
2025-07-31 10:25:29,422 - 18436-Thread-9 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:308] - INFO: ��ȡ��̬����: CLOUD_VM_NOVA
2025-07-31 10:25:29,422 - 18436-Thread-9 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\CLOUD_VM_NOVA.json
2025-07-31 10:25:29,423 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2025] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-07-31 10:25:29,424 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2062] - WARNING: HTTP���� 404 (���� 1/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-07-31 10:25:29,424 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2077] - INFO: ���������󣬵ȴ� 5 �������...
2025-07-31 10:25:34,430 - 18436-Thread-10 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:308] - INFO: ��ȡ��̬����: CLOUD_VM_NOVA
2025-07-31 10:25:34,431 - 18436-Thread-10 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\CLOUD_VM_NOVA.json
2025-07-31 10:25:34,433 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2025] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-07-31 10:25:34,433 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2062] - WARNING: HTTP���� 404 (���� 2/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-07-31 10:25:34,434 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2077] - INFO: ���������󣬵ȴ� 5 �������...
2025-07-31 10:25:39,439 - 18436-Thread-11 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:308] - INFO: ��ȡ��̬����: CLOUD_VM_NOVA
2025-07-31 10:25:39,440 - 18436-Thread-11 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\CLOUD_VM_NOVA.json
2025-07-31 10:25:39,441 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2025] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-07-31 10:25:39,442 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2062] - WARNING: HTTP���� 404 (���� 3/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-07-31 10:25:39,442 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2077] - INFO: ���������󣬵ȴ� 5 �������...
2025-07-31 10:25:44,448 - 18436-Thread-12 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:308] - INFO: ��ȡ��̬����: CLOUD_VM_NOVA
2025-07-31 10:25:44,448 - 18436-Thread-12 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\CLOUD_VM_NOVA.json
2025-07-31 10:25:44,450 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2025] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-07-31 10:25:44,450 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2062] - WARNING: HTTP���� 404 (���� 4/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-07-31 10:25:44,450 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2081] - ERROR: HTTP���󣬲�������: 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-07-31 10:25:44,451 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4645] - ERROR: ��ȡ�ڲ�����ʧ��: API���÷���None�����������糬ʱ�����񲻿��û���֤ʧ�ܣ�
2025-07-31 10:25:44,455 - 18436-Thread-13 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:308] - INFO: ��ȡ��̬����: ApplicationSystem
2025-07-31 10:25:44,455 - 18436-Thread-13 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-07-31 10:25:44,456 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2025] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-07-31 10:25:44,457 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2062] - WARNING: HTTP���� 404 (���� 1/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-07-31 10:25:44,457 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2077] - INFO: ���������󣬵ȴ� 5 �������...
2025-07-31 10:25:49,463 - 18436-Thread-14 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:308] - INFO: ��ȡ��̬����: ApplicationSystem
2025-07-31 10:25:49,464 - 18436-Thread-14 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-07-31 10:25:49,465 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2025] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-07-31 10:25:49,466 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2062] - WARNING: HTTP���� 404 (���� 2/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-07-31 10:25:49,466 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2077] - INFO: ���������󣬵ȴ� 5 �������...
2025-07-31 10:25:54,470 - 18436-Thread-15 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:308] - INFO: ��ȡ��̬����: ApplicationSystem
2025-07-31 10:25:54,471 - 18436-Thread-15 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-07-31 10:25:54,472 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2025] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-07-31 10:25:54,473 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2062] - WARNING: HTTP���� 404 (���� 3/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-07-31 10:25:54,473 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2077] - INFO: ���������󣬵ȴ� 5 �������...
2025-07-31 10:25:59,479 - 18436-Thread-16 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:308] - INFO: ��ȡ��̬����: ApplicationSystem
2025-07-31 10:25:59,479 - 18436-Thread-16 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-07-31 10:25:59,481 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2025] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-07-31 10:25:59,482 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2062] - WARNING: HTTP���� 404 (���� 4/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-07-31 10:25:59,482 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2081] - ERROR: HTTP���󣬲�������: 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-07-31 10:25:59,482 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4645] - ERROR: ��ȡ�ڲ�����ʧ��: API���÷���None�����������糬ʱ�����񲻿��û���֤ʧ�ܣ�
2025-07-31 10:25:59,483 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:554] - WARNING: No data found for model CLOUD_VM_NOVA, skipping
2025-07-31 10:25:59,483 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:614] - INFO: Completed fetching internal-side data for model: CLOUD_VM_NOVA
2025-07-31 10:25:59,483 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:235] - INFO: ��ʼͬ��ģ��: CLOUD_VM_NOVA
2025-07-31 10:25:59,489 - 18436-Thread-17 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:308] - INFO: ��ȡ��̬����: ApplicationSystem
2025-07-31 10:25:59,489 - 18436-Thread-17 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-07-31 10:25:59,491 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2025] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-07-31 10:25:59,491 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2062] - WARNING: HTTP���� 404 (���� 1/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-07-31 10:25:59,492 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2077] - INFO: ���������󣬵ȴ� 5 �������...
2025-07-31 10:26:04,498 - 18436-Thread-18 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:308] - INFO: ��ȡ��̬����: ApplicationSystem
2025-07-31 10:26:04,498 - 18436-Thread-18 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-07-31 10:26:04,499 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2025] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-07-31 10:26:04,500 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2062] - WARNING: HTTP���� 404 (���� 2/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-07-31 10:26:04,500 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2077] - INFO: ���������󣬵ȴ� 5 �������...
2025-07-31 10:26:09,506 - 18436-Thread-19 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:308] - INFO: ��ȡ��̬����: ApplicationSystem
2025-07-31 10:26:09,507 - 18436-Thread-19 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-07-31 10:26:09,508 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2025] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-07-31 10:26:09,509 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2062] - WARNING: HTTP���� 404 (���� 3/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-07-31 10:26:09,509 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2077] - INFO: ���������󣬵ȴ� 5 �������...
2025-07-31 10:26:14,515 - 18436-Thread-20 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:308] - INFO: ��ȡ��̬����: ApplicationSystem
2025-07-31 10:26:14,515 - 18436-Thread-20 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-07-31 10:26:14,516 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2025] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-07-31 10:26:14,517 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2062] - WARNING: HTTP���� 404 (���� 4/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-07-31 10:26:14,517 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2081] - ERROR: HTTP���󣬲�������: 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-07-31 10:26:14,518 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4645] - ERROR: ��ȡ�ڲ�����ʧ��: API���÷���None�����������糬ʱ�����񲻿��û���֤ʧ�ܣ�
2025-07-31 10:26:14,518 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:675] - INFO: Synchronizing model: CLOUD_VM_NOVA
2025-07-31 10:26:14,518 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:688] - INFO: No historical data found for model CLOUD_VM_NOVA, using current internal data
2025-07-31 10:26:14,518 - 18436-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:691] - WARNING: No cloud data found for model CLOUD_VM_NOVA, skipping
2025-07-31 11:00:00,653 - 18436-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:153] - INFO: ****************����ִ��ͬ�� (5Сʱ���)**********************
2025-07-31 11:00:00,654 - 18436-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:139] - WARNING: ���֤Ŀ¼������: E:\File\repository\xycmdbsync\config\license
2025-07-31 11:00:00,654 - 18436-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:142] - INFO: ���Դ����ݿ��ȡ���֤��Ϣ
2025-07-31 11:00:00,708 - 18436-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:73] - INFO: Connection to MySQL DB successful
2025-07-31 11:00:00,794 - 18436-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:88] - INFO: MySQL connection is closed
2025-07-31 11:00:00,794 - 18436-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:148] - INFO: �ɹ������ݿ��ȡ���֤��Ϣ: XYCMDB
2025-07-31 11:00:00,842 - 18436-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:156] - INFO: **************License�ѹ���*******************
2025-07-31 11:00:00,842 - 18436-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-07-31 16:00:00, ���뻹�� 18000.00 �룬������...
