2025-08-05 09:49:41,170 - 8360-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:279] - INFO: ********************************************************************************
2025-08-05 09:49:41,171 - 8360-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:280] - INFO: ��־ϵͳ�ѳ�ʼ������־Ŀ¼: E:\File\repository\xycmdbsync\logs
2025-08-05 09:49:41,172 - 8360-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:281] - INFO: ��־ϵͳ�ѳ�ʼ������־��ת��СΪ: 300M
2025-08-05 09:49:41,172 - 8360-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:282] - INFO: ��־ϵͳ�ѳ�ʼ������־������СΪ50���ļ�
2025-08-05 09:49:41,173 - 8360-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:283] - INFO: ��־ϵͳ�ѳ�ʼ������ǰ��־����Ϊ��20
2025-08-05 09:49:41,173 - 8360-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:284] - INFO: ********************************************************************************
2025-08-05 09:49:41,236 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:44] - INFO: ��ʼ��������...
2025-08-05 09:49:41,280 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:69] - INFO: Loaded base config from E:\File\repository\xycmdbsync\config\base_config.yaml
2025-08-05 09:49:41,283 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:88] - INFO: Found cloud sources: source_1, source_2
2025-08-05 09:49:41,284 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for cloud_side from E:\File\repository\xycmdbsync\config\endpoints\cloud_side.yaml
2025-08-05 09:49:41,292 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for internal_side from E:\File\repository\xycmdbsync\config\endpoints\internal_side.yaml
2025-08-05 09:49:41,441 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:106] - INFO: Loaded transformers from E:\File\repository\xycmdbsync\config\transformers\common.yaml
2025-08-05 09:49:41,499 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_AZONE from E:\File\repository\xycmdbsync\config\models\cloud_azone.yaml
2025-08-05 09:49:41,664 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_BMS from E:\File\repository\xycmdbsync\config\models\cloud_bms.yaml
2025-08-05 09:49:41,740 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cce_cluster.yaml
2025-08-05 09:49:41,800 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CONTAINER from E:\File\repository\xycmdbsync\config\models\cloud_cce_container.yaml
2025-08-05 09:49:41,844 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NAMESPACE from E:\File\repository\xycmdbsync\config\models\cloud_cce_namespace.yaml
2025-08-05 09:49:41,920 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NODE from E:\File\repository\xycmdbsync\config\models\cloud_cce_node.yaml
2025-08-05 09:49:41,980 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_POD from E:\File\repository\xycmdbsync\config\models\cloud_cce_pod.yaml
2025-08-05 09:49:42,041 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_SERVICE from E:\File\repository\xycmdbsync\config\models\cloud_cce_service.yaml
2025-08-05 09:49:42,156 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudCronJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_cronjob.yaml
2025-08-05 09:49:42,270 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDaemonset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_daemonset.yaml
2025-08-05 09:49:42,383 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDeployment from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_deployment.yaml
2025-08-05 09:49:42,498 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_job.yaml
2025-08-05 09:49:42,624 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudStatefulset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_statefulset.yaml
2025-08-05 09:49:42,687 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cluster.yaml
2025-08-05 09:49:42,905 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_HOST from E:\File\repository\xycmdbsync\config\models\cloud_host.yaml
2025-08-05 09:49:42,960 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_OBS_BUCKET from E:\File\repository\xycmdbsync\config\models\cloud_obs_bucket.yaml
2025-08-05 09:49:43,011 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_SFS_SHARE from E:\File\repository\xycmdbsync\config\models\cloud_sfs_share.yaml
2025-08-05 09:49:43,131 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VM_NOVA from E:\File\repository\xycmdbsync\config\models\cloud_vm_nova.yaml
2025-08-05 09:49:43,185 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VOLUME from E:\File\repository\xycmdbsync\config\models\cloud_volume.yaml
2025-08-05 09:49:43,319 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_kafka from E:\File\repository\xycmdbsync\config\models\middleware_kafka.yaml
2025-08-05 09:49:43,377 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_nacos from E:\File\repository\xycmdbsync\config\models\middleware_nacos.yaml
2025-08-05 09:49:43,449 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_redis from E:\File\repository\xycmdbsync\config\models\middleware_redis.yaml
2025-08-05 09:49:43,596 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_rocketmq from E:\File\repository\xycmdbsync\config\models\middleware_rocket_mq.yaml
2025-08-05 09:49:43,718 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_optical_switch from E:\File\repository\xycmdbsync\config\models\network_access_optical_switch.yaml
2025-08-05 09:49:43,838 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_switch from E:\File\repository\xycmdbsync\config\models\network_access_switch.yaml
2025-08-05 09:49:43,947 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for back_bone_router from E:\File\repository\xycmdbsync\config\models\network_backbone_router.yaml
2025-08-05 09:49:43,992 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_bandwidths from E:\File\repository\xycmdbsync\config\models\network_cloud_bandwidths.yaml
2025-08-05 09:49:44,043 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_floating_ips from E:\File\repository\xycmdbsync\config\models\network_cloud_floating_ips.yaml
2025-08-05 09:49:44,098 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_network from E:\File\repository\xycmdbsync\config\models\network_cloud_network.yaml
2025-08-05 09:49:44,167 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_port from E:\File\repository\xycmdbsync\config\models\network_cloud_port.yaml
2025-08-05 09:49:44,223 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_router from E:\File\repository\xycmdbsync\config\models\network_cloud_router.yaml
2025-08-05 09:49:44,276 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_subnet from E:\File\repository\xycmdbsync\config\models\network_cloud_subnet.yaml
2025-08-05 09:49:44,392 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_optical_switch from E:\File\repository\xycmdbsync\config\models\network_core_optical_switch.yaml
2025-08-05 09:49:44,502 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_switch from E:\File\repository\xycmdbsync\config\models\network_core_switch.yaml
2025-08-05 09:49:44,638 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for network_device_port from E:\File\repository\xycmdbsync\config\models\network_device_port.yaml
2025-08-05 09:49:44,760 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall from E:\File\repository\xycmdbsync\config\models\network_fire_wall.yaml
2025-08-05 09:49:44,848 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall_policy_log from E:\File\repository\xycmdbsync\config\models\network_fire_wall_policy_log.yaml
2025-08-05 09:49:44,954 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for land_balance_device from E:\File\repository\xycmdbsync\config\models\network_land_balance_device.yaml
2025-08-05 09:49:44,999 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool.yaml
2025-08-05 09:49:45,045 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool_men from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool_men.yaml
2025-08-05 09:49:45,101 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_service from E:\File\repository\xycmdbsync\config\models\network_load_balance_service.yaml
2025-08-05 09:49:45,206 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for non_backbone_router from E:\File\repository\xycmdbsync\config\models\network_non_backbone_router.yaml
2025-08-05 09:49:45,269 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for sys_deviceLink from E:\File\repository\xycmdbsync\config\models\network_sys_devicelink.yaml
2025-08-05 09:49:45,342 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_waf_engine from E:\File\repository\xycmdbsync\config\models\secure_cloud_waf_engine.yaml
2025-08-05 09:49:45,421 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_BusinessRegion from E:\File\repository\xycmdbsync\config\models\sys_businessregion.yaml
2025-08-05 09:49:45,449 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DataCenter from E:\File\repository\xycmdbsync\config\models\sys_datacenter.yaml
2025-08-05 09:49:45,497 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DistributedStorage from E:\File\repository\xycmdbsync\config\models\sys_distributedstorage.yaml
2025-08-05 09:49:45,620 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_LUN from E:\File\repository\xycmdbsync\config\models\sys_lun.yaml
2025-08-05 09:49:45,767 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_PhysicalServer from E:\File\repository\xycmdbsync\config\models\sys_physicalserver.yaml
2025-08-05 09:49:45,804 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack from E:\File\repository\xycmdbsync\config\models\sys_rack.yaml
2025-08-05 09:49:45,842 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_IntegratedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_integratedcabinet.yaml
2025-08-05 09:49:45,879 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_ShieldedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_shieldedcabinet.yaml
2025-08-05 09:49:45,919 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_ResourcePool from E:\File\repository\xycmdbsync\config\models\sys_resourcepool.yaml
2025-08-05 09:49:45,940 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Room from E:\File\repository\xycmdbsync\config\models\sys_room.yaml
2025-08-05 09:49:45,980 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageCluster from E:\File\repository\xycmdbsync\config\models\sys_storagecluster.yaml
2025-08-05 09:49:46,032 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageNode from E:\File\repository\xycmdbsync\config\models\sys_storagenode.yaml
2025-08-05 09:49:46,088 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StoragePool from E:\File\repository\xycmdbsync\config\models\sys_storagepool.yaml
2025-08-05 09:49:46,247 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorDevice from E:\File\repository\xycmdbsync\config\models\sys_stordevice.yaml
2025-08-05 09:49:46,441 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_X86Server from E:\File\repository\xycmdbsync\config\models\sys_x86server.yaml
2025-08-05 09:49:46,442 - 8360-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:50] - INFO: ���ü������
2025-08-05 09:49:46,667 - 8360-MainThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:45] - INFO: MySQL���ӳس�ʼ���ɹ�����С: 5
2025-08-05 09:49:46,670 - 8360-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:120] - INFO: Initialized cloud API client for source: source_1
2025-08-05 09:49:46,671 - 8360-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:120] - INFO: Initialized cloud API client for source: source_2
2025-08-05 09:49:46,671 - 8360-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:134] - INFO: Initialized cloud API client for source: source_1
2025-08-05 09:49:46,671 - 8360-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:148] - INFO: Initialized internal API client
2025-08-05 09:49:46,671 - 8360-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:210] - INFO: Loading data from cache
2025-08-05 09:49:46,686 - 8360-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-08-05 09:49:46,694 - 8360-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-08-05 09:49:46,704 - 8360-RestApiThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:47] - INFO: �����߳�: restful_api��Դ�ϱ��ӿ�.
2025-08-05 09:49:46,705 - 8360-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:46] - WARNING: ��� 5 Сʱ��������24Сʱ�����ܵ���ִ��ʱ�䲻����
2025-08-05 09:49:46,706 - 8360-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:34] - INFO: ��������ʼ�����: ���=5Сʱ, ��ʼ=1��
2025-08-05 09:49:46,706 - 8360-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:35] - INFO: ִ��ʱ���: ['01:00', '06:00', '11:00', '16:00', '21:00']
2025-08-05 09:49:46,707 - 8360-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:84] - INFO: �����߳�: async_model��ʱ����ͬ������
2025-08-05 09:49:46,707 - 8360-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:85] - INFO: ��������: ÿ5Сʱִ��һ�Σ���01:00��ʼ
2025-08-05 09:49:46,707 - 8360-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:86] - INFO: ִ��ʱ���: 01:00, 06:00, 11:00, 16:00, 21:00
2025-08-05 09:49:46,708 - 8360-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:87] - INFO: ÿ��ִ�д���: 5 ��
2025-08-05 09:49:46,708 - 8360-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-05 11:00:00, ���뻹�� 4214.00 �룬������...
2025-08-05 09:50:21,513 - 9504-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:279] - INFO: ********************************************************************************
2025-08-05 09:50:21,514 - 9504-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:280] - INFO: ��־ϵͳ�ѳ�ʼ������־Ŀ¼: E:\File\repository\xycmdbsync\logs
2025-08-05 09:50:21,514 - 9504-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:281] - INFO: ��־ϵͳ�ѳ�ʼ������־��ת��СΪ: 300M
2025-08-05 09:50:21,515 - 9504-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:282] - INFO: ��־ϵͳ�ѳ�ʼ������־������СΪ50���ļ�
2025-08-05 09:50:21,515 - 9504-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:283] - INFO: ��־ϵͳ�ѳ�ʼ������ǰ��־����Ϊ��20
2025-08-05 09:50:21,515 - 9504-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:284] - INFO: ********************************************************************************
2025-08-05 09:50:21,580 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:44] - INFO: ��ʼ��������...
2025-08-05 09:50:21,629 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:69] - INFO: Loaded base config from E:\File\repository\xycmdbsync\config\base_config.yaml
2025-08-05 09:50:21,632 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:88] - INFO: Found cloud sources: source_1, source_2
2025-08-05 09:50:21,633 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for cloud_side from E:\File\repository\xycmdbsync\config\endpoints\cloud_side.yaml
2025-08-05 09:50:21,642 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for internal_side from E:\File\repository\xycmdbsync\config\endpoints\internal_side.yaml
2025-08-05 09:50:21,802 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:106] - INFO: Loaded transformers from E:\File\repository\xycmdbsync\config\transformers\common.yaml
2025-08-05 09:50:21,861 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_AZONE from E:\File\repository\xycmdbsync\config\models\cloud_azone.yaml
2025-08-05 09:50:22,015 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_BMS from E:\File\repository\xycmdbsync\config\models\cloud_bms.yaml
2025-08-05 09:50:22,083 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cce_cluster.yaml
2025-08-05 09:50:22,141 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CONTAINER from E:\File\repository\xycmdbsync\config\models\cloud_cce_container.yaml
2025-08-05 09:50:22,193 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NAMESPACE from E:\File\repository\xycmdbsync\config\models\cloud_cce_namespace.yaml
2025-08-05 09:50:22,278 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NODE from E:\File\repository\xycmdbsync\config\models\cloud_cce_node.yaml
2025-08-05 09:50:22,340 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_POD from E:\File\repository\xycmdbsync\config\models\cloud_cce_pod.yaml
2025-08-05 09:50:22,403 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_SERVICE from E:\File\repository\xycmdbsync\config\models\cloud_cce_service.yaml
2025-08-05 09:50:22,519 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudCronJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_cronjob.yaml
2025-08-05 09:50:22,651 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDaemonset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_daemonset.yaml
2025-08-05 09:50:22,777 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDeployment from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_deployment.yaml
2025-08-05 09:50:22,893 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_job.yaml
2025-08-05 09:50:23,012 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudStatefulset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_statefulset.yaml
2025-08-05 09:50:23,092 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cluster.yaml
2025-08-05 09:50:23,347 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_HOST from E:\File\repository\xycmdbsync\config\models\cloud_host.yaml
2025-08-05 09:50:23,394 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_OBS_BUCKET from E:\File\repository\xycmdbsync\config\models\cloud_obs_bucket.yaml
2025-08-05 09:50:23,443 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_SFS_SHARE from E:\File\repository\xycmdbsync\config\models\cloud_sfs_share.yaml
2025-08-05 09:50:23,570 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VM_NOVA from E:\File\repository\xycmdbsync\config\models\cloud_vm_nova.yaml
2025-08-05 09:50:23,630 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VOLUME from E:\File\repository\xycmdbsync\config\models\cloud_volume.yaml
2025-08-05 09:50:23,774 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_kafka from E:\File\repository\xycmdbsync\config\models\middleware_kafka.yaml
2025-08-05 09:50:23,838 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_nacos from E:\File\repository\xycmdbsync\config\models\middleware_nacos.yaml
2025-08-05 09:50:23,911 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_redis from E:\File\repository\xycmdbsync\config\models\middleware_redis.yaml
2025-08-05 09:50:24,065 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_rocketmq from E:\File\repository\xycmdbsync\config\models\middleware_rocket_mq.yaml
2025-08-05 09:50:24,185 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_optical_switch from E:\File\repository\xycmdbsync\config\models\network_access_optical_switch.yaml
2025-08-05 09:50:24,305 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_switch from E:\File\repository\xycmdbsync\config\models\network_access_switch.yaml
2025-08-05 09:50:24,413 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for back_bone_router from E:\File\repository\xycmdbsync\config\models\network_backbone_router.yaml
2025-08-05 09:50:24,460 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_bandwidths from E:\File\repository\xycmdbsync\config\models\network_cloud_bandwidths.yaml
2025-08-05 09:50:24,513 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_floating_ips from E:\File\repository\xycmdbsync\config\models\network_cloud_floating_ips.yaml
2025-08-05 09:50:24,571 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_network from E:\File\repository\xycmdbsync\config\models\network_cloud_network.yaml
2025-08-05 09:50:24,638 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_port from E:\File\repository\xycmdbsync\config\models\network_cloud_port.yaml
2025-08-05 09:50:24,694 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_router from E:\File\repository\xycmdbsync\config\models\network_cloud_router.yaml
2025-08-05 09:50:24,749 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_subnet from E:\File\repository\xycmdbsync\config\models\network_cloud_subnet.yaml
2025-08-05 09:50:24,868 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_optical_switch from E:\File\repository\xycmdbsync\config\models\network_core_optical_switch.yaml
2025-08-05 09:50:24,982 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_switch from E:\File\repository\xycmdbsync\config\models\network_core_switch.yaml
2025-08-05 09:50:25,097 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for network_device_port from E:\File\repository\xycmdbsync\config\models\network_device_port.yaml
2025-08-05 09:50:25,210 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall from E:\File\repository\xycmdbsync\config\models\network_fire_wall.yaml
2025-08-05 09:50:25,302 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall_policy_log from E:\File\repository\xycmdbsync\config\models\network_fire_wall_policy_log.yaml
2025-08-05 09:50:25,410 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for land_balance_device from E:\File\repository\xycmdbsync\config\models\network_land_balance_device.yaml
2025-08-05 09:50:25,457 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool.yaml
2025-08-05 09:50:25,505 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool_men from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool_men.yaml
2025-08-05 09:50:25,566 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_service from E:\File\repository\xycmdbsync\config\models\network_load_balance_service.yaml
2025-08-05 09:50:25,684 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for non_backbone_router from E:\File\repository\xycmdbsync\config\models\network_non_backbone_router.yaml
2025-08-05 09:50:25,743 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for sys_deviceLink from E:\File\repository\xycmdbsync\config\models\network_sys_devicelink.yaml
2025-08-05 09:50:25,819 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_waf_engine from E:\File\repository\xycmdbsync\config\models\secure_cloud_waf_engine.yaml
2025-08-05 09:50:25,900 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_BusinessRegion from E:\File\repository\xycmdbsync\config\models\sys_businessregion.yaml
2025-08-05 09:50:25,927 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DataCenter from E:\File\repository\xycmdbsync\config\models\sys_datacenter.yaml
2025-08-05 09:50:25,977 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DistributedStorage from E:\File\repository\xycmdbsync\config\models\sys_distributedstorage.yaml
2025-08-05 09:50:26,096 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_LUN from E:\File\repository\xycmdbsync\config\models\sys_lun.yaml
2025-08-05 09:50:26,249 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_PhysicalServer from E:\File\repository\xycmdbsync\config\models\sys_physicalserver.yaml
2025-08-05 09:50:26,288 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack from E:\File\repository\xycmdbsync\config\models\sys_rack.yaml
2025-08-05 09:50:26,329 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_IntegratedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_integratedcabinet.yaml
2025-08-05 09:50:26,366 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_ShieldedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_shieldedcabinet.yaml
2025-08-05 09:50:26,406 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_ResourcePool from E:\File\repository\xycmdbsync\config\models\sys_resourcepool.yaml
2025-08-05 09:50:26,427 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Room from E:\File\repository\xycmdbsync\config\models\sys_room.yaml
2025-08-05 09:50:26,468 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageCluster from E:\File\repository\xycmdbsync\config\models\sys_storagecluster.yaml
2025-08-05 09:50:26,515 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageNode from E:\File\repository\xycmdbsync\config\models\sys_storagenode.yaml
2025-08-05 09:50:26,578 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StoragePool from E:\File\repository\xycmdbsync\config\models\sys_storagepool.yaml
2025-08-05 09:50:26,770 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorDevice from E:\File\repository\xycmdbsync\config\models\sys_stordevice.yaml
2025-08-05 09:50:26,990 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_X86Server from E:\File\repository\xycmdbsync\config\models\sys_x86server.yaml
2025-08-05 09:50:26,990 - 9504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:50] - INFO: ���ü������
2025-08-05 09:50:27,098 - 9504-MainThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:45] - INFO: MySQL���ӳس�ʼ���ɹ�����С: 5
2025-08-05 09:50:27,101 - 9504-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:120] - INFO: Initialized cloud API client for source: source_1
2025-08-05 09:50:27,101 - 9504-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:120] - INFO: Initialized cloud API client for source: source_2
2025-08-05 09:50:27,101 - 9504-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:134] - INFO: Initialized cloud API client for source: source_1
2025-08-05 09:50:27,102 - 9504-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:148] - INFO: Initialized internal API client
2025-08-05 09:50:27,102 - 9504-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:210] - INFO: Loading data from cache
2025-08-05 09:50:27,114 - 9504-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-08-05 09:50:27,123 - 9504-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-08-05 09:50:27,134 - 9504-RestApiThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:47] - INFO: �����߳�: restful_api��Դ�ϱ��ӿ�.
2025-08-05 09:50:27,134 - 9504-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:34] - INFO: ��������ʼ�����: ���=24Сʱ, ��ʼ=0��
2025-08-05 09:50:27,135 - 9504-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:35] - INFO: ִ��ʱ���: ['00:00']
2025-08-05 09:50:27,136 - 9504-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:84] - INFO: �����߳�: async_model��ʱ����ͬ������
2025-08-05 09:50:27,136 - 9504-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:85] - INFO: ��������: ÿ24Сʱִ��һ�Σ���00:00��ʼ
2025-08-05 09:50:27,136 - 9504-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:86] - INFO: ִ��ʱ���: 00:00
2025-08-05 09:50:27,136 - 9504-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:87] - INFO: ÿ��ִ�д���: 1 ��
2025-08-05 09:50:27,137 - 9504-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-06 00:00:00, ���뻹�� 50973.00 �룬������...
2025-08-05 17:59:46,041 - 724-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:279] - INFO: ********************************************************************************
2025-08-05 17:59:46,043 - 724-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:280] - INFO: ��־ϵͳ�ѳ�ʼ������־Ŀ¼: E:\File\repository\xycmdbsync\logs
2025-08-05 17:59:46,045 - 724-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:281] - INFO: ��־ϵͳ�ѳ�ʼ������־��ת��СΪ: 300M
2025-08-05 17:59:46,046 - 724-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:282] - INFO: ��־ϵͳ�ѳ�ʼ������־������СΪ50���ļ�
2025-08-05 17:59:46,047 - 724-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:283] - INFO: ��־ϵͳ�ѳ�ʼ������ǰ��־����Ϊ��20
2025-08-05 17:59:46,048 - 724-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:284] - INFO: ********************************************************************************
2025-08-05 17:59:46,178 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:44] - INFO: ��ʼ��������...
2025-08-05 17:59:46,242 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:69] - INFO: Loaded base config from E:\File\repository\xycmdbsync\config\base_config.yaml
2025-08-05 17:59:46,247 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:88] - INFO: Found cloud sources: source_1, source_2
2025-08-05 17:59:46,248 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for cloud_side from E:\File\repository\xycmdbsync\config\endpoints\cloud_side.yaml
2025-08-05 17:59:46,260 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for internal_side from E:\File\repository\xycmdbsync\config\endpoints\internal_side.yaml
2025-08-05 17:59:46,445 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:106] - INFO: Loaded transformers from E:\File\repository\xycmdbsync\config\transformers\common.yaml
2025-08-05 17:59:46,505 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_AZONE from E:\File\repository\xycmdbsync\config\models\cloud_azone.yaml
2025-08-05 17:59:46,692 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_BMS from E:\File\repository\xycmdbsync\config\models\cloud_bms.yaml
2025-08-05 17:59:46,773 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cce_cluster.yaml
2025-08-05 17:59:46,839 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CONTAINER from E:\File\repository\xycmdbsync\config\models\cloud_cce_container.yaml
2025-08-05 17:59:46,889 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NAMESPACE from E:\File\repository\xycmdbsync\config\models\cloud_cce_namespace.yaml
2025-08-05 17:59:46,972 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NODE from E:\File\repository\xycmdbsync\config\models\cloud_cce_node.yaml
2025-08-05 17:59:47,045 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_POD from E:\File\repository\xycmdbsync\config\models\cloud_cce_pod.yaml
2025-08-05 17:59:47,117 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_SERVICE from E:\File\repository\xycmdbsync\config\models\cloud_cce_service.yaml
2025-08-05 17:59:47,240 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudCronJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_cronjob.yaml
2025-08-05 17:59:47,385 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDaemonset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_daemonset.yaml
2025-08-05 17:59:47,536 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDeployment from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_deployment.yaml
2025-08-05 17:59:47,699 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_job.yaml
2025-08-05 17:59:47,852 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudStatefulset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_statefulset.yaml
2025-08-05 17:59:47,931 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cluster.yaml
2025-08-05 17:59:48,204 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_HOST from E:\File\repository\xycmdbsync\config\models\cloud_host.yaml
2025-08-05 17:59:48,258 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_OBS_BUCKET from E:\File\repository\xycmdbsync\config\models\cloud_obs_bucket.yaml
2025-08-05 17:59:48,313 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_SFS_SHARE from E:\File\repository\xycmdbsync\config\models\cloud_sfs_share.yaml
2025-08-05 17:59:48,449 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VM_NOVA from E:\File\repository\xycmdbsync\config\models\cloud_vm_nova.yaml
2025-08-05 17:59:48,512 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VOLUME from E:\File\repository\xycmdbsync\config\models\cloud_volume.yaml
2025-08-05 17:59:48,678 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_kafka from E:\File\repository\xycmdbsync\config\models\middleware_kafka.yaml
2025-08-05 17:59:48,743 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_nacos from E:\File\repository\xycmdbsync\config\models\middleware_nacos.yaml
2025-08-05 17:59:48,822 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_redis from E:\File\repository\xycmdbsync\config\models\middleware_redis.yaml
2025-08-05 17:59:48,987 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_rocketmq from E:\File\repository\xycmdbsync\config\models\middleware_rocket_mq.yaml
2025-08-05 17:59:49,115 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_optical_switch from E:\File\repository\xycmdbsync\config\models\network_access_optical_switch.yaml
2025-08-05 17:59:49,295 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_switch from E:\File\repository\xycmdbsync\config\models\network_access_switch.yaml
2025-08-05 17:59:49,439 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for back_bone_router from E:\File\repository\xycmdbsync\config\models\network_backbone_router.yaml
2025-08-05 17:59:49,498 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_bandwidths from E:\File\repository\xycmdbsync\config\models\network_cloud_bandwidths.yaml
2025-08-05 17:59:49,563 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_floating_ips from E:\File\repository\xycmdbsync\config\models\network_cloud_floating_ips.yaml
2025-08-05 17:59:49,624 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_network from E:\File\repository\xycmdbsync\config\models\network_cloud_network.yaml
2025-08-05 17:59:49,696 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_port from E:\File\repository\xycmdbsync\config\models\network_cloud_port.yaml
2025-08-05 17:59:49,764 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_router from E:\File\repository\xycmdbsync\config\models\network_cloud_router.yaml
2025-08-05 17:59:49,825 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_subnet from E:\File\repository\xycmdbsync\config\models\network_cloud_subnet.yaml
2025-08-05 17:59:49,966 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_optical_switch from E:\File\repository\xycmdbsync\config\models\network_core_optical_switch.yaml
2025-08-05 17:59:50,110 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_switch from E:\File\repository\xycmdbsync\config\models\network_core_switch.yaml
2025-08-05 17:59:50,243 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for network_device_port from E:\File\repository\xycmdbsync\config\models\network_device_port.yaml
2025-08-05 17:59:50,392 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall from E:\File\repository\xycmdbsync\config\models\network_fire_wall.yaml
2025-08-05 17:59:50,491 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall_policy_log from E:\File\repository\xycmdbsync\config\models\network_fire_wall_policy_log.yaml
2025-08-05 17:59:50,622 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for land_balance_device from E:\File\repository\xycmdbsync\config\models\network_land_balance_device.yaml
2025-08-05 17:59:50,675 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool.yaml
2025-08-05 17:59:50,728 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool_men from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool_men.yaml
2025-08-05 17:59:50,795 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_service from E:\File\repository\xycmdbsync\config\models\network_load_balance_service.yaml
2025-08-05 17:59:50,922 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for non_backbone_router from E:\File\repository\xycmdbsync\config\models\network_non_backbone_router.yaml
2025-08-05 17:59:50,989 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for sys_deviceLink from E:\File\repository\xycmdbsync\config\models\network_sys_devicelink.yaml
2025-08-05 17:59:51,073 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_waf_engine from E:\File\repository\xycmdbsync\config\models\secure_cloud_waf_engine.yaml
2025-08-05 17:59:51,168 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_BusinessRegion from E:\File\repository\xycmdbsync\config\models\sys_businessregion.yaml
2025-08-05 17:59:51,200 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DataCenter from E:\File\repository\xycmdbsync\config\models\sys_datacenter.yaml
2025-08-05 17:59:51,256 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DistributedStorage from E:\File\repository\xycmdbsync\config\models\sys_distributedstorage.yaml
2025-08-05 17:59:51,398 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_LUN from E:\File\repository\xycmdbsync\config\models\sys_lun.yaml
2025-08-05 17:59:51,561 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_PhysicalServer from E:\File\repository\xycmdbsync\config\models\sys_physicalserver.yaml
2025-08-05 17:59:51,602 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack from E:\File\repository\xycmdbsync\config\models\sys_rack.yaml
2025-08-05 17:59:51,649 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_IntegratedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_integratedcabinet.yaml
2025-08-05 17:59:51,692 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_ShieldedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_shieldedcabinet.yaml
2025-08-05 17:59:51,737 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_ResourcePool from E:\File\repository\xycmdbsync\config\models\sys_resourcepool.yaml
2025-08-05 17:59:51,760 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Room from E:\File\repository\xycmdbsync\config\models\sys_room.yaml
2025-08-05 17:59:51,806 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageCluster from E:\File\repository\xycmdbsync\config\models\sys_storagecluster.yaml
2025-08-05 17:59:51,860 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageNode from E:\File\repository\xycmdbsync\config\models\sys_storagenode.yaml
2025-08-05 17:59:51,922 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StoragePool from E:\File\repository\xycmdbsync\config\models\sys_storagepool.yaml
2025-08-05 17:59:52,103 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorDevice from E:\File\repository\xycmdbsync\config\models\sys_stordevice.yaml
2025-08-05 17:59:52,329 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_X86Server from E:\File\repository\xycmdbsync\config\models\sys_x86server.yaml
2025-08-05 17:59:52,330 - 724-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:50] - INFO: ���ü������
2025-08-05 17:59:52,582 - 724-MainThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:45] - INFO: MySQL���ӳس�ʼ���ɹ�����С: 5
2025-08-05 17:59:52,587 - 724-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_1
2025-08-05 17:59:52,587 - 724-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_2
2025-08-05 17:59:52,588 - 724-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:136] - INFO: Initialized cloud API client for source: source_1
2025-08-05 17:59:52,588 - 724-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:150] - INFO: Initialized internal API client
2025-08-05 17:59:52,588 - 724-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:212] - INFO: Loading data from cache
2025-08-05 17:59:52,604 - 724-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-08-05 17:59:52,613 - 724-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-08-05 17:59:52,626 - 724-RestApiThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:47] - INFO: �����߳�: restful_api��Դ�ϱ��ӿ�.
2025-08-05 17:59:52,627 - 724-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:34] - INFO: ��������ʼ�����: ���=24Сʱ, ��ʼ=0��
2025-08-05 17:59:52,628 - 724-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:35] - INFO: ִ��ʱ���: ['00:00']
2025-08-05 17:59:52,628 - 724-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:84] - INFO: �����߳�: async_model��ʱ����ͬ������
2025-08-05 17:59:52,629 - 724-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:85] - INFO: ��������: ÿ24Сʱִ��һ�Σ���00:00��ʼ
2025-08-05 17:59:52,629 - 724-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:86] - INFO: ִ��ʱ���: 00:00
2025-08-05 17:59:52,629 - 724-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:87] - INFO: ÿ��ִ�д���: 1 ��
2025-08-05 17:59:52,630 - 724-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-06 00:00:00, ���뻹�� 21608.00 �룬������...
2025-08-05 18:02:40,370 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:324] - INFO: Fetching cloud-side data for model: CLOUD_VM_NOVA
2025-08-05 18:02:40,371 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:329] - INFO: Using specified data source: source_1
2025-08-05 18:02:40,371 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:361] - INFO: Fetching cloud-side data for model: CLOUD_VM_NOVA
2025-08-05 18:02:40,371 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:378] - INFO: Fetching data for model CLOUD_VM_NOVA from source source_1
2025-08-05 18:02:40,768 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2373] - INFO: Password authentication successful for cloud_source_1
2025-08-05 18:02:41,155 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.38s, ״̬��: 200
2025-08-05 18:02:41,378 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.22s, ״̬��: 200
2025-08-05 18:02:41,612 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.23s, ״̬��: 200
2025-08-05 18:02:41,614 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:266] - INFO: Processing related models for CLOUD_VM_NOVA from source source_1
2025-08-05 18:02:41,614 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:277] - INFO: Processing related model R_Volume_MountOn_VM using method relation_table
2025-08-05 18:02:41,614 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2872] - INFO: Fetching relation table data for model CLOUD_VM_NOVA, related model R_Volume_MountOn_VM from source source_1
2025-08-05 18:02:41,773 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.16s, ״̬��: 200
2025-08-05 18:02:41,924 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.15s, ״̬��: 200
2025-08-05 18:02:42,022 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-05 18:02:42,127 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-05 18:02:42,220 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-05 18:02:42,341 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-05 18:02:42,436 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-05 18:02:42,549 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-05 18:02:42,642 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-05 18:02:42,742 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-05 18:02:42,839 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-05 18:02:42,947 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-05 18:02:43,047 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-05 18:02:43,141 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-05 18:02:43,238 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-05 18:02:43,344 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-05 18:02:43,472 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-05 18:02:43,580 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-05 18:02:43,698 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-05 18:02:43,817 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-05 18:02:43,909 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-05 18:02:44,000 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-05 18:02:44,124 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-05 18:02:44,215 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-05 18:02:44,305 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-05 18:02:44,409 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-05 18:02:44,514 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-05 18:02:44,613 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-05 18:02:44,719 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-05 18:02:44,823 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-05 18:02:44,923 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-05 18:02:45,012 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-05 18:02:45,150 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.14s, ״̬��: 200
2025-08-05 18:02:45,282 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-05 18:02:45,419 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.14s, ״̬��: 200
2025-08-05 18:02:45,556 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.14s, ״̬��: 200
2025-08-05 18:02:45,751 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.19s, ״̬��: 200
2025-08-05 18:02:45,930 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.18s, ״̬��: 200
2025-08-05 18:02:46,082 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.15s, ״̬��: 200
2025-08-05 18:02:46,250 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.17s, ״̬��: 200
2025-08-05 18:02:46,381 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-05 18:02:46,496 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-05 18:02:46,608 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-05 18:02:46,647 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:285] - INFO: Fetched 366 records for related model R_Volume_MountOn_VM
2025-08-05 18:02:46,647 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:277] - INFO: Processing related model M_VMUsesFlavor using method relation_table
2025-08-05 18:02:46,647 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2872] - INFO: Fetching relation table data for model CLOUD_VM_NOVA, related model M_VMUsesFlavor from source source_1
2025-08-05 18:02:46,763 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-05 18:02:46,863 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-05 18:02:46,989 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-05 18:02:47,109 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-05 18:02:47,216 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-05 18:02:47,389 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.17s, ״̬��: 200
2025-08-05 18:02:47,600 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.21s, ״̬��: 200
2025-08-05 18:02:47,713 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-05 18:02:47,820 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-05 18:02:47,948 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-05 18:02:48,093 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.14s, ״̬��: 200
2025-08-05 18:02:48,196 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-05 18:02:48,308 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-05 18:02:48,431 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-05 18:02:48,540 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-05 18:02:48,709 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.17s, ״̬��: 200
2025-08-05 18:02:48,851 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.14s, ״̬��: 200
2025-08-05 18:02:48,856 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:285] - INFO: Fetched 89 records for related model M_VMUsesFlavor
2025-08-05 18:02:48,857 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:411] - INFO: Completed fetching cloud-side data for model: CLOUD_VM_NOVA from source source_1 (found 283 records)
2025-08-05 18:02:48,858 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:420] - ERROR: Error fetching cloud-side data for model CLOUD_VM_NOVA: rawData illegal
2025-08-05 18:02:48,862 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:514] - INFO: Fetching internal-side data for model: CLOUD_VM_NOVA
2025-08-05 18:02:48,862 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:541] - INFO: Fetching internal-side data for model: CLOUD_VM_NOVA
2025-08-05 18:02:48,875 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2373] - INFO: Bearer authentication successful for internal_side
2025-08-05 18:02:48,882 - 724-Thread-9 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:308] - INFO: ��ȡ��̬����: CLOUD_VM_NOVA
2025-08-05 18:02:48,885 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2121] - INFO: API���ú�ʱ: 0.01s, ״̬��: 200
2025-08-05 18:02:48,893 - 724-Thread-10 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:308] - INFO: ��ȡ��̬����: ApplicationSystem
2025-08-05 18:02:48,893 - 724-Thread-10 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-08-05 18:02:48,895 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2121] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-08-05 18:02:48,895 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2158] - WARNING: HTTP���� 404 (���� 1/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-05 18:02:48,896 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2173] - INFO: ���������󣬵ȴ� 5 �������...
2025-08-05 18:02:53,904 - 724-Thread-11 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:308] - INFO: ��ȡ��̬����: ApplicationSystem
2025-08-05 18:02:53,904 - 724-Thread-11 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-08-05 18:02:53,906 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2121] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-08-05 18:02:53,907 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2158] - WARNING: HTTP���� 404 (���� 2/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-05 18:02:53,907 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2173] - INFO: ���������󣬵ȴ� 5 �������...
2025-08-05 18:02:58,914 - 724-Thread-12 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:308] - INFO: ��ȡ��̬����: ApplicationSystem
2025-08-05 18:02:58,914 - 724-Thread-12 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-08-05 18:02:58,916 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2121] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-08-05 18:02:58,917 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2158] - WARNING: HTTP���� 404 (���� 3/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-05 18:02:58,917 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2173] - INFO: ���������󣬵ȴ� 5 �������...
2025-08-05 18:03:03,924 - 724-Thread-13 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:308] - INFO: ��ȡ��̬����: ApplicationSystem
2025-08-05 18:03:03,924 - 724-Thread-13 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-08-05 18:03:03,926 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2121] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-08-05 18:03:03,926 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2158] - WARNING: HTTP���� 404 (���� 4/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-05 18:03:03,927 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2177] - ERROR: HTTP���󣬲�������: 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-05 18:03:03,927 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4742] - ERROR: ��ȡ�ڲ�����ʧ��: API���÷���None�����������糬ʱ�����񲻿��û���֤ʧ�ܣ�
2025-08-05 18:03:03,927 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:605] - INFO: Completed fetching internal-side data for model: CLOUD_VM_NOVA (found 1 records)
2025-08-05 18:03:03,929 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:616] - INFO: Completed fetching internal-side data for model: CLOUD_VM_NOVA
2025-08-05 18:03:03,929 - 724-Thread-7 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:235] - INFO: ��ʼͬ��ģ��: CLOUD_VM_NOVA
2025-08-05 18:05:46,305 - 2036-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:279] - INFO: ********************************************************************************
2025-08-05 18:05:46,306 - 2036-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:280] - INFO: ��־ϵͳ�ѳ�ʼ������־Ŀ¼: E:\File\repository\xycmdbsync\logs
2025-08-05 18:05:46,306 - 2036-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:281] - INFO: ��־ϵͳ�ѳ�ʼ������־��ת��СΪ: 300M
2025-08-05 18:05:46,307 - 2036-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:282] - INFO: ��־ϵͳ�ѳ�ʼ������־������СΪ50���ļ�
2025-08-05 18:05:46,307 - 2036-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:283] - INFO: ��־ϵͳ�ѳ�ʼ������ǰ��־����Ϊ��20
2025-08-05 18:05:46,307 - 2036-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:284] - INFO: ********************************************************************************
2025-08-05 18:05:46,369 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:44] - INFO: ��ʼ��������...
2025-08-05 18:05:46,422 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:69] - INFO: Loaded base config from E:\File\repository\xycmdbsync\config\base_config.yaml
2025-08-05 18:05:46,426 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:88] - INFO: Found cloud sources: source_1, source_2
2025-08-05 18:05:46,426 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for cloud_side from E:\File\repository\xycmdbsync\config\endpoints\cloud_side.yaml
2025-08-05 18:05:46,435 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for internal_side from E:\File\repository\xycmdbsync\config\endpoints\internal_side.yaml
2025-08-05 18:05:46,596 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:106] - INFO: Loaded transformers from E:\File\repository\xycmdbsync\config\transformers\common.yaml
2025-08-05 18:05:46,662 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_AZONE from E:\File\repository\xycmdbsync\config\models\cloud_azone.yaml
2025-08-05 18:05:46,955 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_BMS from E:\File\repository\xycmdbsync\config\models\cloud_bms.yaml
2025-08-05 18:05:47,071 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cce_cluster.yaml
2025-08-05 18:05:47,160 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CONTAINER from E:\File\repository\xycmdbsync\config\models\cloud_cce_container.yaml
2025-08-05 18:05:47,219 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NAMESPACE from E:\File\repository\xycmdbsync\config\models\cloud_cce_namespace.yaml
2025-08-05 18:05:47,313 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NODE from E:\File\repository\xycmdbsync\config\models\cloud_cce_node.yaml
2025-08-05 18:05:47,396 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_POD from E:\File\repository\xycmdbsync\config\models\cloud_cce_pod.yaml
2025-08-05 18:05:47,485 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_SERVICE from E:\File\repository\xycmdbsync\config\models\cloud_cce_service.yaml
2025-08-05 18:05:47,645 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudCronJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_cronjob.yaml
2025-08-05 18:05:47,792 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDaemonset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_daemonset.yaml
2025-08-05 18:05:47,978 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDeployment from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_deployment.yaml
2025-08-05 18:05:48,163 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_job.yaml
2025-08-05 18:05:48,355 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudStatefulset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_statefulset.yaml
2025-08-05 18:05:48,456 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cluster.yaml
2025-08-05 18:05:48,793 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_HOST from E:\File\repository\xycmdbsync\config\models\cloud_host.yaml
2025-08-05 18:05:48,848 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_OBS_BUCKET from E:\File\repository\xycmdbsync\config\models\cloud_obs_bucket.yaml
2025-08-05 18:05:48,906 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_SFS_SHARE from E:\File\repository\xycmdbsync\config\models\cloud_sfs_share.yaml
2025-08-05 18:05:49,054 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VM_NOVA from E:\File\repository\xycmdbsync\config\models\cloud_vm_nova.yaml
2025-08-05 18:05:49,117 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VOLUME from E:\File\repository\xycmdbsync\config\models\cloud_volume.yaml
2025-08-05 18:05:49,287 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_kafka from E:\File\repository\xycmdbsync\config\models\middleware_kafka.yaml
2025-08-05 18:05:49,363 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_nacos from E:\File\repository\xycmdbsync\config\models\middleware_nacos.yaml
2025-08-05 18:05:49,464 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_redis from E:\File\repository\xycmdbsync\config\models\middleware_redis.yaml
2025-08-05 18:05:49,650 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_rocketmq from E:\File\repository\xycmdbsync\config\models\middleware_rocket_mq.yaml
2025-08-05 18:05:49,797 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_optical_switch from E:\File\repository\xycmdbsync\config\models\network_access_optical_switch.yaml
2025-08-05 18:05:49,923 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_switch from E:\File\repository\xycmdbsync\config\models\network_access_switch.yaml
2025-08-05 18:05:50,045 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for back_bone_router from E:\File\repository\xycmdbsync\config\models\network_backbone_router.yaml
2025-08-05 18:05:50,096 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_bandwidths from E:\File\repository\xycmdbsync\config\models\network_cloud_bandwidths.yaml
2025-08-05 18:05:50,157 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_floating_ips from E:\File\repository\xycmdbsync\config\models\network_cloud_floating_ips.yaml
2025-08-05 18:05:50,213 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_network from E:\File\repository\xycmdbsync\config\models\network_cloud_network.yaml
2025-08-05 18:05:50,281 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_port from E:\File\repository\xycmdbsync\config\models\network_cloud_port.yaml
2025-08-05 18:05:50,343 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_router from E:\File\repository\xycmdbsync\config\models\network_cloud_router.yaml
2025-08-05 18:05:50,403 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_subnet from E:\File\repository\xycmdbsync\config\models\network_cloud_subnet.yaml
2025-08-05 18:05:50,531 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_optical_switch from E:\File\repository\xycmdbsync\config\models\network_core_optical_switch.yaml
2025-08-05 18:05:50,657 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_switch from E:\File\repository\xycmdbsync\config\models\network_core_switch.yaml
2025-08-05 18:05:50,784 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for network_device_port from E:\File\repository\xycmdbsync\config\models\network_device_port.yaml
2025-08-05 18:05:50,907 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall from E:\File\repository\xycmdbsync\config\models\network_fire_wall.yaml
2025-08-05 18:05:51,005 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall_policy_log from E:\File\repository\xycmdbsync\config\models\network_fire_wall_policy_log.yaml
2025-08-05 18:05:51,126 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for land_balance_device from E:\File\repository\xycmdbsync\config\models\network_land_balance_device.yaml
2025-08-05 18:05:51,188 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool.yaml
2025-08-05 18:05:51,237 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool_men from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool_men.yaml
2025-08-05 18:05:51,301 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_service from E:\File\repository\xycmdbsync\config\models\network_load_balance_service.yaml
2025-08-05 18:05:51,422 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for non_backbone_router from E:\File\repository\xycmdbsync\config\models\network_non_backbone_router.yaml
2025-08-05 18:05:51,487 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for sys_deviceLink from E:\File\repository\xycmdbsync\config\models\network_sys_devicelink.yaml
2025-08-05 18:05:51,569 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_waf_engine from E:\File\repository\xycmdbsync\config\models\secure_cloud_waf_engine.yaml
2025-08-05 18:05:51,659 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_BusinessRegion from E:\File\repository\xycmdbsync\config\models\sys_businessregion.yaml
2025-08-05 18:05:51,687 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DataCenter from E:\File\repository\xycmdbsync\config\models\sys_datacenter.yaml
2025-08-05 18:05:51,737 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DistributedStorage from E:\File\repository\xycmdbsync\config\models\sys_distributedstorage.yaml
2025-08-05 18:05:51,867 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_LUN from E:\File\repository\xycmdbsync\config\models\sys_lun.yaml
2025-08-05 18:05:52,031 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_PhysicalServer from E:\File\repository\xycmdbsync\config\models\sys_physicalserver.yaml
2025-08-05 18:05:52,074 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack from E:\File\repository\xycmdbsync\config\models\sys_rack.yaml
2025-08-05 18:05:52,120 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_IntegratedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_integratedcabinet.yaml
2025-08-05 18:05:52,163 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_ShieldedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_shieldedcabinet.yaml
2025-08-05 18:05:52,207 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_ResourcePool from E:\File\repository\xycmdbsync\config\models\sys_resourcepool.yaml
2025-08-05 18:05:52,230 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Room from E:\File\repository\xycmdbsync\config\models\sys_room.yaml
2025-08-05 18:05:52,276 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageCluster from E:\File\repository\xycmdbsync\config\models\sys_storagecluster.yaml
2025-08-05 18:05:52,326 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageNode from E:\File\repository\xycmdbsync\config\models\sys_storagenode.yaml
2025-08-05 18:05:52,388 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StoragePool from E:\File\repository\xycmdbsync\config\models\sys_storagepool.yaml
2025-08-05 18:05:52,554 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorDevice from E:\File\repository\xycmdbsync\config\models\sys_stordevice.yaml
2025-08-05 18:05:52,777 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_X86Server from E:\File\repository\xycmdbsync\config\models\sys_x86server.yaml
2025-08-05 18:05:52,777 - 2036-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:50] - INFO: ���ü������
2025-08-05 18:05:52,896 - 2036-MainThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:45] - INFO: MySQL���ӳس�ʼ���ɹ�����С: 5
2025-08-05 18:05:52,901 - 2036-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_1
2025-08-05 18:05:52,901 - 2036-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_2
2025-08-05 18:05:52,902 - 2036-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:136] - INFO: Initialized cloud API client for source: source_1
2025-08-05 18:05:52,902 - 2036-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:150] - INFO: Initialized internal API client
2025-08-05 18:05:52,902 - 2036-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:212] - INFO: Loading data from cache
2025-08-05 18:05:52,904 - 2036-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:231] - INFO: Loaded internal-side data for model CLOUD_VM_NOVA from cache
2025-08-05 18:05:52,917 - 2036-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-08-05 18:05:52,927 - 2036-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-08-05 18:05:52,955 - 2036-RestApiThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:47] - INFO: �����߳�: restful_api��Դ�ϱ��ӿ�.
2025-08-05 18:05:52,956 - 2036-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:34] - INFO: ��������ʼ�����: ���=24Сʱ, ��ʼ=0��
2025-08-05 18:05:52,957 - 2036-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:35] - INFO: ִ��ʱ���: ['00:00']
2025-08-05 18:05:52,958 - 2036-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:84] - INFO: �����߳�: async_model��ʱ����ͬ������
2025-08-05 18:05:52,958 - 2036-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:85] - INFO: ��������: ÿ24Сʱִ��һ�Σ���00:00��ʼ
2025-08-05 18:05:52,958 - 2036-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:86] - INFO: ִ��ʱ���: 00:00
2025-08-05 18:05:52,959 - 2036-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:87] - INFO: ÿ��ִ�д���: 1 ��
2025-08-05 18:05:52,959 - 2036-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-06 00:00:00, ���뻹�� 21248.00 �룬������...
2025-08-05 18:06:00,129 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:324] - INFO: Fetching cloud-side data for model: CLOUD_VM_NOVA
2025-08-05 18:06:00,129 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:329] - INFO: Using specified data source: source_1
2025-08-05 18:06:00,130 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:361] - INFO: Fetching cloud-side data for model: CLOUD_VM_NOVA
2025-08-05 18:06:00,130 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:378] - INFO: Fetching data for model CLOUD_VM_NOVA from source source_1
2025-08-05 18:06:01,219 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2373] - INFO: Password authentication successful for cloud_source_1
2025-08-05 18:06:01,955 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.73s, ״̬��: 200
2025-08-05 18:06:02,330 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.37s, ״̬��: 200
2025-08-05 18:06:02,590 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.26s, ״̬��: 200
2025-08-05 18:06:02,592 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:266] - INFO: Processing related models for CLOUD_VM_NOVA from source source_1
2025-08-05 18:06:02,592 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:277] - INFO: Processing related model R_Volume_MountOn_VM using method relation_table
2025-08-05 18:06:02,593 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2872] - INFO: Fetching relation table data for model CLOUD_VM_NOVA, related model R_Volume_MountOn_VM from source source_1
2025-08-05 18:06:02,697 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-05 18:06:02,804 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-05 18:06:02,918 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-05 18:06:03,045 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-05 18:06:03,184 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.14s, ״̬��: 200
2025-08-05 18:06:03,284 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-05 18:06:03,417 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-05 18:06:03,518 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-05 18:06:03,624 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-05 18:06:03,754 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-05 18:06:03,856 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-05 18:06:03,973 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-05 18:06:04,081 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-05 18:06:04,229 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.15s, ״̬��: 200
2025-08-05 18:06:04,426 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.20s, ״̬��: 200
2025-08-05 18:06:04,600 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.17s, ״̬��: 200
2025-08-05 18:06:04,717 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-05 18:06:04,915 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.20s, ״̬��: 200
2025-08-05 18:06:05,026 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-05 18:06:05,179 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.15s, ״̬��: 200
2025-08-05 18:06:05,311 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-05 18:06:05,464 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.15s, ״̬��: 200
2025-08-05 18:06:05,569 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-05 18:06:05,775 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.21s, ״̬��: 200
2025-08-05 18:06:05,941 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.17s, ״̬��: 200
2025-08-05 18:06:06,076 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-05 18:06:06,210 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-05 18:06:06,336 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-05 18:06:06,460 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-05 18:06:06,560 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-05 18:06:06,663 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-05 18:06:06,769 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-05 18:06:06,999 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.23s, ״̬��: 200
2025-08-05 18:06:07,228 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.23s, ״̬��: 200
2025-08-05 18:06:07,436 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.21s, ״̬��: 200
2025-08-05 18:06:07,582 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.14s, ״̬��: 200
2025-08-05 18:06:07,724 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.14s, ״̬��: 200
2025-08-05 18:06:07,879 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.15s, ״̬��: 200
2025-08-05 18:06:08,016 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.14s, ״̬��: 200
2025-08-05 18:06:08,184 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.17s, ״̬��: 200
2025-08-05 18:06:08,320 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-05 18:06:08,482 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.16s, ״̬��: 200
2025-08-05 18:06:08,626 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.14s, ״̬��: 200
2025-08-05 18:06:08,661 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:285] - INFO: Fetched 366 records for related model R_Volume_MountOn_VM
2025-08-05 18:06:08,662 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:277] - INFO: Processing related model M_VMUsesFlavor using method relation_table
2025-08-05 18:06:08,662 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2872] - INFO: Fetching relation table data for model CLOUD_VM_NOVA, related model M_VMUsesFlavor from source source_1
2025-08-05 18:06:08,756 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-05 18:06:08,844 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-05 18:06:08,958 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-05 18:06:09,056 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-05 18:06:09,172 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-05 18:06:09,289 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-05 18:06:09,443 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.15s, ״̬��: 200
2025-08-05 18:06:09,558 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-05 18:06:09,651 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-05 18:06:09,757 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-05 18:06:09,846 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-05 18:06:09,940 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-05 18:06:10,043 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-05 18:06:10,136 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-05 18:06:10,242 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-05 18:06:10,388 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.15s, ״̬��: 200
2025-08-05 18:06:10,537 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1981] - INFO: �ƶ�API���ú�ʱ: 0.15s, ״̬��: 200
2025-08-05 18:06:10,544 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:285] - INFO: Fetched 89 records for related model M_VMUsesFlavor
2025-08-05 18:06:10,544 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:411] - INFO: Completed fetching cloud-side data for model: CLOUD_VM_NOVA from source source_1 (found 283 records)
2025-08-05 18:06:10,545 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:420] - ERROR: Error fetching cloud-side data for model CLOUD_VM_NOVA: rawData illegal
2025-08-05 18:06:10,560 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2373] - INFO: Bearer authentication successful for internal_side
2025-08-05 18:06:56,079 - 2036-Thread-9 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:308] - INFO: ��ȡ��̬����: ApplicationSystem
2025-08-05 18:06:56,079 - 2036-Thread-9 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:317] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-08-05 18:06:56,082 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2121] - INFO: API���ú�ʱ: 45.52s, ״̬��: 404
2025-08-05 18:06:56,082 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2158] - WARNING: HTTP���� 404 (���� 1/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-05 18:06:56,083 - 2036-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2173] - INFO: ���������󣬵ȴ� 5 �������...
