2025-08-13 12:34:29,660 - 24452-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:153] - INFO: ****************����ִ��ͬ�� (24Сʱ���)**********************
2025-08-13 12:34:29,704 - 24452-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:161] - WARNING: ���֤Ŀ¼������: E:\File\repository\xycmdbsync\config\license
2025-08-13 12:34:29,704 - 24452-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:164] - INFO: ���Դ����ݿ��ȡ���֤��Ϣ
2025-08-13 12:34:29,721 - 24452-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:73] - INFO: Connection to MySQL DB successful
2025-08-13 12:34:29,726 - 24452-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:88] - INFO: MySQL connection is closed
2025-08-13 12:34:29,726 - 24452-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:170] - INFO: �ɹ������ݿ��ȡ���֤��Ϣ: XYCMDB
2025-08-13 12:34:29,829 - 24452-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:156] - INFO: **************License�ѹ���*******************
2025-08-13 12:34:29,830 - 24452-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-14 00:00:00, ���뻹�� 41131.00 �룬������...
2025-08-13 22:08:21,037 - 6156-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:279] - INFO: ********************************************************************************
2025-08-13 22:08:21,038 - 6156-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:280] - INFO: ��־ϵͳ�ѳ�ʼ������־Ŀ¼: E:\File\repository\xycmdbsync\logs
2025-08-13 22:08:21,038 - 6156-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:281] - INFO: ��־ϵͳ�ѳ�ʼ������־��ת��СΪ: 300M
2025-08-13 22:08:21,039 - 6156-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:282] - INFO: ��־ϵͳ�ѳ�ʼ������־������СΪ50���ļ�
2025-08-13 22:08:21,039 - 6156-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:283] - INFO: ��־ϵͳ�ѳ�ʼ������ǰ��־����Ϊ��10
2025-08-13 22:08:21,040 - 6156-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:284] - INFO: ********************************************************************************
2025-08-13 22:08:21,107 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:44] - INFO: ��ʼ��������...
2025-08-13 22:08:21,154 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:69] - INFO: Loaded base config from E:\File\repository\xycmdbsync\config\base_config.yaml
2025-08-13 22:08:21,158 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:88] - INFO: Found cloud sources: source_1, source_2
2025-08-13 22:08:21,158 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for cloud_side from E:\File\repository\xycmdbsync\config\endpoints\cloud_side.yaml
2025-08-13 22:08:21,167 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for internal_side from E:\File\repository\xycmdbsync\config\endpoints\internal_side.yaml
2025-08-13 22:08:21,323 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:106] - INFO: Loaded transformers from E:\File\repository\xycmdbsync\config\transformers\common.yaml
2025-08-13 22:08:21,378 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_AZONE from E:\File\repository\xycmdbsync\config\models\cloud_azone.yaml
2025-08-13 22:08:21,540 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model CLOUD_BMS
2025-08-13 22:08:21,541 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_BMS from E:\File\repository\xycmdbsync\config\models\cloud_bms.yaml
2025-08-13 22:08:21,620 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cce_cluster.yaml
2025-08-13 22:08:21,689 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CONTAINER from E:\File\repository\xycmdbsync\config\models\cloud_cce_container.yaml
2025-08-13 22:08:21,740 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NAMESPACE from E:\File\repository\xycmdbsync\config\models\cloud_cce_namespace.yaml
2025-08-13 22:08:21,828 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NODE from E:\File\repository\xycmdbsync\config\models\cloud_cce_node.yaml
2025-08-13 22:08:21,897 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_POD from E:\File\repository\xycmdbsync\config\models\cloud_cce_pod.yaml
2025-08-13 22:08:21,962 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_SERVICE from E:\File\repository\xycmdbsync\config\models\cloud_cce_service.yaml
2025-08-13 22:08:22,084 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudCronJob
2025-08-13 22:08:22,084 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudCronJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_cronjob.yaml
2025-08-13 22:08:22,202 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudDaemonset
2025-08-13 22:08:22,202 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDaemonset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_daemonset.yaml
2025-08-13 22:08:22,324 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudDeployment
2025-08-13 22:08:22,324 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDeployment from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_deployment.yaml
2025-08-13 22:08:22,445 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudJob
2025-08-13 22:08:22,445 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_job.yaml
2025-08-13 22:08:22,573 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudStatefulset
2025-08-13 22:08:22,573 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudStatefulset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_statefulset.yaml
2025-08-13 22:08:22,640 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cluster.yaml
2025-08-13 22:08:22,862 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model CLOUD_HOST
2025-08-13 22:08:22,862 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_HOST from E:\File\repository\xycmdbsync\config\models\cloud_host.yaml
2025-08-13 22:08:22,910 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_OBS_BUCKET from E:\File\repository\xycmdbsync\config\models\cloud_obs_bucket.yaml
2025-08-13 22:08:22,961 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_SFS_SHARE from E:\File\repository\xycmdbsync\config\models\cloud_sfs_share.yaml
2025-08-13 22:08:23,088 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VM_NOVA from E:\File\repository\xycmdbsync\config\models\cloud_vm_nova.yaml
2025-08-13 22:08:23,149 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VOLUME from E:\File\repository\xycmdbsync\config\models\cloud_volume.yaml
2025-08-13 22:08:23,293 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model cloud_kafka
2025-08-13 22:08:23,294 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_kafka from E:\File\repository\xycmdbsync\config\models\middleware_kafka.yaml
2025-08-13 22:08:23,354 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_nacos from E:\File\repository\xycmdbsync\config\models\middleware_nacos.yaml
2025-08-13 22:08:23,428 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_redis from E:\File\repository\xycmdbsync\config\models\middleware_redis.yaml
2025-08-13 22:08:23,582 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model cloud_rocketmq
2025-08-13 22:08:23,582 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_rocketmq from E:\File\repository\xycmdbsync\config\models\middleware_rocket_mq.yaml
2025-08-13 22:08:23,702 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model access_optical_switch
2025-08-13 22:08:23,702 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_optical_switch from E:\File\repository\xycmdbsync\config\models\network_access_optical_switch.yaml
2025-08-13 22:08:23,834 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model access_switch
2025-08-13 22:08:23,834 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_switch from E:\File\repository\xycmdbsync\config\models\network_access_switch.yaml
2025-08-13 22:08:23,949 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model back_bone_router
2025-08-13 22:08:23,949 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for back_bone_router from E:\File\repository\xycmdbsync\config\models\network_backbone_router.yaml
2025-08-13 22:08:23,997 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_bandwidths from E:\File\repository\xycmdbsync\config\models\network_cloud_bandwidths.yaml
2025-08-13 22:08:24,053 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_floating_ips from E:\File\repository\xycmdbsync\config\models\network_cloud_floating_ips.yaml
2025-08-13 22:08:24,111 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_network from E:\File\repository\xycmdbsync\config\models\network_cloud_network.yaml
2025-08-13 22:08:24,176 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_port from E:\File\repository\xycmdbsync\config\models\network_cloud_port.yaml
2025-08-13 22:08:24,232 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_router from E:\File\repository\xycmdbsync\config\models\network_cloud_router.yaml
2025-08-13 22:08:24,286 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_subnet from E:\File\repository\xycmdbsync\config\models\network_cloud_subnet.yaml
2025-08-13 22:08:24,409 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model core_optical_switch
2025-08-13 22:08:24,409 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_optical_switch from E:\File\repository\xycmdbsync\config\models\network_core_optical_switch.yaml
2025-08-13 22:08:24,526 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model core_switch
2025-08-13 22:08:24,526 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_switch from E:\File\repository\xycmdbsync\config\models\network_core_switch.yaml
2025-08-13 22:08:24,650 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model network_device_port
2025-08-13 22:08:24,650 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for network_device_port from E:\File\repository\xycmdbsync\config\models\network_device_port.yaml
2025-08-13 22:08:24,760 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model firewall
2025-08-13 22:08:24,760 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall from E:\File\repository\xycmdbsync\config\models\network_fire_wall.yaml
2025-08-13 22:08:24,852 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall_policy_log from E:\File\repository\xycmdbsync\config\models\network_fire_wall_policy_log.yaml
2025-08-13 22:08:24,963 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model land_balance_device
2025-08-13 22:08:24,963 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for land_balance_device from E:\File\repository\xycmdbsync\config\models\network_land_balance_device.yaml
2025-08-13 22:08:25,010 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool.yaml
2025-08-13 22:08:25,057 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool_men from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool_men.yaml
2025-08-13 22:08:25,115 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_service from E:\File\repository\xycmdbsync\config\models\network_load_balance_service.yaml
2025-08-13 22:08:25,231 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model non_backbone_router
2025-08-13 22:08:25,231 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for non_backbone_router from E:\File\repository\xycmdbsync\config\models\network_non_backbone_router.yaml
2025-08-13 22:08:25,294 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for sys_deviceLink from E:\File\repository\xycmdbsync\config\models\network_sys_devicelink.yaml
2025-08-13 22:08:25,374 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_waf_engine from E:\File\repository\xycmdbsync\config\models\secure_cloud_waf_engine.yaml
2025-08-13 22:08:25,464 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_BusinessRegion from E:\File\repository\xycmdbsync\config\models\sys_businessregion.yaml
2025-08-13 22:08:25,496 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DataCenter from E:\File\repository\xycmdbsync\config\models\sys_datacenter.yaml
2025-08-13 22:08:25,549 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DistributedStorage from E:\File\repository\xycmdbsync\config\models\sys_distributedstorage.yaml
2025-08-13 22:08:25,687 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model SYS_LUN
2025-08-13 22:08:25,688 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_LUN from E:\File\repository\xycmdbsync\config\models\sys_lun.yaml
2025-08-13 22:08:25,844 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model SYS_PhysicalServer
2025-08-13 22:08:25,844 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_PhysicalServer from E:\File\repository\xycmdbsync\config\models\sys_physicalserver.yaml
2025-08-13 22:08:25,883 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack from E:\File\repository\xycmdbsync\config\models\sys_rack.yaml
2025-08-13 22:08:25,928 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_IntegratedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_integratedcabinet.yaml
2025-08-13 22:08:25,973 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_ShieldedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_shieldedcabinet.yaml
2025-08-13 22:08:26,015 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_ResourcePool from E:\File\repository\xycmdbsync\config\models\sys_resourcepool.yaml
2025-08-13 22:08:26,037 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Room from E:\File\repository\xycmdbsync\config\models\sys_room.yaml
2025-08-13 22:08:26,079 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageCluster from E:\File\repository\xycmdbsync\config\models\sys_storagecluster.yaml
2025-08-13 22:08:26,123 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageNode from E:\File\repository\xycmdbsync\config\models\sys_storagenode.yaml
2025-08-13 22:08:26,181 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StoragePool from E:\File\repository\xycmdbsync\config\models\sys_storagepool.yaml
2025-08-13 22:08:26,346 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 7 check templates for model SYS_StorDevice
2025-08-13 22:08:26,346 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorDevice from E:\File\repository\xycmdbsync\config\models\sys_stordevice.yaml
2025-08-13 22:08:26,560 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model SYS_X86Server
2025-08-13 22:08:26,560 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_X86Server from E:\File\repository\xycmdbsync\config\models\sys_x86server.yaml
2025-08-13 22:08:26,560 - 6156-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:50] - INFO: ���ü������
2025-08-13 22:08:47,747 - 6156-MainThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:47] - ERROR: ��ʼ�����ӳ�ʱ��������: '2003 (HY000): Can't connect to MySQL server on '10.143.132.59:32513' (10060)'
2025-08-13 22:08:47,750 - 6156-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_1
2025-08-13 22:08:47,750 - 6156-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_2
2025-08-13 22:08:47,751 - 6156-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:136] - INFO: Initialized cloud API client for source: source_1
2025-08-13 22:08:47,751 - 6156-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:150] - INFO: Initialized internal API client
2025-08-13 22:08:47,751 - 6156-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:212] - INFO: Loading data from cache
2025-08-13 22:09:08,762 - 6156-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:327] - ERROR: Error initializing sync record database: 2003 (HY000): Can't connect to MySQL server on '10.143.132.59:32513' (10060)
2025-08-13 22:09:29,775 - 6156-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:327] - ERROR: Error initializing sync record database: 2003 (HY000): Can't connect to MySQL server on '10.143.132.59:32513' (10060)
2025-08-13 22:09:29,786 - 6156-RestApiThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:47] - INFO: �����߳�: restful_api��Դ�ϱ��ӿ�.
2025-08-13 22:09:29,787 - 6156-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:34] - INFO: ��������ʼ�����: ���=24Сʱ, ��ʼ=0��
2025-08-13 22:09:29,788 - 6156-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:35] - INFO: ִ��ʱ���: ['00:00']
2025-08-13 22:09:29,788 - 6156-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:84] - INFO: �����߳�: async_model��ʱ����ͬ������
2025-08-13 22:09:29,789 - 6156-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:85] - INFO: ��������: ÿ24Сʱִ��һ�Σ���00:00��ʼ
2025-08-13 22:09:29,789 - 6156-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:86] - INFO: ִ��ʱ���: 00:00
2025-08-13 22:09:29,789 - 6156-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:87] - INFO: ÿ��ִ�д���: 1 ��
2025-08-13 22:09:29,789 - 6156-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-14 00:00:00, ���뻹�� 6631.00 �룬������...
