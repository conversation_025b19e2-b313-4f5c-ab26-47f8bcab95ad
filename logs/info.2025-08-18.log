2025-08-18 10:53:31,434 - 22536-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:279] - INFO: ********************************************************************************
2025-08-18 10:53:31,434 - 22536-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:280] - INFO: ��־ϵͳ�ѳ�ʼ������־Ŀ¼: E:\File\repository\xycmdbsync\logs
2025-08-18 10:53:31,435 - 22536-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:281] - INFO: ��־ϵͳ�ѳ�ʼ������־��ת��СΪ: 300M
2025-08-18 10:53:31,436 - 22536-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:282] - INFO: ��־ϵͳ�ѳ�ʼ������־������СΪ50���ļ�
2025-08-18 10:53:31,436 - 22536-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:283] - INFO: ��־ϵͳ�ѳ�ʼ������ǰ��־����Ϊ��10
2025-08-18 10:53:31,437 - 22536-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:284] - INFO: ********************************************************************************
2025-08-18 10:53:31,506 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:44] - INFO: ��ʼ��������...
2025-08-18 10:53:31,557 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:69] - INFO: Loaded base config from E:\File\repository\xycmdbsync\config\base_config.yaml
2025-08-18 10:53:31,561 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:88] - INFO: Found cloud sources: source_1, source_2
2025-08-18 10:53:31,562 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for cloud_side from E:\File\repository\xycmdbsync\config\endpoints\cloud_side.yaml
2025-08-18 10:53:31,571 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for internal_side from E:\File\repository\xycmdbsync\config\endpoints\internal_side.yaml
2025-08-18 10:53:31,743 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:106] - INFO: Loaded transformers from E:\File\repository\xycmdbsync\config\transformers\common.yaml
2025-08-18 10:53:31,804 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_AZONE from E:\File\repository\xycmdbsync\config\models\cloud_azone.yaml
2025-08-18 10:53:31,983 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model CLOUD_BMS
2025-08-18 10:53:31,984 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_BMS from E:\File\repository\xycmdbsync\config\models\cloud_bms.yaml
2025-08-18 10:53:32,065 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cce_cluster.yaml
2025-08-18 10:53:32,131 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CONTAINER from E:\File\repository\xycmdbsync\config\models\cloud_cce_container.yaml
2025-08-18 10:53:32,181 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NAMESPACE from E:\File\repository\xycmdbsync\config\models\cloud_cce_namespace.yaml
2025-08-18 10:53:32,271 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NODE from E:\File\repository\xycmdbsync\config\models\cloud_cce_node.yaml
2025-08-18 10:53:32,338 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_POD from E:\File\repository\xycmdbsync\config\models\cloud_cce_pod.yaml
2025-08-18 10:53:32,409 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_SERVICE from E:\File\repository\xycmdbsync\config\models\cloud_cce_service.yaml
2025-08-18 10:53:32,545 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudCronJob
2025-08-18 10:53:32,545 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudCronJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_cronjob.yaml
2025-08-18 10:53:32,698 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudDaemonset
2025-08-18 10:53:32,698 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDaemonset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_daemonset.yaml
2025-08-18 10:53:32,859 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudDeployment
2025-08-18 10:53:32,859 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDeployment from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_deployment.yaml
2025-08-18 10:53:33,028 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudJob
2025-08-18 10:53:33,028 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_job.yaml
2025-08-18 10:53:33,204 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudStatefulset
2025-08-18 10:53:33,205 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudStatefulset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_statefulset.yaml
2025-08-18 10:53:33,286 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cluster.yaml
2025-08-18 10:53:33,564 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model CLOUD_HOST
2025-08-18 10:53:33,564 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_HOST from E:\File\repository\xycmdbsync\config\models\cloud_host.yaml
2025-08-18 10:53:33,622 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_OBS_BUCKET from E:\File\repository\xycmdbsync\config\models\cloud_obs_bucket.yaml
2025-08-18 10:53:33,681 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_SFS_SHARE from E:\File\repository\xycmdbsync\config\models\cloud_sfs_share.yaml
2025-08-18 10:53:33,821 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VM_NOVA from E:\File\repository\xycmdbsync\config\models\cloud_vm_nova.yaml
2025-08-18 10:53:33,886 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VOLUME from E:\File\repository\xycmdbsync\config\models\cloud_volume.yaml
2025-08-18 10:53:34,056 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model cloud_kafka
2025-08-18 10:53:34,056 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_kafka from E:\File\repository\xycmdbsync\config\models\middleware_kafka.yaml
2025-08-18 10:53:34,121 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_nacos from E:\File\repository\xycmdbsync\config\models\middleware_nacos.yaml
2025-08-18 10:53:34,199 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_redis from E:\File\repository\xycmdbsync\config\models\middleware_redis.yaml
2025-08-18 10:53:34,370 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model cloud_rocketmq
2025-08-18 10:53:34,370 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_rocketmq from E:\File\repository\xycmdbsync\config\models\middleware_rocket_mq.yaml
2025-08-18 10:53:34,511 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model access_optical_switch
2025-08-18 10:53:34,511 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_optical_switch from E:\File\repository\xycmdbsync\config\models\network_access_optical_switch.yaml
2025-08-18 10:53:34,639 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model access_switch
2025-08-18 10:53:34,639 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_switch from E:\File\repository\xycmdbsync\config\models\network_access_switch.yaml
2025-08-18 10:53:34,766 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model back_bone_router
2025-08-18 10:53:34,767 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for back_bone_router from E:\File\repository\xycmdbsync\config\models\network_backbone_router.yaml
2025-08-18 10:53:34,816 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_bandwidths from E:\File\repository\xycmdbsync\config\models\network_cloud_bandwidths.yaml
2025-08-18 10:53:34,877 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_floating_ips from E:\File\repository\xycmdbsync\config\models\network_cloud_floating_ips.yaml
2025-08-18 10:53:34,936 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_network from E:\File\repository\xycmdbsync\config\models\network_cloud_network.yaml
2025-08-18 10:53:35,006 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_port from E:\File\repository\xycmdbsync\config\models\network_cloud_port.yaml
2025-08-18 10:53:35,067 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_router from E:\File\repository\xycmdbsync\config\models\network_cloud_router.yaml
2025-08-18 10:53:35,128 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_subnet from E:\File\repository\xycmdbsync\config\models\network_cloud_subnet.yaml
2025-08-18 10:53:35,257 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model core_optical_switch
2025-08-18 10:53:35,257 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_optical_switch from E:\File\repository\xycmdbsync\config\models\network_core_optical_switch.yaml
2025-08-18 10:53:35,404 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model core_switch
2025-08-18 10:53:35,405 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_switch from E:\File\repository\xycmdbsync\config\models\network_core_switch.yaml
2025-08-18 10:53:35,532 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model network_device_port
2025-08-18 10:53:35,532 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for network_device_port from E:\File\repository\xycmdbsync\config\models\network_device_port.yaml
2025-08-18 10:53:35,657 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model firewall
2025-08-18 10:53:35,657 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall from E:\File\repository\xycmdbsync\config\models\network_fire_wall.yaml
2025-08-18 10:53:35,756 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall_policy_log from E:\File\repository\xycmdbsync\config\models\network_fire_wall_policy_log.yaml
2025-08-18 10:53:35,879 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model land_balance_device
2025-08-18 10:53:35,879 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for land_balance_device from E:\File\repository\xycmdbsync\config\models\network_land_balance_device.yaml
2025-08-18 10:53:35,930 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool.yaml
2025-08-18 10:53:35,981 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool_men from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool_men.yaml
2025-08-18 10:53:36,045 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_service from E:\File\repository\xycmdbsync\config\models\network_load_balance_service.yaml
2025-08-18 10:53:36,181 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model non_backbone_router
2025-08-18 10:53:36,181 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for non_backbone_router from E:\File\repository\xycmdbsync\config\models\network_non_backbone_router.yaml
2025-08-18 10:53:36,250 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for sys_deviceLink from E:\File\repository\xycmdbsync\config\models\network_sys_devicelink.yaml
2025-08-18 10:53:36,332 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_waf_engine from E:\File\repository\xycmdbsync\config\models\secure_cloud_waf_engine.yaml
2025-08-18 10:53:36,411 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_BusinessRegion from E:\File\repository\xycmdbsync\config\models\sys_businessregion.yaml
2025-08-18 10:53:36,443 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DataCenter from E:\File\repository\xycmdbsync\config\models\sys_datacenter.yaml
2025-08-18 10:53:36,494 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DistributedStorage from E:\File\repository\xycmdbsync\config\models\sys_distributedstorage.yaml
2025-08-18 10:53:36,635 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model SYS_LUN
2025-08-18 10:53:36,636 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_LUN from E:\File\repository\xycmdbsync\config\models\sys_lun.yaml
2025-08-18 10:53:36,803 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model SYS_PhysicalServer
2025-08-18 10:53:36,803 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_PhysicalServer from E:\File\repository\xycmdbsync\config\models\sys_physicalserver.yaml
2025-08-18 10:53:36,847 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack from E:\File\repository\xycmdbsync\config\models\sys_rack.yaml
2025-08-18 10:53:36,898 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_IntegratedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_integratedcabinet.yaml
2025-08-18 10:53:36,942 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_ShieldedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_shieldedcabinet.yaml
2025-08-18 10:53:36,988 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_ResourcePool from E:\File\repository\xycmdbsync\config\models\sys_resourcepool.yaml
2025-08-18 10:53:37,013 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Room from E:\File\repository\xycmdbsync\config\models\sys_room.yaml
2025-08-18 10:53:37,061 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageCluster from E:\File\repository\xycmdbsync\config\models\sys_storagecluster.yaml
2025-08-18 10:53:37,116 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageNode from E:\File\repository\xycmdbsync\config\models\sys_storagenode.yaml
2025-08-18 10:53:37,177 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StoragePool from E:\File\repository\xycmdbsync\config\models\sys_storagepool.yaml
2025-08-18 10:53:37,349 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 7 check templates for model SYS_StorDevice
2025-08-18 10:53:37,349 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorDevice from E:\File\repository\xycmdbsync\config\models\sys_stordevice.yaml
2025-08-18 10:53:37,580 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model SYS_X86Server
2025-08-18 10:53:37,580 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_X86Server from E:\File\repository\xycmdbsync\config\models\sys_x86server.yaml
2025-08-18 10:53:37,580 - 22536-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:50] - INFO: ���ü������
2025-08-18 10:53:37,799 - 22536-MainThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:45] - INFO: MySQL���ӳس�ʼ���ɹ�����С: 5
2025-08-18 10:53:37,805 - 22536-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_1
2025-08-18 10:53:37,805 - 22536-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_2
2025-08-18 10:53:37,805 - 22536-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:136] - INFO: Initialized cloud API client for source: source_1
2025-08-18 10:53:37,805 - 22536-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:150] - INFO: Initialized internal API client
2025-08-18 10:53:37,806 - 22536-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:212] - INFO: Loading data from cache
2025-08-18 10:53:37,822 - 22536-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-08-18 10:53:37,832 - 22536-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-08-18 10:53:37,856 - 22536-RestApiThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:47] - INFO: �����߳�: restful_api��Դ�ϱ��ӿ�.
2025-08-18 10:53:37,857 - 22536-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:208] - INFO: ����������ʼ�����: ���=30����, ��ʼ=00:00
2025-08-18 10:53:37,858 - 22536-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:209] - INFO: ִ��ʱ���: ['00:00', '00:30', '01:00', '01:30', '02:00', '02:30', '03:00', '03:30', '04:00', '04:30', '05:00', '05:30', '06:00', '06:30', '07:00', '07:30', '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00', '17:30', '18:00', '18:30', '19:00', '19:30', '20:00', '20:30', '21:00', '21:30', '22:00', '22:30', '23:00', '23:30']
2025-08-18 10:53:37,858 - 22536-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:81] - INFO: �����߳�: async_model��ʱ����ͬ������
2025-08-18 10:53:37,859 - 22536-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:82] - INFO: ��������: ÿ30����ִ��һ�Σ���00:00��ʼ
2025-08-18 10:53:37,859 - 22536-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:83] - INFO: ִ��ʱ���: 00:00, 00:30, 01:00, 01:30, 02:00, 02:30, 03:00, 03:30, 04:00, 04:30, 05:00, 05:30, 06:00, 06:30, 07:00, 07:30, 08:00, 08:30, 09:00, 09:30, 10:00, 10:30, 11:00, 11:30, 12:00, 12:30, 13:00, 13:30, 14:00, 14:30, 15:00, 15:30, 16:00, 16:30, 17:00, 17:30, 18:00, 18:30, 19:00, 19:30, 20:00, 20:30, 21:00, 21:30, 22:00, 22:30, 23:00, 23:30
2025-08-18 10:53:37,859 - 22536-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:84] - INFO: ÿ��ִ�д���: 48 ��
2025-08-18 10:53:37,860 - 22536-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-18 11:00:00, ���뻹�� 383.00 �룬������...
2025-08-18 10:54:13,237 - 22864-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:279] - INFO: ********************************************************************************
2025-08-18 10:54:13,238 - 22864-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:280] - INFO: ��־ϵͳ�ѳ�ʼ������־Ŀ¼: E:\File\repository\xycmdbsync\logs
2025-08-18 10:54:13,238 - 22864-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:281] - INFO: ��־ϵͳ�ѳ�ʼ������־��ת��СΪ: 300M
2025-08-18 10:54:13,239 - 22864-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:282] - INFO: ��־ϵͳ�ѳ�ʼ������־������СΪ50���ļ�
2025-08-18 10:54:13,239 - 22864-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:283] - INFO: ��־ϵͳ�ѳ�ʼ������ǰ��־����Ϊ��10
2025-08-18 10:54:13,240 - 22864-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:284] - INFO: ********************************************************************************
2025-08-18 10:54:13,295 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:44] - INFO: ��ʼ��������...
2025-08-18 10:54:13,346 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:69] - INFO: Loaded base config from E:\File\repository\xycmdbsync\config\base_config.yaml
2025-08-18 10:54:13,349 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:88] - INFO: Found cloud sources: source_1, source_2
2025-08-18 10:54:13,349 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for cloud_side from E:\File\repository\xycmdbsync\config\endpoints\cloud_side.yaml
2025-08-18 10:54:13,358 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for internal_side from E:\File\repository\xycmdbsync\config\endpoints\internal_side.yaml
2025-08-18 10:54:13,519 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:106] - INFO: Loaded transformers from E:\File\repository\xycmdbsync\config\transformers\common.yaml
2025-08-18 10:54:13,581 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_AZONE from E:\File\repository\xycmdbsync\config\models\cloud_azone.yaml
2025-08-18 10:54:13,751 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model CLOUD_BMS
2025-08-18 10:54:13,751 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_BMS from E:\File\repository\xycmdbsync\config\models\cloud_bms.yaml
2025-08-18 10:54:13,825 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cce_cluster.yaml
2025-08-18 10:54:13,888 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CONTAINER from E:\File\repository\xycmdbsync\config\models\cloud_cce_container.yaml
2025-08-18 10:54:13,936 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NAMESPACE from E:\File\repository\xycmdbsync\config\models\cloud_cce_namespace.yaml
2025-08-18 10:54:14,026 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NODE from E:\File\repository\xycmdbsync\config\models\cloud_cce_node.yaml
2025-08-18 10:54:14,096 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_POD from E:\File\repository\xycmdbsync\config\models\cloud_cce_pod.yaml
2025-08-18 10:54:14,168 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_SERVICE from E:\File\repository\xycmdbsync\config\models\cloud_cce_service.yaml
2025-08-18 10:54:14,299 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudCronJob
2025-08-18 10:54:14,299 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudCronJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_cronjob.yaml
2025-08-18 10:54:14,443 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudDaemonset
2025-08-18 10:54:14,443 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDaemonset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_daemonset.yaml
2025-08-18 10:54:14,589 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudDeployment
2025-08-18 10:54:14,589 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDeployment from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_deployment.yaml
2025-08-18 10:54:14,726 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudJob
2025-08-18 10:54:14,726 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_job.yaml
2025-08-18 10:54:14,859 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudStatefulset
2025-08-18 10:54:14,860 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudStatefulset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_statefulset.yaml
2025-08-18 10:54:14,932 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cluster.yaml
2025-08-18 10:54:15,213 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model CLOUD_HOST
2025-08-18 10:54:15,213 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_HOST from E:\File\repository\xycmdbsync\config\models\cloud_host.yaml
2025-08-18 10:54:15,271 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_OBS_BUCKET from E:\File\repository\xycmdbsync\config\models\cloud_obs_bucket.yaml
2025-08-18 10:54:15,328 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_SFS_SHARE from E:\File\repository\xycmdbsync\config\models\cloud_sfs_share.yaml
2025-08-18 10:54:15,483 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VM_NOVA from E:\File\repository\xycmdbsync\config\models\cloud_vm_nova.yaml
2025-08-18 10:54:15,542 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VOLUME from E:\File\repository\xycmdbsync\config\models\cloud_volume.yaml
2025-08-18 10:54:15,694 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model cloud_kafka
2025-08-18 10:54:15,694 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_kafka from E:\File\repository\xycmdbsync\config\models\middleware_kafka.yaml
2025-08-18 10:54:15,756 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_nacos from E:\File\repository\xycmdbsync\config\models\middleware_nacos.yaml
2025-08-18 10:54:15,834 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_redis from E:\File\repository\xycmdbsync\config\models\middleware_redis.yaml
2025-08-18 10:54:15,990 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model cloud_rocketmq
2025-08-18 10:54:15,990 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_rocketmq from E:\File\repository\xycmdbsync\config\models\middleware_rocket_mq.yaml
2025-08-18 10:54:16,130 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model access_optical_switch
2025-08-18 10:54:16,130 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_optical_switch from E:\File\repository\xycmdbsync\config\models\network_access_optical_switch.yaml
2025-08-18 10:54:16,290 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model access_switch
2025-08-18 10:54:16,291 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_switch from E:\File\repository\xycmdbsync\config\models\network_access_switch.yaml
2025-08-18 10:54:16,432 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model back_bone_router
2025-08-18 10:54:16,432 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for back_bone_router from E:\File\repository\xycmdbsync\config\models\network_backbone_router.yaml
2025-08-18 10:54:16,488 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_bandwidths from E:\File\repository\xycmdbsync\config\models\network_cloud_bandwidths.yaml
2025-08-18 10:54:16,551 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_floating_ips from E:\File\repository\xycmdbsync\config\models\network_cloud_floating_ips.yaml
2025-08-18 10:54:16,614 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_network from E:\File\repository\xycmdbsync\config\models\network_cloud_network.yaml
2025-08-18 10:54:16,692 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_port from E:\File\repository\xycmdbsync\config\models\network_cloud_port.yaml
2025-08-18 10:54:16,758 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_router from E:\File\repository\xycmdbsync\config\models\network_cloud_router.yaml
2025-08-18 10:54:16,821 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_subnet from E:\File\repository\xycmdbsync\config\models\network_cloud_subnet.yaml
2025-08-18 10:54:16,951 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model core_optical_switch
2025-08-18 10:54:16,952 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_optical_switch from E:\File\repository\xycmdbsync\config\models\network_core_optical_switch.yaml
2025-08-18 10:54:17,080 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model core_switch
2025-08-18 10:54:17,081 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_switch from E:\File\repository\xycmdbsync\config\models\network_core_switch.yaml
2025-08-18 10:54:17,206 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model network_device_port
2025-08-18 10:54:17,206 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for network_device_port from E:\File\repository\xycmdbsync\config\models\network_device_port.yaml
2025-08-18 10:54:17,329 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model firewall
2025-08-18 10:54:17,329 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall from E:\File\repository\xycmdbsync\config\models\network_fire_wall.yaml
2025-08-18 10:54:17,429 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall_policy_log from E:\File\repository\xycmdbsync\config\models\network_fire_wall_policy_log.yaml
2025-08-18 10:54:17,552 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model land_balance_device
2025-08-18 10:54:17,552 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for land_balance_device from E:\File\repository\xycmdbsync\config\models\network_land_balance_device.yaml
2025-08-18 10:54:17,604 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool.yaml
2025-08-18 10:54:17,658 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool_men from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool_men.yaml
2025-08-18 10:54:17,722 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_service from E:\File\repository\xycmdbsync\config\models\network_load_balance_service.yaml
2025-08-18 10:54:17,846 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model non_backbone_router
2025-08-18 10:54:17,846 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for non_backbone_router from E:\File\repository\xycmdbsync\config\models\network_non_backbone_router.yaml
2025-08-18 10:54:17,911 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for sys_deviceLink from E:\File\repository\xycmdbsync\config\models\network_sys_devicelink.yaml
2025-08-18 10:54:17,992 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_waf_engine from E:\File\repository\xycmdbsync\config\models\secure_cloud_waf_engine.yaml
2025-08-18 10:54:18,068 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_BusinessRegion from E:\File\repository\xycmdbsync\config\models\sys_businessregion.yaml
2025-08-18 10:54:18,100 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DataCenter from E:\File\repository\xycmdbsync\config\models\sys_datacenter.yaml
2025-08-18 10:54:18,157 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DistributedStorage from E:\File\repository\xycmdbsync\config\models\sys_distributedstorage.yaml
2025-08-18 10:54:18,293 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model SYS_LUN
2025-08-18 10:54:18,293 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_LUN from E:\File\repository\xycmdbsync\config\models\sys_lun.yaml
2025-08-18 10:54:18,458 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model SYS_PhysicalServer
2025-08-18 10:54:18,458 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_PhysicalServer from E:\File\repository\xycmdbsync\config\models\sys_physicalserver.yaml
2025-08-18 10:54:18,501 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack from E:\File\repository\xycmdbsync\config\models\sys_rack.yaml
2025-08-18 10:54:18,548 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_IntegratedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_integratedcabinet.yaml
2025-08-18 10:54:18,593 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_ShieldedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_shieldedcabinet.yaml
2025-08-18 10:54:18,638 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_ResourcePool from E:\File\repository\xycmdbsync\config\models\sys_resourcepool.yaml
2025-08-18 10:54:18,661 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Room from E:\File\repository\xycmdbsync\config\models\sys_room.yaml
2025-08-18 10:54:18,707 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageCluster from E:\File\repository\xycmdbsync\config\models\sys_storagecluster.yaml
2025-08-18 10:54:18,759 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageNode from E:\File\repository\xycmdbsync\config\models\sys_storagenode.yaml
2025-08-18 10:54:18,834 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StoragePool from E:\File\repository\xycmdbsync\config\models\sys_storagepool.yaml
2025-08-18 10:54:19,013 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 7 check templates for model SYS_StorDevice
2025-08-18 10:54:19,013 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorDevice from E:\File\repository\xycmdbsync\config\models\sys_stordevice.yaml
2025-08-18 10:54:19,241 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model SYS_X86Server
2025-08-18 10:54:19,242 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_X86Server from E:\File\repository\xycmdbsync\config\models\sys_x86server.yaml
2025-08-18 10:54:19,242 - 22864-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:50] - INFO: ���ü������
2025-08-18 10:54:19,355 - 22864-MainThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:45] - INFO: MySQL���ӳس�ʼ���ɹ�����С: 5
2025-08-18 10:54:19,359 - 22864-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_1
2025-08-18 10:54:19,360 - 22864-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_2
2025-08-18 10:54:19,360 - 22864-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:136] - INFO: Initialized cloud API client for source: source_1
2025-08-18 10:54:19,360 - 22864-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:150] - INFO: Initialized internal API client
2025-08-18 10:54:19,360 - 22864-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:212] - INFO: Loading data from cache
2025-08-18 10:54:19,374 - 22864-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-08-18 10:54:19,447 - 22864-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-08-18 10:54:19,459 - 22864-RestApiThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:47] - INFO: �����߳�: restful_api��Դ�ϱ��ӿ�.
2025-08-18 10:54:19,460 - 22864-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:34] - INFO: ��������ʼ�����: ���=24Сʱ, ��ʼ=0��
2025-08-18 10:54:19,461 - 22864-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:35] - INFO: ִ��ʱ���: ['00:00']
2025-08-18 10:54:19,461 - 22864-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:81] - INFO: �����߳�: async_model��ʱ����ͬ������
2025-08-18 10:54:19,462 - 22864-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:82] - INFO: ��������: ÿ24Сʱִ��һ�Σ���00:00��ʼ
2025-08-18 10:54:19,462 - 22864-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:83] - INFO: ִ��ʱ���: 00:00
2025-08-18 10:54:19,462 - 22864-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:84] - INFO: ÿ��ִ�д���: 1 ��
2025-08-18 10:54:19,463 - 22864-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-19 00:00:00, ���뻹�� 47141.00 �룬������...
2025-08-18 10:55:49,278 - 5044-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:279] - INFO: ********************************************************************************
2025-08-18 10:55:49,278 - 5044-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:280] - INFO: ��־ϵͳ�ѳ�ʼ������־Ŀ¼: E:\File\repository\xycmdbsync\logs
2025-08-18 10:55:49,279 - 5044-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:281] - INFO: ��־ϵͳ�ѳ�ʼ������־��ת��СΪ: 300M
2025-08-18 10:55:49,279 - 5044-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:282] - INFO: ��־ϵͳ�ѳ�ʼ������־������СΪ50���ļ�
2025-08-18 10:55:49,279 - 5044-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:283] - INFO: ��־ϵͳ�ѳ�ʼ������ǰ��־����Ϊ��10
2025-08-18 10:55:49,279 - 5044-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:284] - INFO: ********************************************************************************
2025-08-18 10:55:49,336 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:44] - INFO: ��ʼ��������...
2025-08-18 10:55:49,384 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:69] - INFO: Loaded base config from E:\File\repository\xycmdbsync\config\base_config.yaml
2025-08-18 10:55:49,387 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:88] - INFO: Found cloud sources: source_1, source_2
2025-08-18 10:55:49,388 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for cloud_side from E:\File\repository\xycmdbsync\config\endpoints\cloud_side.yaml
2025-08-18 10:55:49,396 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for internal_side from E:\File\repository\xycmdbsync\config\endpoints\internal_side.yaml
2025-08-18 10:55:49,560 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:106] - INFO: Loaded transformers from E:\File\repository\xycmdbsync\config\transformers\common.yaml
2025-08-18 10:55:49,614 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_AZONE from E:\File\repository\xycmdbsync\config\models\cloud_azone.yaml
2025-08-18 10:55:49,779 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model CLOUD_BMS
2025-08-18 10:55:49,779 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_BMS from E:\File\repository\xycmdbsync\config\models\cloud_bms.yaml
2025-08-18 10:55:49,854 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cce_cluster.yaml
2025-08-18 10:55:49,918 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CONTAINER from E:\File\repository\xycmdbsync\config\models\cloud_cce_container.yaml
2025-08-18 10:55:49,965 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NAMESPACE from E:\File\repository\xycmdbsync\config\models\cloud_cce_namespace.yaml
2025-08-18 10:55:50,052 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NODE from E:\File\repository\xycmdbsync\config\models\cloud_cce_node.yaml
2025-08-18 10:55:50,123 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_POD from E:\File\repository\xycmdbsync\config\models\cloud_cce_pod.yaml
2025-08-18 10:55:50,197 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_SERVICE from E:\File\repository\xycmdbsync\config\models\cloud_cce_service.yaml
2025-08-18 10:55:50,326 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudCronJob
2025-08-18 10:55:50,326 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudCronJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_cronjob.yaml
2025-08-18 10:55:50,453 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudDaemonset
2025-08-18 10:55:50,453 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDaemonset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_daemonset.yaml
2025-08-18 10:55:50,586 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudDeployment
2025-08-18 10:55:50,586 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDeployment from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_deployment.yaml
2025-08-18 10:55:50,711 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudJob
2025-08-18 10:55:50,711 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_job.yaml
2025-08-18 10:55:50,846 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudStatefulset
2025-08-18 10:55:50,847 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudStatefulset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_statefulset.yaml
2025-08-18 10:55:50,916 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cluster.yaml
2025-08-18 10:55:51,157 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model CLOUD_HOST
2025-08-18 10:55:51,157 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_HOST from E:\File\repository\xycmdbsync\config\models\cloud_host.yaml
2025-08-18 10:55:51,209 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_OBS_BUCKET from E:\File\repository\xycmdbsync\config\models\cloud_obs_bucket.yaml
2025-08-18 10:55:51,265 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_SFS_SHARE from E:\File\repository\xycmdbsync\config\models\cloud_sfs_share.yaml
2025-08-18 10:55:51,406 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VM_NOVA from E:\File\repository\xycmdbsync\config\models\cloud_vm_nova.yaml
2025-08-18 10:55:51,467 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VOLUME from E:\File\repository\xycmdbsync\config\models\cloud_volume.yaml
2025-08-18 10:55:51,622 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model cloud_kafka
2025-08-18 10:55:51,622 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_kafka from E:\File\repository\xycmdbsync\config\models\middleware_kafka.yaml
2025-08-18 10:55:51,685 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_nacos from E:\File\repository\xycmdbsync\config\models\middleware_nacos.yaml
2025-08-18 10:55:51,762 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_redis from E:\File\repository\xycmdbsync\config\models\middleware_redis.yaml
2025-08-18 10:55:51,924 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model cloud_rocketmq
2025-08-18 10:55:51,925 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_rocketmq from E:\File\repository\xycmdbsync\config\models\middleware_rocket_mq.yaml
2025-08-18 10:55:52,048 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model access_optical_switch
2025-08-18 10:55:52,049 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_optical_switch from E:\File\repository\xycmdbsync\config\models\network_access_optical_switch.yaml
2025-08-18 10:55:52,183 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model access_switch
2025-08-18 10:55:52,183 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_switch from E:\File\repository\xycmdbsync\config\models\network_access_switch.yaml
2025-08-18 10:55:52,307 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model back_bone_router
2025-08-18 10:55:52,307 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for back_bone_router from E:\File\repository\xycmdbsync\config\models\network_backbone_router.yaml
2025-08-18 10:55:52,359 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_bandwidths from E:\File\repository\xycmdbsync\config\models\network_cloud_bandwidths.yaml
2025-08-18 10:55:52,419 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_floating_ips from E:\File\repository\xycmdbsync\config\models\network_cloud_floating_ips.yaml
2025-08-18 10:55:52,477 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_network from E:\File\repository\xycmdbsync\config\models\network_cloud_network.yaml
2025-08-18 10:55:52,547 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_port from E:\File\repository\xycmdbsync\config\models\network_cloud_port.yaml
2025-08-18 10:55:52,609 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_router from E:\File\repository\xycmdbsync\config\models\network_cloud_router.yaml
2025-08-18 10:55:52,668 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_subnet from E:\File\repository\xycmdbsync\config\models\network_cloud_subnet.yaml
2025-08-18 10:55:52,795 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model core_optical_switch
2025-08-18 10:55:52,795 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_optical_switch from E:\File\repository\xycmdbsync\config\models\network_core_optical_switch.yaml
2025-08-18 10:55:52,924 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model core_switch
2025-08-18 10:55:52,924 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_switch from E:\File\repository\xycmdbsync\config\models\network_core_switch.yaml
2025-08-18 10:55:53,052 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model network_device_port
2025-08-18 10:55:53,052 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for network_device_port from E:\File\repository\xycmdbsync\config\models\network_device_port.yaml
2025-08-18 10:55:53,176 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model firewall
2025-08-18 10:55:53,176 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall from E:\File\repository\xycmdbsync\config\models\network_fire_wall.yaml
2025-08-18 10:55:53,271 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall_policy_log from E:\File\repository\xycmdbsync\config\models\network_fire_wall_policy_log.yaml
2025-08-18 10:55:53,390 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model land_balance_device
2025-08-18 10:55:53,390 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for land_balance_device from E:\File\repository\xycmdbsync\config\models\network_land_balance_device.yaml
2025-08-18 10:55:53,441 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool.yaml
2025-08-18 10:55:53,491 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool_men from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool_men.yaml
2025-08-18 10:55:53,554 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_service from E:\File\repository\xycmdbsync\config\models\network_load_balance_service.yaml
2025-08-18 10:55:53,675 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model non_backbone_router
2025-08-18 10:55:53,675 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for non_backbone_router from E:\File\repository\xycmdbsync\config\models\network_non_backbone_router.yaml
2025-08-18 10:55:53,739 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for sys_deviceLink from E:\File\repository\xycmdbsync\config\models\network_sys_devicelink.yaml
2025-08-18 10:55:53,821 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_waf_engine from E:\File\repository\xycmdbsync\config\models\secure_cloud_waf_engine.yaml
2025-08-18 10:55:53,906 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_BusinessRegion from E:\File\repository\xycmdbsync\config\models\sys_businessregion.yaml
2025-08-18 10:55:53,935 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DataCenter from E:\File\repository\xycmdbsync\config\models\sys_datacenter.yaml
2025-08-18 10:55:53,985 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DistributedStorage from E:\File\repository\xycmdbsync\config\models\sys_distributedstorage.yaml
2025-08-18 10:55:54,113 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model SYS_LUN
2025-08-18 10:55:54,113 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_LUN from E:\File\repository\xycmdbsync\config\models\sys_lun.yaml
2025-08-18 10:55:54,276 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model SYS_PhysicalServer
2025-08-18 10:55:54,276 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_PhysicalServer from E:\File\repository\xycmdbsync\config\models\sys_physicalserver.yaml
2025-08-18 10:55:54,320 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack from E:\File\repository\xycmdbsync\config\models\sys_rack.yaml
2025-08-18 10:55:54,367 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_IntegratedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_integratedcabinet.yaml
2025-08-18 10:55:54,412 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_ShieldedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_shieldedcabinet.yaml
2025-08-18 10:55:54,456 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_ResourcePool from E:\File\repository\xycmdbsync\config\models\sys_resourcepool.yaml
2025-08-18 10:55:54,479 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Room from E:\File\repository\xycmdbsync\config\models\sys_room.yaml
2025-08-18 10:55:54,525 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageCluster from E:\File\repository\xycmdbsync\config\models\sys_storagecluster.yaml
2025-08-18 10:55:54,577 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageNode from E:\File\repository\xycmdbsync\config\models\sys_storagenode.yaml
2025-08-18 10:55:54,640 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StoragePool from E:\File\repository\xycmdbsync\config\models\sys_storagepool.yaml
2025-08-18 10:55:54,809 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 7 check templates for model SYS_StorDevice
2025-08-18 10:55:54,810 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorDevice from E:\File\repository\xycmdbsync\config\models\sys_stordevice.yaml
2025-08-18 10:55:55,028 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model SYS_X86Server
2025-08-18 10:55:55,028 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_X86Server from E:\File\repository\xycmdbsync\config\models\sys_x86server.yaml
2025-08-18 10:55:55,028 - 5044-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:50] - INFO: ���ü������
2025-08-18 10:55:55,151 - 5044-MainThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:45] - INFO: MySQL���ӳس�ʼ���ɹ�����С: 5
2025-08-18 10:55:55,155 - 5044-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_1
2025-08-18 10:55:55,155 - 5044-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_2
2025-08-18 10:55:55,156 - 5044-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:136] - INFO: Initialized cloud API client for source: source_1
2025-08-18 10:55:55,156 - 5044-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:150] - INFO: Initialized internal API client
2025-08-18 10:55:55,156 - 5044-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:212] - INFO: Loading data from cache
2025-08-18 10:55:55,170 - 5044-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-08-18 10:55:55,178 - 5044-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-08-18 10:55:55,190 - 5044-RestApiThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:47] - INFO: �����߳�: restful_api��Դ�ϱ��ӿ�.
2025-08-18 10:55:55,191 - 5044-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:34] - INFO: ������(��ʱ)��ʼ�����: ���=24Сʱ, ��ʼ=0��
2025-08-18 10:55:55,192 - 5044-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:35] - INFO: ִ��ʱ���: ['00:00']
2025-08-18 10:55:55,192 - 5044-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:81] - INFO: �����߳�: async_model��ʱ����ͬ������
2025-08-18 10:55:55,193 - 5044-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:82] - INFO: ��������: ÿ24Сʱִ��һ�Σ���00:00��ʼ
2025-08-18 10:55:55,193 - 5044-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:83] - INFO: ִ��ʱ���: 00:00
2025-08-18 10:55:55,193 - 5044-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:84] - INFO: ÿ��ִ�д���: 1 ��
2025-08-18 10:55:55,194 - 5044-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-19 00:00:00, ���뻹�� 47045.00 �룬������...
2025-08-18 10:56:28,473 - 5248-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:279] - INFO: ********************************************************************************
2025-08-18 10:56:28,474 - 5248-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:280] - INFO: ��־ϵͳ�ѳ�ʼ������־Ŀ¼: E:\File\repository\xycmdbsync\logs
2025-08-18 10:56:28,474 - 5248-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:281] - INFO: ��־ϵͳ�ѳ�ʼ������־��ת��СΪ: 300M
2025-08-18 10:56:28,474 - 5248-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:282] - INFO: ��־ϵͳ�ѳ�ʼ������־������СΪ50���ļ�
2025-08-18 10:56:28,475 - 5248-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:283] - INFO: ��־ϵͳ�ѳ�ʼ������ǰ��־����Ϊ��10
2025-08-18 10:56:28,475 - 5248-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:284] - INFO: ********************************************************************************
2025-08-18 10:56:28,533 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:44] - INFO: ��ʼ��������...
2025-08-18 10:56:28,582 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:69] - INFO: Loaded base config from E:\File\repository\xycmdbsync\config\base_config.yaml
2025-08-18 10:56:28,586 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:88] - INFO: Found cloud sources: source_1, source_2
2025-08-18 10:56:28,586 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for cloud_side from E:\File\repository\xycmdbsync\config\endpoints\cloud_side.yaml
2025-08-18 10:56:28,595 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for internal_side from E:\File\repository\xycmdbsync\config\endpoints\internal_side.yaml
2025-08-18 10:56:28,754 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:106] - INFO: Loaded transformers from E:\File\repository\xycmdbsync\config\transformers\common.yaml
2025-08-18 10:56:28,813 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_AZONE from E:\File\repository\xycmdbsync\config\models\cloud_azone.yaml
2025-08-18 10:56:28,983 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model CLOUD_BMS
2025-08-18 10:56:28,984 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_BMS from E:\File\repository\xycmdbsync\config\models\cloud_bms.yaml
2025-08-18 10:56:29,059 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cce_cluster.yaml
2025-08-18 10:56:29,122 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CONTAINER from E:\File\repository\xycmdbsync\config\models\cloud_cce_container.yaml
2025-08-18 10:56:29,170 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NAMESPACE from E:\File\repository\xycmdbsync\config\models\cloud_cce_namespace.yaml
2025-08-18 10:56:29,255 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NODE from E:\File\repository\xycmdbsync\config\models\cloud_cce_node.yaml
2025-08-18 10:56:29,322 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_POD from E:\File\repository\xycmdbsync\config\models\cloud_cce_pod.yaml
2025-08-18 10:56:29,393 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_SERVICE from E:\File\repository\xycmdbsync\config\models\cloud_cce_service.yaml
2025-08-18 10:56:29,527 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudCronJob
2025-08-18 10:56:29,527 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudCronJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_cronjob.yaml
2025-08-18 10:56:29,654 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudDaemonset
2025-08-18 10:56:29,654 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDaemonset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_daemonset.yaml
2025-08-18 10:56:29,790 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudDeployment
2025-08-18 10:56:29,791 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDeployment from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_deployment.yaml
2025-08-18 10:56:29,920 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudJob
2025-08-18 10:56:29,921 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_job.yaml
2025-08-18 10:56:30,058 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudStatefulset
2025-08-18 10:56:30,059 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudStatefulset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_statefulset.yaml
2025-08-18 10:56:30,132 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cluster.yaml
2025-08-18 10:56:30,374 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model CLOUD_HOST
2025-08-18 10:56:30,375 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_HOST from E:\File\repository\xycmdbsync\config\models\cloud_host.yaml
2025-08-18 10:56:30,426 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_OBS_BUCKET from E:\File\repository\xycmdbsync\config\models\cloud_obs_bucket.yaml
2025-08-18 10:56:30,481 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_SFS_SHARE from E:\File\repository\xycmdbsync\config\models\cloud_sfs_share.yaml
2025-08-18 10:56:30,617 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VM_NOVA from E:\File\repository\xycmdbsync\config\models\cloud_vm_nova.yaml
2025-08-18 10:56:30,678 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VOLUME from E:\File\repository\xycmdbsync\config\models\cloud_volume.yaml
2025-08-18 10:56:30,866 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model cloud_kafka
2025-08-18 10:56:30,866 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_kafka from E:\File\repository\xycmdbsync\config\models\middleware_kafka.yaml
2025-08-18 10:56:30,939 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_nacos from E:\File\repository\xycmdbsync\config\models\middleware_nacos.yaml
2025-08-18 10:56:31,030 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_redis from E:\File\repository\xycmdbsync\config\models\middleware_redis.yaml
2025-08-18 10:56:31,201 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model cloud_rocketmq
2025-08-18 10:56:31,201 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_rocketmq from E:\File\repository\xycmdbsync\config\models\middleware_rocket_mq.yaml
2025-08-18 10:56:31,326 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model access_optical_switch
2025-08-18 10:56:31,326 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_optical_switch from E:\File\repository\xycmdbsync\config\models\network_access_optical_switch.yaml
2025-08-18 10:56:31,460 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model access_switch
2025-08-18 10:56:31,460 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_switch from E:\File\repository\xycmdbsync\config\models\network_access_switch.yaml
2025-08-18 10:56:31,589 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model back_bone_router
2025-08-18 10:56:31,589 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for back_bone_router from E:\File\repository\xycmdbsync\config\models\network_backbone_router.yaml
2025-08-18 10:56:31,642 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_bandwidths from E:\File\repository\xycmdbsync\config\models\network_cloud_bandwidths.yaml
2025-08-18 10:56:31,705 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_floating_ips from E:\File\repository\xycmdbsync\config\models\network_cloud_floating_ips.yaml
2025-08-18 10:56:31,763 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_network from E:\File\repository\xycmdbsync\config\models\network_cloud_network.yaml
2025-08-18 10:56:31,834 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_port from E:\File\repository\xycmdbsync\config\models\network_cloud_port.yaml
2025-08-18 10:56:31,899 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_router from E:\File\repository\xycmdbsync\config\models\network_cloud_router.yaml
2025-08-18 10:56:31,964 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_subnet from E:\File\repository\xycmdbsync\config\models\network_cloud_subnet.yaml
2025-08-18 10:56:32,100 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model core_optical_switch
2025-08-18 10:56:32,100 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_optical_switch from E:\File\repository\xycmdbsync\config\models\network_core_optical_switch.yaml
2025-08-18 10:56:32,231 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model core_switch
2025-08-18 10:56:32,231 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_switch from E:\File\repository\xycmdbsync\config\models\network_core_switch.yaml
2025-08-18 10:56:32,370 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model network_device_port
2025-08-18 10:56:32,371 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for network_device_port from E:\File\repository\xycmdbsync\config\models\network_device_port.yaml
2025-08-18 10:56:32,493 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model firewall
2025-08-18 10:56:32,494 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall from E:\File\repository\xycmdbsync\config\models\network_fire_wall.yaml
2025-08-18 10:56:32,590 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall_policy_log from E:\File\repository\xycmdbsync\config\models\network_fire_wall_policy_log.yaml
2025-08-18 10:56:32,711 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model land_balance_device
2025-08-18 10:56:32,711 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for land_balance_device from E:\File\repository\xycmdbsync\config\models\network_land_balance_device.yaml
2025-08-18 10:56:32,761 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool.yaml
2025-08-18 10:56:32,813 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool_men from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool_men.yaml
2025-08-18 10:56:32,876 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_service from E:\File\repository\xycmdbsync\config\models\network_load_balance_service.yaml
2025-08-18 10:56:32,999 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model non_backbone_router
2025-08-18 10:56:32,999 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for non_backbone_router from E:\File\repository\xycmdbsync\config\models\network_non_backbone_router.yaml
2025-08-18 10:56:33,064 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for sys_deviceLink from E:\File\repository\xycmdbsync\config\models\network_sys_devicelink.yaml
2025-08-18 10:56:33,145 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_waf_engine from E:\File\repository\xycmdbsync\config\models\secure_cloud_waf_engine.yaml
2025-08-18 10:56:33,220 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_BusinessRegion from E:\File\repository\xycmdbsync\config\models\sys_businessregion.yaml
2025-08-18 10:56:33,249 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DataCenter from E:\File\repository\xycmdbsync\config\models\sys_datacenter.yaml
2025-08-18 10:56:33,301 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DistributedStorage from E:\File\repository\xycmdbsync\config\models\sys_distributedstorage.yaml
2025-08-18 10:56:33,435 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model SYS_LUN
2025-08-18 10:56:33,435 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_LUN from E:\File\repository\xycmdbsync\config\models\sys_lun.yaml
2025-08-18 10:56:33,602 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model SYS_PhysicalServer
2025-08-18 10:56:33,602 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_PhysicalServer from E:\File\repository\xycmdbsync\config\models\sys_physicalserver.yaml
2025-08-18 10:56:33,645 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack from E:\File\repository\xycmdbsync\config\models\sys_rack.yaml
2025-08-18 10:56:33,693 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_IntegratedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_integratedcabinet.yaml
2025-08-18 10:56:33,737 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_ShieldedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_shieldedcabinet.yaml
2025-08-18 10:56:33,785 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_ResourcePool from E:\File\repository\xycmdbsync\config\models\sys_resourcepool.yaml
2025-08-18 10:56:33,809 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Room from E:\File\repository\xycmdbsync\config\models\sys_room.yaml
2025-08-18 10:56:33,857 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageCluster from E:\File\repository\xycmdbsync\config\models\sys_storagecluster.yaml
2025-08-18 10:56:33,907 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageNode from E:\File\repository\xycmdbsync\config\models\sys_storagenode.yaml
2025-08-18 10:56:33,968 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StoragePool from E:\File\repository\xycmdbsync\config\models\sys_storagepool.yaml
2025-08-18 10:56:34,137 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 7 check templates for model SYS_StorDevice
2025-08-18 10:56:34,137 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorDevice from E:\File\repository\xycmdbsync\config\models\sys_stordevice.yaml
2025-08-18 10:56:34,370 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model SYS_X86Server
2025-08-18 10:56:34,370 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_X86Server from E:\File\repository\xycmdbsync\config\models\sys_x86server.yaml
2025-08-18 10:56:34,371 - 5248-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:50] - INFO: ���ü������
2025-08-18 10:56:34,534 - 5248-MainThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:45] - INFO: MySQL���ӳس�ʼ���ɹ�����С: 5
2025-08-18 10:56:34,538 - 5248-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_1
2025-08-18 10:56:34,538 - 5248-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_2
2025-08-18 10:56:34,538 - 5248-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:136] - INFO: Initialized cloud API client for source: source_1
2025-08-18 10:56:34,538 - 5248-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:150] - INFO: Initialized internal API client
2025-08-18 10:56:34,539 - 5248-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:212] - INFO: Loading data from cache
2025-08-18 10:56:34,552 - 5248-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-08-18 10:56:34,560 - 5248-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-08-18 10:56:34,572 - 5248-RestApiThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:47] - INFO: �����߳�: restful_api��Դ�ϱ��ӿ�.
2025-08-18 10:56:34,573 - 5248-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:208] - INFO: ������(����)��ʼ�����: ���=30����, ��ʼ=00:00
2025-08-18 10:56:34,595 - 5248-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:209] - INFO: ִ��ʱ���: ['00:00', '00:30', '01:00', '01:30', '02:00', '02:30', '03:00', '03:30', '04:00', '04:30', '05:00', '05:30', '06:00', '06:30', '07:00', '07:30', '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00', '17:30', '18:00', '18:30', '19:00', '19:30', '20:00', '20:30', '21:00', '21:30', '22:00', '22:30', '23:00', '23:30']
2025-08-18 10:56:34,596 - 5248-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:81] - INFO: �����߳�: async_model��ʱ����ͬ������
2025-08-18 10:56:34,596 - 5248-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:82] - INFO: ��������: ÿ30����ִ��һ�Σ���00:00��ʼ
2025-08-18 10:56:34,597 - 5248-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:83] - INFO: ִ��ʱ���: 00:00, 00:30, 01:00, 01:30, 02:00, 02:30, 03:00, 03:30, 04:00, 04:30, 05:00, 05:30, 06:00, 06:30, 07:00, 07:30, 08:00, 08:30, 09:00, 09:30, 10:00, 10:30, 11:00, 11:30, 12:00, 12:30, 13:00, 13:30, 14:00, 14:30, 15:00, 15:30, 16:00, 16:30, 17:00, 17:30, 18:00, 18:30, 19:00, 19:30, 20:00, 20:30, 21:00, 21:30, 22:00, 22:30, 23:00, 23:30
2025-08-18 10:56:34,597 - 5248-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:84] - INFO: ÿ��ִ�д���: 48 ��
2025-08-18 10:56:34,597 - 5248-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-18 11:00:00, ���뻹�� 206.00 �룬������...
2025-08-18 11:00:00,598 - 5248-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:161] - INFO: ****************����ִ��ͬ�� (30���Ӽ��)**********************
2025-08-18 11:00:00,598 - 5248-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:161] - WARNING: ���֤Ŀ¼������: E:\File\repository\xycmdbsync\config\license
2025-08-18 11:00:00,598 - 5248-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:164] - INFO: ���Դ����ݿ��ȡ���֤��Ϣ
2025-08-18 11:00:00,601 - 5248-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:73] - INFO: Connection to MySQL DB successful
2025-08-18 11:00:00,602 - 5248-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:88] - INFO: MySQL connection is closed
2025-08-18 11:00:00,603 - 5248-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:170] - INFO: �ɹ������ݿ��ȡ���֤��Ϣ: XYCMDB
2025-08-18 11:00:00,653 - 5248-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:165] - INFO: **************License�ѹ���*******************
2025-08-18 11:00:00,653 - 5248-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-18 11:30:00, ���뻹�� 1800.00 �룬������...
2025-08-18 11:02:34,067 - 9908-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:279] - INFO: ********************************************************************************
2025-08-18 11:02:34,068 - 9908-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:280] - INFO: ��־ϵͳ�ѳ�ʼ������־Ŀ¼: E:\File\repository\xycmdbsync\logs
2025-08-18 11:02:34,068 - 9908-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:281] - INFO: ��־ϵͳ�ѳ�ʼ������־��ת��СΪ: 300M
2025-08-18 11:02:34,069 - 9908-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:282] - INFO: ��־ϵͳ�ѳ�ʼ������־������СΪ50���ļ�
2025-08-18 11:02:34,069 - 9908-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:283] - INFO: ��־ϵͳ�ѳ�ʼ������ǰ��־����Ϊ��10
2025-08-18 11:02:34,069 - 9908-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:284] - INFO: ********************************************************************************
2025-08-18 11:02:34,128 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:44] - INFO: ��ʼ��������...
2025-08-18 11:02:34,178 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:69] - INFO: Loaded base config from E:\File\repository\xycmdbsync\config\base_config.yaml
2025-08-18 11:02:34,181 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:88] - INFO: Found cloud sources: source_1, source_2
2025-08-18 11:02:34,181 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for cloud_side from E:\File\repository\xycmdbsync\config\endpoints\cloud_side.yaml
2025-08-18 11:02:34,190 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for internal_side from E:\File\repository\xycmdbsync\config\endpoints\internal_side.yaml
2025-08-18 11:02:34,353 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:106] - INFO: Loaded transformers from E:\File\repository\xycmdbsync\config\transformers\common.yaml
2025-08-18 11:02:34,412 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_AZONE from E:\File\repository\xycmdbsync\config\models\cloud_azone.yaml
2025-08-18 11:02:34,583 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model CLOUD_BMS
2025-08-18 11:02:34,584 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_BMS from E:\File\repository\xycmdbsync\config\models\cloud_bms.yaml
2025-08-18 11:02:34,659 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cce_cluster.yaml
2025-08-18 11:02:34,725 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CONTAINER from E:\File\repository\xycmdbsync\config\models\cloud_cce_container.yaml
2025-08-18 11:02:34,772 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NAMESPACE from E:\File\repository\xycmdbsync\config\models\cloud_cce_namespace.yaml
2025-08-18 11:02:34,855 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NODE from E:\File\repository\xycmdbsync\config\models\cloud_cce_node.yaml
2025-08-18 11:02:34,921 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_POD from E:\File\repository\xycmdbsync\config\models\cloud_cce_pod.yaml
2025-08-18 11:02:34,993 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_SERVICE from E:\File\repository\xycmdbsync\config\models\cloud_cce_service.yaml
2025-08-18 11:02:35,125 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudCronJob
2025-08-18 11:02:35,125 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudCronJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_cronjob.yaml
2025-08-18 11:02:35,252 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudDaemonset
2025-08-18 11:02:35,252 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDaemonset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_daemonset.yaml
2025-08-18 11:02:35,389 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudDeployment
2025-08-18 11:02:35,389 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDeployment from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_deployment.yaml
2025-08-18 11:02:35,521 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudJob
2025-08-18 11:02:35,522 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_job.yaml
2025-08-18 11:02:35,656 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudStatefulset
2025-08-18 11:02:35,657 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudStatefulset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_statefulset.yaml
2025-08-18 11:02:35,729 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cluster.yaml
2025-08-18 11:02:35,969 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model CLOUD_HOST
2025-08-18 11:02:35,970 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_HOST from E:\File\repository\xycmdbsync\config\models\cloud_host.yaml
2025-08-18 11:02:36,021 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_OBS_BUCKET from E:\File\repository\xycmdbsync\config\models\cloud_obs_bucket.yaml
2025-08-18 11:02:36,083 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_SFS_SHARE from E:\File\repository\xycmdbsync\config\models\cloud_sfs_share.yaml
2025-08-18 11:02:36,235 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VM_NOVA from E:\File\repository\xycmdbsync\config\models\cloud_vm_nova.yaml
2025-08-18 11:02:36,306 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VOLUME from E:\File\repository\xycmdbsync\config\models\cloud_volume.yaml
2025-08-18 11:02:36,460 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model cloud_kafka
2025-08-18 11:02:36,460 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_kafka from E:\File\repository\xycmdbsync\config\models\middleware_kafka.yaml
2025-08-18 11:02:36,522 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_nacos from E:\File\repository\xycmdbsync\config\models\middleware_nacos.yaml
2025-08-18 11:02:36,599 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_redis from E:\File\repository\xycmdbsync\config\models\middleware_redis.yaml
2025-08-18 11:02:36,764 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model cloud_rocketmq
2025-08-18 11:02:36,764 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_rocketmq from E:\File\repository\xycmdbsync\config\models\middleware_rocket_mq.yaml
2025-08-18 11:02:36,900 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model access_optical_switch
2025-08-18 11:02:36,901 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_optical_switch from E:\File\repository\xycmdbsync\config\models\network_access_optical_switch.yaml
2025-08-18 11:02:37,043 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model access_switch
2025-08-18 11:02:37,043 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_switch from E:\File\repository\xycmdbsync\config\models\network_access_switch.yaml
2025-08-18 11:02:37,189 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model back_bone_router
2025-08-18 11:02:37,190 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for back_bone_router from E:\File\repository\xycmdbsync\config\models\network_backbone_router.yaml
2025-08-18 11:02:37,239 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_bandwidths from E:\File\repository\xycmdbsync\config\models\network_cloud_bandwidths.yaml
2025-08-18 11:02:37,301 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_floating_ips from E:\File\repository\xycmdbsync\config\models\network_cloud_floating_ips.yaml
2025-08-18 11:02:37,356 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_network from E:\File\repository\xycmdbsync\config\models\network_cloud_network.yaml
2025-08-18 11:02:37,426 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_port from E:\File\repository\xycmdbsync\config\models\network_cloud_port.yaml
2025-08-18 11:02:37,486 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_router from E:\File\repository\xycmdbsync\config\models\network_cloud_router.yaml
2025-08-18 11:02:37,544 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_subnet from E:\File\repository\xycmdbsync\config\models\network_cloud_subnet.yaml
2025-08-18 11:02:37,672 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model core_optical_switch
2025-08-18 11:02:37,672 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_optical_switch from E:\File\repository\xycmdbsync\config\models\network_core_optical_switch.yaml
2025-08-18 11:02:37,799 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model core_switch
2025-08-18 11:02:37,799 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_switch from E:\File\repository\xycmdbsync\config\models\network_core_switch.yaml
2025-08-18 11:02:37,922 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model network_device_port
2025-08-18 11:02:37,923 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for network_device_port from E:\File\repository\xycmdbsync\config\models\network_device_port.yaml
2025-08-18 11:02:38,039 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model firewall
2025-08-18 11:02:38,040 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall from E:\File\repository\xycmdbsync\config\models\network_fire_wall.yaml
2025-08-18 11:02:38,137 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall_policy_log from E:\File\repository\xycmdbsync\config\models\network_fire_wall_policy_log.yaml
2025-08-18 11:02:38,269 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model land_balance_device
2025-08-18 11:02:38,270 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for land_balance_device from E:\File\repository\xycmdbsync\config\models\network_land_balance_device.yaml
2025-08-18 11:02:38,318 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool.yaml
2025-08-18 11:02:38,369 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool_men from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool_men.yaml
2025-08-18 11:02:38,432 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_service from E:\File\repository\xycmdbsync\config\models\network_load_balance_service.yaml
2025-08-18 11:02:38,552 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model non_backbone_router
2025-08-18 11:02:38,553 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for non_backbone_router from E:\File\repository\xycmdbsync\config\models\network_non_backbone_router.yaml
2025-08-18 11:02:38,617 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for sys_deviceLink from E:\File\repository\xycmdbsync\config\models\network_sys_devicelink.yaml
2025-08-18 11:02:38,697 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_waf_engine from E:\File\repository\xycmdbsync\config\models\secure_cloud_waf_engine.yaml
2025-08-18 11:02:38,774 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_BusinessRegion from E:\File\repository\xycmdbsync\config\models\sys_businessregion.yaml
2025-08-18 11:02:38,804 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DataCenter from E:\File\repository\xycmdbsync\config\models\sys_datacenter.yaml
2025-08-18 11:02:38,861 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DistributedStorage from E:\File\repository\xycmdbsync\config\models\sys_distributedstorage.yaml
2025-08-18 11:02:38,990 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model SYS_LUN
2025-08-18 11:02:38,990 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_LUN from E:\File\repository\xycmdbsync\config\models\sys_lun.yaml
2025-08-18 11:02:39,155 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model SYS_PhysicalServer
2025-08-18 11:02:39,155 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_PhysicalServer from E:\File\repository\xycmdbsync\config\models\sys_physicalserver.yaml
2025-08-18 11:02:39,198 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack from E:\File\repository\xycmdbsync\config\models\sys_rack.yaml
2025-08-18 11:02:39,252 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_IntegratedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_integratedcabinet.yaml
2025-08-18 11:02:39,294 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_ShieldedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_shieldedcabinet.yaml
2025-08-18 11:02:39,340 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_ResourcePool from E:\File\repository\xycmdbsync\config\models\sys_resourcepool.yaml
2025-08-18 11:02:39,363 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Room from E:\File\repository\xycmdbsync\config\models\sys_room.yaml
2025-08-18 11:02:39,412 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageCluster from E:\File\repository\xycmdbsync\config\models\sys_storagecluster.yaml
2025-08-18 11:02:39,468 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageNode from E:\File\repository\xycmdbsync\config\models\sys_storagenode.yaml
2025-08-18 11:02:39,529 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StoragePool from E:\File\repository\xycmdbsync\config\models\sys_storagepool.yaml
2025-08-18 11:02:39,701 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 7 check templates for model SYS_StorDevice
2025-08-18 11:02:39,701 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorDevice from E:\File\repository\xycmdbsync\config\models\sys_stordevice.yaml
2025-08-18 11:02:39,929 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model SYS_X86Server
2025-08-18 11:02:39,930 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_X86Server from E:\File\repository\xycmdbsync\config\models\sys_x86server.yaml
2025-08-18 11:02:39,930 - 9908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:50] - INFO: ���ü������
2025-08-18 11:02:40,046 - 9908-MainThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:45] - INFO: MySQL���ӳس�ʼ���ɹ�����С: 5
2025-08-18 11:02:40,050 - 9908-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_1
2025-08-18 11:02:40,051 - 9908-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_2
2025-08-18 11:02:40,051 - 9908-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:136] - INFO: Initialized cloud API client for source: source_1
2025-08-18 11:02:40,051 - 9908-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:150] - INFO: Initialized internal API client
2025-08-18 11:02:40,051 - 9908-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:212] - INFO: Loading data from cache
2025-08-18 11:02:40,065 - 9908-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-08-18 11:02:40,074 - 9908-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-08-18 11:02:40,086 - 9908-RestApiThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:47] - INFO: �����߳�: restful_api��Դ�ϱ��ӿ�.
2025-08-18 11:02:40,086 - 9908-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:34] - INFO: ������(��ʱ)��ʼ�����: ���=1Сʱ, ��ʼ=0��
2025-08-18 11:02:40,088 - 9908-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:35] - INFO: ִ��ʱ���: ['00:00', '01:00', '02:00', '03:00', '04:00', '05:00', '06:00', '07:00', '08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00', '21:00', '22:00', '23:00']
2025-08-18 11:02:40,088 - 9908-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:81] - INFO: �����߳�: async_model��ʱ����ͬ������
2025-08-18 11:02:40,088 - 9908-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:82] - INFO: ��������: ÿ1Сʱִ��һ�Σ���00:00��ʼ
2025-08-18 11:02:40,089 - 9908-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:83] - INFO: ִ��ʱ���: 00:00, 01:00, 02:00, 03:00, 04:00, 05:00, 06:00, 07:00, 08:00, 09:00, 10:00, 11:00, 12:00, 13:00, 14:00, 15:00, 16:00, 17:00, 18:00, 19:00, 20:00, 21:00, 22:00, 23:00
2025-08-18 11:02:40,089 - 9908-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:84] - INFO: ÿ��ִ�д���: 24 ��
2025-08-18 11:02:40,089 - 9908-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-18 12:00:00, ���뻹�� 3440.00 �룬������...
2025-08-18 11:03:51,222 - 13908-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:279] - INFO: ********************************************************************************
2025-08-18 11:03:51,222 - 13908-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:280] - INFO: ��־ϵͳ�ѳ�ʼ������־Ŀ¼: E:\File\repository\xycmdbsync\logs
2025-08-18 11:03:51,222 - 13908-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:281] - INFO: ��־ϵͳ�ѳ�ʼ������־��ת��СΪ: 300M
2025-08-18 11:03:51,223 - 13908-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:282] - INFO: ��־ϵͳ�ѳ�ʼ������־������СΪ50���ļ�
2025-08-18 11:03:51,223 - 13908-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:283] - INFO: ��־ϵͳ�ѳ�ʼ������ǰ��־����Ϊ��10
2025-08-18 11:03:51,224 - 13908-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:284] - INFO: ********************************************************************************
2025-08-18 11:03:51,281 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:44] - INFO: ��ʼ��������...
2025-08-18 11:03:51,331 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:69] - INFO: Loaded base config from E:\File\repository\xycmdbsync\config\base_config.yaml
2025-08-18 11:03:51,334 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:88] - INFO: Found cloud sources: source_1, source_2
2025-08-18 11:03:51,334 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for cloud_side from E:\File\repository\xycmdbsync\config\endpoints\cloud_side.yaml
2025-08-18 11:03:51,343 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for internal_side from E:\File\repository\xycmdbsync\config\endpoints\internal_side.yaml
2025-08-18 11:03:51,501 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:106] - INFO: Loaded transformers from E:\File\repository\xycmdbsync\config\transformers\common.yaml
2025-08-18 11:03:51,558 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_AZONE from E:\File\repository\xycmdbsync\config\models\cloud_azone.yaml
2025-08-18 11:03:51,727 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model CLOUD_BMS
2025-08-18 11:03:51,727 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_BMS from E:\File\repository\xycmdbsync\config\models\cloud_bms.yaml
2025-08-18 11:03:51,801 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cce_cluster.yaml
2025-08-18 11:03:51,865 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CONTAINER from E:\File\repository\xycmdbsync\config\models\cloud_cce_container.yaml
2025-08-18 11:03:51,913 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NAMESPACE from E:\File\repository\xycmdbsync\config\models\cloud_cce_namespace.yaml
2025-08-18 11:03:51,993 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NODE from E:\File\repository\xycmdbsync\config\models\cloud_cce_node.yaml
2025-08-18 11:03:52,058 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_POD from E:\File\repository\xycmdbsync\config\models\cloud_cce_pod.yaml
2025-08-18 11:03:52,126 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_SERVICE from E:\File\repository\xycmdbsync\config\models\cloud_cce_service.yaml
2025-08-18 11:03:52,256 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudCronJob
2025-08-18 11:03:52,257 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudCronJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_cronjob.yaml
2025-08-18 11:03:52,386 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudDaemonset
2025-08-18 11:03:52,387 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDaemonset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_daemonset.yaml
2025-08-18 11:03:52,533 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudDeployment
2025-08-18 11:03:52,533 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDeployment from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_deployment.yaml
2025-08-18 11:03:52,671 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudJob
2025-08-18 11:03:52,672 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_job.yaml
2025-08-18 11:03:52,805 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudStatefulset
2025-08-18 11:03:52,806 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudStatefulset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_statefulset.yaml
2025-08-18 11:03:52,876 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cluster.yaml
2025-08-18 11:03:53,123 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model CLOUD_HOST
2025-08-18 11:03:53,123 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_HOST from E:\File\repository\xycmdbsync\config\models\cloud_host.yaml
2025-08-18 11:03:53,174 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_OBS_BUCKET from E:\File\repository\xycmdbsync\config\models\cloud_obs_bucket.yaml
2025-08-18 11:03:53,227 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_SFS_SHARE from E:\File\repository\xycmdbsync\config\models\cloud_sfs_share.yaml
2025-08-18 11:03:53,362 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VM_NOVA from E:\File\repository\xycmdbsync\config\models\cloud_vm_nova.yaml
2025-08-18 11:03:53,425 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VOLUME from E:\File\repository\xycmdbsync\config\models\cloud_volume.yaml
2025-08-18 11:03:53,577 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model cloud_kafka
2025-08-18 11:03:53,577 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_kafka from E:\File\repository\xycmdbsync\config\models\middleware_kafka.yaml
2025-08-18 11:03:53,640 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_nacos from E:\File\repository\xycmdbsync\config\models\middleware_nacos.yaml
2025-08-18 11:03:53,716 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_redis from E:\File\repository\xycmdbsync\config\models\middleware_redis.yaml
2025-08-18 11:03:53,876 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model cloud_rocketmq
2025-08-18 11:03:53,876 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_rocketmq from E:\File\repository\xycmdbsync\config\models\middleware_rocket_mq.yaml
2025-08-18 11:03:54,005 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model access_optical_switch
2025-08-18 11:03:54,005 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_optical_switch from E:\File\repository\xycmdbsync\config\models\network_access_optical_switch.yaml
2025-08-18 11:03:54,140 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model access_switch
2025-08-18 11:03:54,140 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_switch from E:\File\repository\xycmdbsync\config\models\network_access_switch.yaml
2025-08-18 11:03:54,265 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model back_bone_router
2025-08-18 11:03:54,265 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for back_bone_router from E:\File\repository\xycmdbsync\config\models\network_backbone_router.yaml
2025-08-18 11:03:54,320 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_bandwidths from E:\File\repository\xycmdbsync\config\models\network_cloud_bandwidths.yaml
2025-08-18 11:03:54,383 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_floating_ips from E:\File\repository\xycmdbsync\config\models\network_cloud_floating_ips.yaml
2025-08-18 11:03:54,442 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_network from E:\File\repository\xycmdbsync\config\models\network_cloud_network.yaml
2025-08-18 11:03:54,510 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_port from E:\File\repository\xycmdbsync\config\models\network_cloud_port.yaml
2025-08-18 11:03:54,576 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_router from E:\File\repository\xycmdbsync\config\models\network_cloud_router.yaml
2025-08-18 11:03:54,637 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_subnet from E:\File\repository\xycmdbsync\config\models\network_cloud_subnet.yaml
2025-08-18 11:03:54,768 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model core_optical_switch
2025-08-18 11:03:54,768 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_optical_switch from E:\File\repository\xycmdbsync\config\models\network_core_optical_switch.yaml
2025-08-18 11:03:54,894 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model core_switch
2025-08-18 11:03:54,894 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_switch from E:\File\repository\xycmdbsync\config\models\network_core_switch.yaml
2025-08-18 11:03:55,019 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model network_device_port
2025-08-18 11:03:55,019 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for network_device_port from E:\File\repository\xycmdbsync\config\models\network_device_port.yaml
2025-08-18 11:03:55,136 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model firewall
2025-08-18 11:03:55,136 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall from E:\File\repository\xycmdbsync\config\models\network_fire_wall.yaml
2025-08-18 11:03:55,232 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall_policy_log from E:\File\repository\xycmdbsync\config\models\network_fire_wall_policy_log.yaml
2025-08-18 11:03:55,352 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model land_balance_device
2025-08-18 11:03:55,352 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for land_balance_device from E:\File\repository\xycmdbsync\config\models\network_land_balance_device.yaml
2025-08-18 11:03:55,401 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool.yaml
2025-08-18 11:03:55,450 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool_men from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool_men.yaml
2025-08-18 11:03:55,513 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_service from E:\File\repository\xycmdbsync\config\models\network_load_balance_service.yaml
2025-08-18 11:03:55,637 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model non_backbone_router
2025-08-18 11:03:55,637 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for non_backbone_router from E:\File\repository\xycmdbsync\config\models\network_non_backbone_router.yaml
2025-08-18 11:03:55,701 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for sys_deviceLink from E:\File\repository\xycmdbsync\config\models\network_sys_devicelink.yaml
2025-08-18 11:03:55,782 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_waf_engine from E:\File\repository\xycmdbsync\config\models\secure_cloud_waf_engine.yaml
2025-08-18 11:03:55,859 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_BusinessRegion from E:\File\repository\xycmdbsync\config\models\sys_businessregion.yaml
2025-08-18 11:03:55,888 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DataCenter from E:\File\repository\xycmdbsync\config\models\sys_datacenter.yaml
2025-08-18 11:03:55,940 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DistributedStorage from E:\File\repository\xycmdbsync\config\models\sys_distributedstorage.yaml
2025-08-18 11:03:56,073 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model SYS_LUN
2025-08-18 11:03:56,073 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_LUN from E:\File\repository\xycmdbsync\config\models\sys_lun.yaml
2025-08-18 11:03:56,234 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model SYS_PhysicalServer
2025-08-18 11:03:56,234 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_PhysicalServer from E:\File\repository\xycmdbsync\config\models\sys_physicalserver.yaml
2025-08-18 11:03:56,278 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack from E:\File\repository\xycmdbsync\config\models\sys_rack.yaml
2025-08-18 11:03:56,330 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_IntegratedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_integratedcabinet.yaml
2025-08-18 11:03:56,379 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_ShieldedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_shieldedcabinet.yaml
2025-08-18 11:03:56,424 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_ResourcePool from E:\File\repository\xycmdbsync\config\models\sys_resourcepool.yaml
2025-08-18 11:03:56,448 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Room from E:\File\repository\xycmdbsync\config\models\sys_room.yaml
2025-08-18 11:03:56,494 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageCluster from E:\File\repository\xycmdbsync\config\models\sys_storagecluster.yaml
2025-08-18 11:03:56,545 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageNode from E:\File\repository\xycmdbsync\config\models\sys_storagenode.yaml
2025-08-18 11:03:56,606 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StoragePool from E:\File\repository\xycmdbsync\config\models\sys_storagepool.yaml
2025-08-18 11:03:56,784 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 7 check templates for model SYS_StorDevice
2025-08-18 11:03:56,784 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorDevice from E:\File\repository\xycmdbsync\config\models\sys_stordevice.yaml
2025-08-18 11:03:57,004 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model SYS_X86Server
2025-08-18 11:03:57,004 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_X86Server from E:\File\repository\xycmdbsync\config\models\sys_x86server.yaml
2025-08-18 11:03:57,004 - 13908-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:50] - INFO: ���ü������
2025-08-18 11:03:57,119 - 13908-MainThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:45] - INFO: MySQL���ӳس�ʼ���ɹ�����С: 5
2025-08-18 11:03:57,123 - 13908-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_1
2025-08-18 11:03:57,124 - 13908-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_2
2025-08-18 11:03:57,124 - 13908-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:136] - INFO: Initialized cloud API client for source: source_1
2025-08-18 11:03:57,124 - 13908-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:150] - INFO: Initialized internal API client
2025-08-18 11:03:57,124 - 13908-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:212] - INFO: Loading data from cache
2025-08-18 11:03:57,138 - 13908-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-08-18 11:03:57,147 - 13908-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-08-18 11:03:57,160 - 13908-RestApiThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:47] - INFO: �����߳�: restful_api��Դ�ϱ��ӿ�.
2025-08-18 11:03:57,160 - 13908-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:208] - INFO: ������(����)��ʼ�����: ���=45����, ��ʼ=00:00
2025-08-18 11:03:57,174 - 13908-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:209] - INFO: ִ��ʱ���: ['00:00', '00:45', '01:30', '02:15', '03:00', '03:45', '04:30', '05:15', '06:00', '06:45', '07:30', '08:15', '09:00', '09:45', '10:30', '11:15', '12:00', '12:45', '13:30', '14:15', '15:00', '15:45', '16:30', '17:15', '18:00', '18:45', '19:30', '20:15', '21:00', '21:45', '22:30', '23:15']
2025-08-18 11:03:57,175 - 13908-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:81] - INFO: �����߳�: async_model��ʱ����ͬ������
2025-08-18 11:03:57,175 - 13908-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:82] - INFO: ��������: ÿ45����ִ��һ�Σ���00:00��ʼ
2025-08-18 11:03:57,176 - 13908-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:83] - INFO: ִ��ʱ���: 00:00, 00:45, 01:30, 02:15, 03:00, 03:45, 04:30, 05:15, 06:00, 06:45, 07:30, 08:15, 09:00, 09:45, 10:30, 11:15, 12:00, 12:45, 13:30, 14:15, 15:00, 15:45, 16:30, 17:15, 18:00, 18:45, 19:30, 20:15, 21:00, 21:45, 22:30, 23:15
2025-08-18 11:03:57,176 - 13908-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:84] - INFO: ÿ��ִ�д���: 32 ��
2025-08-18 11:03:57,176 - 13908-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-18 11:15:00, ���뻹�� 663.00 �룬������...
2025-08-18 11:04:25,846 - 8260-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:279] - INFO: ********************************************************************************
2025-08-18 11:04:25,846 - 8260-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:280] - INFO: ��־ϵͳ�ѳ�ʼ������־Ŀ¼: E:\File\repository\xycmdbsync\logs
2025-08-18 11:04:25,847 - 8260-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:281] - INFO: ��־ϵͳ�ѳ�ʼ������־��ת��СΪ: 300M
2025-08-18 11:04:25,847 - 8260-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:282] - INFO: ��־ϵͳ�ѳ�ʼ������־������СΪ50���ļ�
2025-08-18 11:04:25,847 - 8260-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:283] - INFO: ��־ϵͳ�ѳ�ʼ������ǰ��־����Ϊ��10
2025-08-18 11:04:25,848 - 8260-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:284] - INFO: ********************************************************************************
2025-08-18 11:04:25,905 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:44] - INFO: ��ʼ��������...
2025-08-18 11:04:25,955 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:69] - INFO: Loaded base config from E:\File\repository\xycmdbsync\config\base_config.yaml
2025-08-18 11:04:25,959 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:88] - INFO: Found cloud sources: source_1, source_2
2025-08-18 11:04:25,959 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for cloud_side from E:\File\repository\xycmdbsync\config\endpoints\cloud_side.yaml
2025-08-18 11:04:25,969 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for internal_side from E:\File\repository\xycmdbsync\config\endpoints\internal_side.yaml
2025-08-18 11:04:26,145 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:106] - INFO: Loaded transformers from E:\File\repository\xycmdbsync\config\transformers\common.yaml
2025-08-18 11:04:26,205 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_AZONE from E:\File\repository\xycmdbsync\config\models\cloud_azone.yaml
2025-08-18 11:04:26,385 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model CLOUD_BMS
2025-08-18 11:04:26,385 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_BMS from E:\File\repository\xycmdbsync\config\models\cloud_bms.yaml
2025-08-18 11:04:26,461 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cce_cluster.yaml
2025-08-18 11:04:26,524 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CONTAINER from E:\File\repository\xycmdbsync\config\models\cloud_cce_container.yaml
2025-08-18 11:04:26,573 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NAMESPACE from E:\File\repository\xycmdbsync\config\models\cloud_cce_namespace.yaml
2025-08-18 11:04:26,655 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NODE from E:\File\repository\xycmdbsync\config\models\cloud_cce_node.yaml
2025-08-18 11:04:26,723 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_POD from E:\File\repository\xycmdbsync\config\models\cloud_cce_pod.yaml
2025-08-18 11:04:26,792 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_SERVICE from E:\File\repository\xycmdbsync\config\models\cloud_cce_service.yaml
2025-08-18 11:04:26,928 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudCronJob
2025-08-18 11:04:26,928 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudCronJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_cronjob.yaml
2025-08-18 11:04:27,059 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudDaemonset
2025-08-18 11:04:27,060 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDaemonset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_daemonset.yaml
2025-08-18 11:04:27,193 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudDeployment
2025-08-18 11:04:27,193 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDeployment from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_deployment.yaml
2025-08-18 11:04:27,320 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudJob
2025-08-18 11:04:27,321 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_job.yaml
2025-08-18 11:04:27,457 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudStatefulset
2025-08-18 11:04:27,457 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudStatefulset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_statefulset.yaml
2025-08-18 11:04:27,529 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cluster.yaml
2025-08-18 11:04:27,776 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model CLOUD_HOST
2025-08-18 11:04:27,777 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_HOST from E:\File\repository\xycmdbsync\config\models\cloud_host.yaml
2025-08-18 11:04:27,832 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_OBS_BUCKET from E:\File\repository\xycmdbsync\config\models\cloud_obs_bucket.yaml
2025-08-18 11:04:27,888 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_SFS_SHARE from E:\File\repository\xycmdbsync\config\models\cloud_sfs_share.yaml
2025-08-18 11:04:28,026 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VM_NOVA from E:\File\repository\xycmdbsync\config\models\cloud_vm_nova.yaml
2025-08-18 11:04:28,085 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VOLUME from E:\File\repository\xycmdbsync\config\models\cloud_volume.yaml
2025-08-18 11:04:28,239 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model cloud_kafka
2025-08-18 11:04:28,240 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_kafka from E:\File\repository\xycmdbsync\config\models\middleware_kafka.yaml
2025-08-18 11:04:28,302 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_nacos from E:\File\repository\xycmdbsync\config\models\middleware_nacos.yaml
2025-08-18 11:04:28,381 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_redis from E:\File\repository\xycmdbsync\config\models\middleware_redis.yaml
2025-08-18 11:04:28,539 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model cloud_rocketmq
2025-08-18 11:04:28,539 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_rocketmq from E:\File\repository\xycmdbsync\config\models\middleware_rocket_mq.yaml
2025-08-18 11:04:28,665 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model access_optical_switch
2025-08-18 11:04:28,666 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_optical_switch from E:\File\repository\xycmdbsync\config\models\network_access_optical_switch.yaml
2025-08-18 11:04:28,800 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model access_switch
2025-08-18 11:04:28,801 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_switch from E:\File\repository\xycmdbsync\config\models\network_access_switch.yaml
2025-08-18 11:04:28,924 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model back_bone_router
2025-08-18 11:04:28,924 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for back_bone_router from E:\File\repository\xycmdbsync\config\models\network_backbone_router.yaml
2025-08-18 11:04:28,977 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_bandwidths from E:\File\repository\xycmdbsync\config\models\network_cloud_bandwidths.yaml
2025-08-18 11:04:29,038 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_floating_ips from E:\File\repository\xycmdbsync\config\models\network_cloud_floating_ips.yaml
2025-08-18 11:04:29,098 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_network from E:\File\repository\xycmdbsync\config\models\network_cloud_network.yaml
2025-08-18 11:04:29,168 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_port from E:\File\repository\xycmdbsync\config\models\network_cloud_port.yaml
2025-08-18 11:04:29,230 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_router from E:\File\repository\xycmdbsync\config\models\network_cloud_router.yaml
2025-08-18 11:04:29,290 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_subnet from E:\File\repository\xycmdbsync\config\models\network_cloud_subnet.yaml
2025-08-18 11:04:29,418 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model core_optical_switch
2025-08-18 11:04:29,418 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_optical_switch from E:\File\repository\xycmdbsync\config\models\network_core_optical_switch.yaml
2025-08-18 11:04:29,548 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model core_switch
2025-08-18 11:04:29,548 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_switch from E:\File\repository\xycmdbsync\config\models\network_core_switch.yaml
2025-08-18 11:04:29,676 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model network_device_port
2025-08-18 11:04:29,676 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for network_device_port from E:\File\repository\xycmdbsync\config\models\network_device_port.yaml
2025-08-18 11:04:29,800 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model firewall
2025-08-18 11:04:29,801 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall from E:\File\repository\xycmdbsync\config\models\network_fire_wall.yaml
2025-08-18 11:04:29,904 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall_policy_log from E:\File\repository\xycmdbsync\config\models\network_fire_wall_policy_log.yaml
2025-08-18 11:04:30,026 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model land_balance_device
2025-08-18 11:04:30,027 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for land_balance_device from E:\File\repository\xycmdbsync\config\models\network_land_balance_device.yaml
2025-08-18 11:04:30,076 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool.yaml
2025-08-18 11:04:30,130 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool_men from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool_men.yaml
2025-08-18 11:04:30,193 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_service from E:\File\repository\xycmdbsync\config\models\network_load_balance_service.yaml
2025-08-18 11:04:30,315 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model non_backbone_router
2025-08-18 11:04:30,315 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for non_backbone_router from E:\File\repository\xycmdbsync\config\models\network_non_backbone_router.yaml
2025-08-18 11:04:30,383 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for sys_deviceLink from E:\File\repository\xycmdbsync\config\models\network_sys_devicelink.yaml
2025-08-18 11:04:30,467 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_waf_engine from E:\File\repository\xycmdbsync\config\models\secure_cloud_waf_engine.yaml
2025-08-18 11:04:30,546 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_BusinessRegion from E:\File\repository\xycmdbsync\config\models\sys_businessregion.yaml
2025-08-18 11:04:30,576 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DataCenter from E:\File\repository\xycmdbsync\config\models\sys_datacenter.yaml
2025-08-18 11:04:30,627 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DistributedStorage from E:\File\repository\xycmdbsync\config\models\sys_distributedstorage.yaml
2025-08-18 11:04:30,757 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model SYS_LUN
2025-08-18 11:04:30,757 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_LUN from E:\File\repository\xycmdbsync\config\models\sys_lun.yaml
2025-08-18 11:04:30,922 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model SYS_PhysicalServer
2025-08-18 11:04:30,922 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_PhysicalServer from E:\File\repository\xycmdbsync\config\models\sys_physicalserver.yaml
2025-08-18 11:04:30,965 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack from E:\File\repository\xycmdbsync\config\models\sys_rack.yaml
2025-08-18 11:04:31,013 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_IntegratedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_integratedcabinet.yaml
2025-08-18 11:04:31,057 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_ShieldedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_shieldedcabinet.yaml
2025-08-18 11:04:31,102 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_ResourcePool from E:\File\repository\xycmdbsync\config\models\sys_resourcepool.yaml
2025-08-18 11:04:31,126 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Room from E:\File\repository\xycmdbsync\config\models\sys_room.yaml
2025-08-18 11:04:31,171 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageCluster from E:\File\repository\xycmdbsync\config\models\sys_storagecluster.yaml
2025-08-18 11:04:31,223 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageNode from E:\File\repository\xycmdbsync\config\models\sys_storagenode.yaml
2025-08-18 11:04:31,285 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StoragePool from E:\File\repository\xycmdbsync\config\models\sys_storagepool.yaml
2025-08-18 11:04:31,457 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 7 check templates for model SYS_StorDevice
2025-08-18 11:04:31,457 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorDevice from E:\File\repository\xycmdbsync\config\models\sys_stordevice.yaml
2025-08-18 11:04:31,682 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model SYS_X86Server
2025-08-18 11:04:31,682 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_X86Server from E:\File\repository\xycmdbsync\config\models\sys_x86server.yaml
2025-08-18 11:04:31,682 - 8260-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:50] - INFO: ���ü������
2025-08-18 11:04:31,800 - 8260-MainThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:45] - INFO: MySQL���ӳس�ʼ���ɹ�����С: 5
2025-08-18 11:04:31,804 - 8260-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_1
2025-08-18 11:04:31,804 - 8260-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_2
2025-08-18 11:04:31,804 - 8260-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:136] - INFO: Initialized cloud API client for source: source_1
2025-08-18 11:04:31,804 - 8260-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:150] - INFO: Initialized internal API client
2025-08-18 11:04:31,805 - 8260-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:212] - INFO: Loading data from cache
2025-08-18 11:04:31,819 - 8260-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-08-18 11:04:31,828 - 8260-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-08-18 11:04:31,840 - 8260-RestApiThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:47] - INFO: �����߳�: restful_api��Դ�ϱ��ӿ�.
2025-08-18 11:04:31,840 - 8260-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:208] - INFO: ������(����)��ʼ�����: ���=60����, ��ʼ=00:00
2025-08-18 11:04:31,842 - 8260-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:209] - INFO: ִ��ʱ���: ['00:00', '01:00', '02:00', '03:00', '04:00', '05:00', '06:00', '07:00', '08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00', '21:00', '22:00', '23:00']
2025-08-18 11:04:31,842 - 8260-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:81] - INFO: �����߳�: async_model��ʱ����ͬ������
2025-08-18 11:04:31,842 - 8260-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:82] - INFO: ��������: ÿ60����ִ��һ�Σ���00:00��ʼ
2025-08-18 11:04:31,843 - 8260-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:83] - INFO: ִ��ʱ���: 00:00, 01:00, 02:00, 03:00, 04:00, 05:00, 06:00, 07:00, 08:00, 09:00, 10:00, 11:00, 12:00, 13:00, 14:00, 15:00, 16:00, 17:00, 18:00, 19:00, 20:00, 21:00, 22:00, 23:00
2025-08-18 11:04:31,843 - 8260-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:84] - INFO: ÿ��ִ�д���: 24 ��
2025-08-18 11:04:31,843 - 8260-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-18 12:00:00, ���뻹�� 3329.00 �룬������...
2025-08-18 11:05:50,612 - 20648-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:279] - INFO: ********************************************************************************
2025-08-18 11:05:50,613 - 20648-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:280] - INFO: ��־ϵͳ�ѳ�ʼ������־Ŀ¼: E:\File\repository\xycmdbsync\logs
2025-08-18 11:05:50,613 - 20648-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:281] - INFO: ��־ϵͳ�ѳ�ʼ������־��ת��СΪ: 300M
2025-08-18 11:05:50,613 - 20648-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:282] - INFO: ��־ϵͳ�ѳ�ʼ������־������СΪ50���ļ�
2025-08-18 11:05:50,614 - 20648-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:283] - INFO: ��־ϵͳ�ѳ�ʼ������ǰ��־����Ϊ��10
2025-08-18 11:05:50,614 - 20648-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:284] - INFO: ********************************************************************************
2025-08-18 11:05:50,671 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:44] - INFO: ��ʼ��������...
2025-08-18 11:05:50,721 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:69] - INFO: Loaded base config from E:\File\repository\xycmdbsync\config\base_config.yaml
2025-08-18 11:05:50,724 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:88] - INFO: Found cloud sources: source_1, source_2
2025-08-18 11:05:50,725 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for cloud_side from E:\File\repository\xycmdbsync\config\endpoints\cloud_side.yaml
2025-08-18 11:05:50,733 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for internal_side from E:\File\repository\xycmdbsync\config\endpoints\internal_side.yaml
2025-08-18 11:05:50,894 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:106] - INFO: Loaded transformers from E:\File\repository\xycmdbsync\config\transformers\common.yaml
2025-08-18 11:05:50,951 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_AZONE from E:\File\repository\xycmdbsync\config\models\cloud_azone.yaml
2025-08-18 11:05:51,118 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model CLOUD_BMS
2025-08-18 11:05:51,118 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_BMS from E:\File\repository\xycmdbsync\config\models\cloud_bms.yaml
2025-08-18 11:05:51,191 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cce_cluster.yaml
2025-08-18 11:05:51,256 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CONTAINER from E:\File\repository\xycmdbsync\config\models\cloud_cce_container.yaml
2025-08-18 11:05:51,306 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NAMESPACE from E:\File\repository\xycmdbsync\config\models\cloud_cce_namespace.yaml
2025-08-18 11:05:51,395 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NODE from E:\File\repository\xycmdbsync\config\models\cloud_cce_node.yaml
2025-08-18 11:05:51,467 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_POD from E:\File\repository\xycmdbsync\config\models\cloud_cce_pod.yaml
2025-08-18 11:05:51,537 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_SERVICE from E:\File\repository\xycmdbsync\config\models\cloud_cce_service.yaml
2025-08-18 11:05:51,665 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudCronJob
2025-08-18 11:05:51,665 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudCronJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_cronjob.yaml
2025-08-18 11:05:51,792 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudDaemonset
2025-08-18 11:05:51,792 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDaemonset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_daemonset.yaml
2025-08-18 11:05:51,926 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudDeployment
2025-08-18 11:05:51,926 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDeployment from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_deployment.yaml
2025-08-18 11:05:52,052 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudJob
2025-08-18 11:05:52,052 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_job.yaml
2025-08-18 11:05:52,190 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudStatefulset
2025-08-18 11:05:52,190 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudStatefulset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_statefulset.yaml
2025-08-18 11:05:52,260 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cluster.yaml
2025-08-18 11:05:52,503 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model CLOUD_HOST
2025-08-18 11:05:52,504 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_HOST from E:\File\repository\xycmdbsync\config\models\cloud_host.yaml
2025-08-18 11:05:52,556 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_OBS_BUCKET from E:\File\repository\xycmdbsync\config\models\cloud_obs_bucket.yaml
2025-08-18 11:05:52,611 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_SFS_SHARE from E:\File\repository\xycmdbsync\config\models\cloud_sfs_share.yaml
2025-08-18 11:05:52,747 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VM_NOVA from E:\File\repository\xycmdbsync\config\models\cloud_vm_nova.yaml
2025-08-18 11:05:52,806 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VOLUME from E:\File\repository\xycmdbsync\config\models\cloud_volume.yaml
2025-08-18 11:05:52,961 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model cloud_kafka
2025-08-18 11:05:52,961 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_kafka from E:\File\repository\xycmdbsync\config\models\middleware_kafka.yaml
2025-08-18 11:05:53,025 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_nacos from E:\File\repository\xycmdbsync\config\models\middleware_nacos.yaml
2025-08-18 11:05:53,102 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_redis from E:\File\repository\xycmdbsync\config\models\middleware_redis.yaml
2025-08-18 11:05:53,261 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model cloud_rocketmq
2025-08-18 11:05:53,262 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_rocketmq from E:\File\repository\xycmdbsync\config\models\middleware_rocket_mq.yaml
2025-08-18 11:05:53,386 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model access_optical_switch
2025-08-18 11:05:53,387 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_optical_switch from E:\File\repository\xycmdbsync\config\models\network_access_optical_switch.yaml
2025-08-18 11:05:53,519 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model access_switch
2025-08-18 11:05:53,519 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_switch from E:\File\repository\xycmdbsync\config\models\network_access_switch.yaml
2025-08-18 11:05:53,639 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model back_bone_router
2025-08-18 11:05:53,639 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for back_bone_router from E:\File\repository\xycmdbsync\config\models\network_backbone_router.yaml
2025-08-18 11:05:53,690 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_bandwidths from E:\File\repository\xycmdbsync\config\models\network_cloud_bandwidths.yaml
2025-08-18 11:05:53,749 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_floating_ips from E:\File\repository\xycmdbsync\config\models\network_cloud_floating_ips.yaml
2025-08-18 11:05:53,807 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_network from E:\File\repository\xycmdbsync\config\models\network_cloud_network.yaml
2025-08-18 11:05:53,875 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_port from E:\File\repository\xycmdbsync\config\models\network_cloud_port.yaml
2025-08-18 11:05:53,935 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_router from E:\File\repository\xycmdbsync\config\models\network_cloud_router.yaml
2025-08-18 11:05:53,993 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_subnet from E:\File\repository\xycmdbsync\config\models\network_cloud_subnet.yaml
2025-08-18 11:05:54,124 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model core_optical_switch
2025-08-18 11:05:54,125 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_optical_switch from E:\File\repository\xycmdbsync\config\models\network_core_optical_switch.yaml
2025-08-18 11:05:54,255 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model core_switch
2025-08-18 11:05:54,256 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_switch from E:\File\repository\xycmdbsync\config\models\network_core_switch.yaml
2025-08-18 11:05:54,386 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model network_device_port
2025-08-18 11:05:54,387 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for network_device_port from E:\File\repository\xycmdbsync\config\models\network_device_port.yaml
2025-08-18 11:05:54,513 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model firewall
2025-08-18 11:05:54,514 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall from E:\File\repository\xycmdbsync\config\models\network_fire_wall.yaml
2025-08-18 11:05:54,609 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall_policy_log from E:\File\repository\xycmdbsync\config\models\network_fire_wall_policy_log.yaml
2025-08-18 11:05:54,730 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model land_balance_device
2025-08-18 11:05:54,730 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for land_balance_device from E:\File\repository\xycmdbsync\config\models\network_land_balance_device.yaml
2025-08-18 11:05:54,779 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool.yaml
2025-08-18 11:05:54,828 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool_men from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool_men.yaml
2025-08-18 11:05:54,890 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_service from E:\File\repository\xycmdbsync\config\models\network_load_balance_service.yaml
2025-08-18 11:05:55,012 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model non_backbone_router
2025-08-18 11:05:55,013 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for non_backbone_router from E:\File\repository\xycmdbsync\config\models\network_non_backbone_router.yaml
2025-08-18 11:05:55,078 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for sys_deviceLink from E:\File\repository\xycmdbsync\config\models\network_sys_devicelink.yaml
2025-08-18 11:05:55,159 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_waf_engine from E:\File\repository\xycmdbsync\config\models\secure_cloud_waf_engine.yaml
2025-08-18 11:05:55,234 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_BusinessRegion from E:\File\repository\xycmdbsync\config\models\sys_businessregion.yaml
2025-08-18 11:05:55,264 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DataCenter from E:\File\repository\xycmdbsync\config\models\sys_datacenter.yaml
2025-08-18 11:05:55,315 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DistributedStorage from E:\File\repository\xycmdbsync\config\models\sys_distributedstorage.yaml
2025-08-18 11:05:55,445 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model SYS_LUN
2025-08-18 11:05:55,446 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_LUN from E:\File\repository\xycmdbsync\config\models\sys_lun.yaml
2025-08-18 11:05:55,612 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model SYS_PhysicalServer
2025-08-18 11:05:55,613 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_PhysicalServer from E:\File\repository\xycmdbsync\config\models\sys_physicalserver.yaml
2025-08-18 11:05:55,658 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack from E:\File\repository\xycmdbsync\config\models\sys_rack.yaml
2025-08-18 11:05:55,704 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_IntegratedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_integratedcabinet.yaml
2025-08-18 11:05:55,746 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_ShieldedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_shieldedcabinet.yaml
2025-08-18 11:05:55,790 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_ResourcePool from E:\File\repository\xycmdbsync\config\models\sys_resourcepool.yaml
2025-08-18 11:05:55,813 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Room from E:\File\repository\xycmdbsync\config\models\sys_room.yaml
2025-08-18 11:05:55,858 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageCluster from E:\File\repository\xycmdbsync\config\models\sys_storagecluster.yaml
2025-08-18 11:05:55,910 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageNode from E:\File\repository\xycmdbsync\config\models\sys_storagenode.yaml
2025-08-18 11:05:55,973 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StoragePool from E:\File\repository\xycmdbsync\config\models\sys_storagepool.yaml
2025-08-18 11:05:56,144 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 7 check templates for model SYS_StorDevice
2025-08-18 11:05:56,144 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorDevice from E:\File\repository\xycmdbsync\config\models\sys_stordevice.yaml
2025-08-18 11:05:56,372 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model SYS_X86Server
2025-08-18 11:05:56,373 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_X86Server from E:\File\repository\xycmdbsync\config\models\sys_x86server.yaml
2025-08-18 11:05:56,373 - 20648-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:50] - INFO: ���ü������
2025-08-18 11:05:56,488 - 20648-MainThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:45] - INFO: MySQL���ӳس�ʼ���ɹ�����С: 5
2025-08-18 11:05:56,491 - 20648-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_1
2025-08-18 11:05:56,492 - 20648-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_2
2025-08-18 11:05:56,492 - 20648-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:136] - INFO: Initialized cloud API client for source: source_1
2025-08-18 11:05:56,492 - 20648-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:150] - INFO: Initialized internal API client
2025-08-18 11:05:56,493 - 20648-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:212] - INFO: Loading data from cache
2025-08-18 11:05:56,506 - 20648-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-08-18 11:05:56,515 - 20648-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-08-18 11:05:56,527 - 20648-RestApiThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:47] - INFO: �����߳�: restful_api��Դ�ϱ��ӿ�.
2025-08-18 11:05:56,528 - 20648-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:208] - INFO: ������(����)��ʼ�����: ���=60����, ��ʼ=20:00
2025-08-18 11:05:56,529 - 20648-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:209] - INFO: ִ��ʱ���: ['20:00', '21:00', '22:00', '23:00']
2025-08-18 11:05:56,529 - 20648-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:81] - INFO: �����߳�: async_model��ʱ����ͬ������
2025-08-18 11:05:56,530 - 20648-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:82] - INFO: ��������: ÿ60����ִ��һ�Σ���20:00��ʼ
2025-08-18 11:05:56,530 - 20648-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:83] - INFO: ִ��ʱ���: 20:00, 21:00, 22:00, 23:00
2025-08-18 11:05:56,530 - 20648-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:84] - INFO: ÿ��ִ�д���: 4 ��
2025-08-18 11:05:56,531 - 20648-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-18 20:00:00, ���뻹�� 32044.00 �룬������...
2025-08-18 11:07:08,504 - 13764-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:279] - INFO: ********************************************************************************
2025-08-18 11:07:08,505 - 13764-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:280] - INFO: ��־ϵͳ�ѳ�ʼ������־Ŀ¼: E:\File\repository\xycmdbsync\logs
2025-08-18 11:07:08,506 - 13764-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:281] - INFO: ��־ϵͳ�ѳ�ʼ������־��ת��СΪ: 300M
2025-08-18 11:07:08,506 - 13764-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:282] - INFO: ��־ϵͳ�ѳ�ʼ������־������СΪ50���ļ�
2025-08-18 11:07:08,506 - 13764-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:283] - INFO: ��־ϵͳ�ѳ�ʼ������ǰ��־����Ϊ��10
2025-08-18 11:07:08,507 - 13764-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:284] - INFO: ********************************************************************************
2025-08-18 11:07:08,565 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:44] - INFO: ��ʼ��������...
2025-08-18 11:07:08,615 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:69] - INFO: Loaded base config from E:\File\repository\xycmdbsync\config\base_config.yaml
2025-08-18 11:07:08,619 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:88] - INFO: Found cloud sources: source_1, source_2
2025-08-18 11:07:08,619 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for cloud_side from E:\File\repository\xycmdbsync\config\endpoints\cloud_side.yaml
2025-08-18 11:07:08,628 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for internal_side from E:\File\repository\xycmdbsync\config\endpoints\internal_side.yaml
2025-08-18 11:07:08,787 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:106] - INFO: Loaded transformers from E:\File\repository\xycmdbsync\config\transformers\common.yaml
2025-08-18 11:07:08,842 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_AZONE from E:\File\repository\xycmdbsync\config\models\cloud_azone.yaml
2025-08-18 11:07:09,012 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model CLOUD_BMS
2025-08-18 11:07:09,013 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_BMS from E:\File\repository\xycmdbsync\config\models\cloud_bms.yaml
2025-08-18 11:07:09,085 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cce_cluster.yaml
2025-08-18 11:07:09,152 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CONTAINER from E:\File\repository\xycmdbsync\config\models\cloud_cce_container.yaml
2025-08-18 11:07:09,201 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NAMESPACE from E:\File\repository\xycmdbsync\config\models\cloud_cce_namespace.yaml
2025-08-18 11:07:09,289 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NODE from E:\File\repository\xycmdbsync\config\models\cloud_cce_node.yaml
2025-08-18 11:07:09,357 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_POD from E:\File\repository\xycmdbsync\config\models\cloud_cce_pod.yaml
2025-08-18 11:07:09,428 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_SERVICE from E:\File\repository\xycmdbsync\config\models\cloud_cce_service.yaml
2025-08-18 11:07:09,555 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudCronJob
2025-08-18 11:07:09,555 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudCronJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_cronjob.yaml
2025-08-18 11:07:09,684 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudDaemonset
2025-08-18 11:07:09,684 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDaemonset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_daemonset.yaml
2025-08-18 11:07:09,816 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudDeployment
2025-08-18 11:07:09,816 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDeployment from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_deployment.yaml
2025-08-18 11:07:09,943 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudJob
2025-08-18 11:07:09,944 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_job.yaml
2025-08-18 11:07:10,078 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model CloudStatefulset
2025-08-18 11:07:10,078 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudStatefulset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_statefulset.yaml
2025-08-18 11:07:10,152 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cluster.yaml
2025-08-18 11:07:10,403 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model CLOUD_HOST
2025-08-18 11:07:10,404 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_HOST from E:\File\repository\xycmdbsync\config\models\cloud_host.yaml
2025-08-18 11:07:10,457 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_OBS_BUCKET from E:\File\repository\xycmdbsync\config\models\cloud_obs_bucket.yaml
2025-08-18 11:07:10,513 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_SFS_SHARE from E:\File\repository\xycmdbsync\config\models\cloud_sfs_share.yaml
2025-08-18 11:07:10,650 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VM_NOVA from E:\File\repository\xycmdbsync\config\models\cloud_vm_nova.yaml
2025-08-18 11:07:10,713 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VOLUME from E:\File\repository\xycmdbsync\config\models\cloud_volume.yaml
2025-08-18 11:07:10,871 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model cloud_kafka
2025-08-18 11:07:10,872 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_kafka from E:\File\repository\xycmdbsync\config\models\middleware_kafka.yaml
2025-08-18 11:07:10,935 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_nacos from E:\File\repository\xycmdbsync\config\models\middleware_nacos.yaml
2025-08-18 11:07:11,018 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_redis from E:\File\repository\xycmdbsync\config\models\middleware_redis.yaml
2025-08-18 11:07:11,178 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model cloud_rocketmq
2025-08-18 11:07:11,179 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_rocketmq from E:\File\repository\xycmdbsync\config\models\middleware_rocket_mq.yaml
2025-08-18 11:07:11,305 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model access_optical_switch
2025-08-18 11:07:11,305 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_optical_switch from E:\File\repository\xycmdbsync\config\models\network_access_optical_switch.yaml
2025-08-18 11:07:11,446 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model access_switch
2025-08-18 11:07:11,446 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_switch from E:\File\repository\xycmdbsync\config\models\network_access_switch.yaml
2025-08-18 11:07:11,567 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model back_bone_router
2025-08-18 11:07:11,567 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for back_bone_router from E:\File\repository\xycmdbsync\config\models\network_backbone_router.yaml
2025-08-18 11:07:11,619 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_bandwidths from E:\File\repository\xycmdbsync\config\models\network_cloud_bandwidths.yaml
2025-08-18 11:07:11,679 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_floating_ips from E:\File\repository\xycmdbsync\config\models\network_cloud_floating_ips.yaml
2025-08-18 11:07:11,737 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_network from E:\File\repository\xycmdbsync\config\models\network_cloud_network.yaml
2025-08-18 11:07:11,804 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_port from E:\File\repository\xycmdbsync\config\models\network_cloud_port.yaml
2025-08-18 11:07:11,866 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_router from E:\File\repository\xycmdbsync\config\models\network_cloud_router.yaml
2025-08-18 11:07:11,925 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_subnet from E:\File\repository\xycmdbsync\config\models\network_cloud_subnet.yaml
2025-08-18 11:07:12,055 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model core_optical_switch
2025-08-18 11:07:12,056 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_optical_switch from E:\File\repository\xycmdbsync\config\models\network_core_optical_switch.yaml
2025-08-18 11:07:12,183 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model core_switch
2025-08-18 11:07:12,184 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_switch from E:\File\repository\xycmdbsync\config\models\network_core_switch.yaml
2025-08-18 11:07:12,309 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model network_device_port
2025-08-18 11:07:12,309 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for network_device_port from E:\File\repository\xycmdbsync\config\models\network_device_port.yaml
2025-08-18 11:07:12,434 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model firewall
2025-08-18 11:07:12,435 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall from E:\File\repository\xycmdbsync\config\models\network_fire_wall.yaml
2025-08-18 11:07:12,539 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall_policy_log from E:\File\repository\xycmdbsync\config\models\network_fire_wall_policy_log.yaml
2025-08-18 11:07:12,662 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model land_balance_device
2025-08-18 11:07:12,662 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for land_balance_device from E:\File\repository\xycmdbsync\config\models\network_land_balance_device.yaml
2025-08-18 11:07:12,712 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool.yaml
2025-08-18 11:07:12,761 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool_men from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool_men.yaml
2025-08-18 11:07:12,828 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_service from E:\File\repository\xycmdbsync\config\models\network_load_balance_service.yaml
2025-08-18 11:07:12,954 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 3 check templates for model non_backbone_router
2025-08-18 11:07:12,955 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for non_backbone_router from E:\File\repository\xycmdbsync\config\models\network_non_backbone_router.yaml
2025-08-18 11:07:13,019 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for sys_deviceLink from E:\File\repository\xycmdbsync\config\models\network_sys_devicelink.yaml
2025-08-18 11:07:13,101 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_waf_engine from E:\File\repository\xycmdbsync\config\models\secure_cloud_waf_engine.yaml
2025-08-18 11:07:13,214 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_BusinessRegion from E:\File\repository\xycmdbsync\config\models\sys_businessregion.yaml
2025-08-18 11:07:13,252 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DataCenter from E:\File\repository\xycmdbsync\config\models\sys_datacenter.yaml
2025-08-18 11:07:13,319 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DistributedStorage from E:\File\repository\xycmdbsync\config\models\sys_distributedstorage.yaml
2025-08-18 11:07:13,516 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 1 check templates for model SYS_LUN
2025-08-18 11:07:13,517 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_LUN from E:\File\repository\xycmdbsync\config\models\sys_lun.yaml
2025-08-18 11:07:13,688 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model SYS_PhysicalServer
2025-08-18 11:07:13,688 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_PhysicalServer from E:\File\repository\xycmdbsync\config\models\sys_physicalserver.yaml
2025-08-18 11:07:13,730 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack from E:\File\repository\xycmdbsync\config\models\sys_rack.yaml
2025-08-18 11:07:13,776 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_IntegratedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_integratedcabinet.yaml
2025-08-18 11:07:13,819 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_ShieldedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_shieldedcabinet.yaml
2025-08-18 11:07:13,866 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_ResourcePool from E:\File\repository\xycmdbsync\config\models\sys_resourcepool.yaml
2025-08-18 11:07:13,890 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Room from E:\File\repository\xycmdbsync\config\models\sys_room.yaml
2025-08-18 11:07:13,936 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageCluster from E:\File\repository\xycmdbsync\config\models\sys_storagecluster.yaml
2025-08-18 11:07:13,994 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageNode from E:\File\repository\xycmdbsync\config\models\sys_storagenode.yaml
2025-08-18 11:07:14,058 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StoragePool from E:\File\repository\xycmdbsync\config\models\sys_storagepool.yaml
2025-08-18 11:07:14,232 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 7 check templates for model SYS_StorDevice
2025-08-18 11:07:14,232 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorDevice from E:\File\repository\xycmdbsync\config\models\sys_stordevice.yaml
2025-08-18 11:07:14,457 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:292] - DEBUG: Processed 5 check templates for model SYS_X86Server
2025-08-18 11:07:14,457 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_X86Server from E:\File\repository\xycmdbsync\config\models\sys_x86server.yaml
2025-08-18 11:07:14,457 - 13764-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:50] - INFO: ���ü������
2025-08-18 11:07:14,577 - 13764-MainThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:45] - INFO: MySQL���ӳس�ʼ���ɹ�����С: 5
2025-08-18 11:07:14,581 - 13764-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_1
2025-08-18 11:07:14,581 - 13764-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_2
2025-08-18 11:07:14,582 - 13764-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:136] - INFO: Initialized cloud API client for source: source_1
2025-08-18 11:07:14,582 - 13764-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:150] - INFO: Initialized internal API client
2025-08-18 11:07:14,582 - 13764-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:212] - INFO: Loading data from cache
2025-08-18 11:07:14,596 - 13764-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-08-18 11:07:14,605 - 13764-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:325] - INFO: Sync record database tables initialized successfully
2025-08-18 11:07:14,617 - 13764-RestApiThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:47] - INFO: �����߳�: restful_api��Դ�ϱ��ӿ�.
2025-08-18 11:07:14,617 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:208] - INFO: ������(����)��ʼ�����: ���=30����, ��ʼ=00:00
2025-08-18 11:07:14,619 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:209] - INFO: ִ��ʱ���: ['00:00', '00:30', '01:00', '01:30', '02:00', '02:30', '03:00', '03:30', '04:00', '04:30', '05:00', '05:30', '06:00', '06:30', '07:00', '07:30', '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00', '17:30', '18:00', '18:30', '19:00', '19:30', '20:00', '20:30', '21:00', '21:30', '22:00', '22:30', '23:00', '23:30']
2025-08-18 11:07:14,620 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:81] - INFO: �����߳�: async_model��ʱ����ͬ������
2025-08-18 11:07:14,620 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:82] - INFO: ��������: ÿ30����ִ��һ�Σ���00:00��ʼ
2025-08-18 11:07:14,620 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:83] - INFO: ִ��ʱ���: 00:00, 00:30, 01:00, 01:30, 02:00, 02:30, 03:00, 03:30, 04:00, 04:30, 05:00, 05:30, 06:00, 06:30, 07:00, 07:30, 08:00, 08:30, 09:00, 09:30, 10:00, 10:30, 11:00, 11:30, 12:00, 12:30, 13:00, 13:30, 14:00, 14:30, 15:00, 15:30, 16:00, 16:30, 17:00, 17:30, 18:00, 18:30, 19:00, 19:30, 20:00, 20:30, 21:00, 21:30, 22:00, 22:30, 23:00, 23:30
2025-08-18 11:07:14,621 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:84] - INFO: ÿ��ִ�д���: 48 ��
2025-08-18 11:07:14,621 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-18 11:30:00, ���뻹�� 1366.00 �룬������...
2025-08-18 11:30:00,622 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:161] - INFO: ****************����ִ��ͬ�� (30���Ӽ��)**********************
2025-08-18 11:30:00,622 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:161] - WARNING: ���֤Ŀ¼������: E:\File\repository\xycmdbsync\config\license
2025-08-18 11:30:00,622 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:164] - INFO: ���Դ����ݿ��ȡ���֤��Ϣ
2025-08-18 11:30:00,625 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:73] - INFO: Connection to MySQL DB successful
2025-08-18 11:30:00,627 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:88] - INFO: MySQL connection is closed
2025-08-18 11:30:00,627 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:170] - INFO: �ɹ������ݿ��ȡ���֤��Ϣ: XYCMDB
2025-08-18 11:30:00,673 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:165] - INFO: **************License�ѹ���*******************
2025-08-18 11:30:00,674 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-18 12:00:00, ���뻹�� 1800.00 �룬������...
2025-08-18 12:00:00,675 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:161] - INFO: ****************����ִ��ͬ�� (30���Ӽ��)**********************
2025-08-18 12:00:00,675 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:161] - WARNING: ���֤Ŀ¼������: E:\File\repository\xycmdbsync\config\license
2025-08-18 12:00:00,675 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:164] - INFO: ���Դ����ݿ��ȡ���֤��Ϣ
2025-08-18 12:00:00,678 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:73] - INFO: Connection to MySQL DB successful
2025-08-18 12:00:00,679 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:88] - INFO: MySQL connection is closed
2025-08-18 12:00:00,679 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:170] - INFO: �ɹ������ݿ��ȡ���֤��Ϣ: XYCMDB
2025-08-18 12:00:00,700 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:165] - INFO: **************License�ѹ���*******************
2025-08-18 12:00:00,701 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-18 12:30:00, ���뻹�� 1800.00 �룬������...
2025-08-18 12:30:00,701 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:161] - INFO: ****************����ִ��ͬ�� (30���Ӽ��)**********************
2025-08-18 12:30:00,701 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:161] - WARNING: ���֤Ŀ¼������: E:\File\repository\xycmdbsync\config\license
2025-08-18 12:30:00,701 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:164] - INFO: ���Դ����ݿ��ȡ���֤��Ϣ
2025-08-18 12:30:00,704 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:73] - INFO: Connection to MySQL DB successful
2025-08-18 12:30:00,705 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:88] - INFO: MySQL connection is closed
2025-08-18 12:30:00,705 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:170] - INFO: �ɹ������ݿ��ȡ���֤��Ϣ: XYCMDB
2025-08-18 12:30:00,725 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:165] - INFO: **************License�ѹ���*******************
2025-08-18 12:30:00,725 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-18 13:00:00, ���뻹�� 1800.00 �룬������...
2025-08-18 13:00:00,726 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:161] - INFO: ****************����ִ��ͬ�� (30���Ӽ��)**********************
2025-08-18 13:00:00,726 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:161] - WARNING: ���֤Ŀ¼������: E:\File\repository\xycmdbsync\config\license
2025-08-18 13:00:00,726 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:164] - INFO: ���Դ����ݿ��ȡ���֤��Ϣ
2025-08-18 13:00:00,729 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:73] - INFO: Connection to MySQL DB successful
2025-08-18 13:00:00,730 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:88] - INFO: MySQL connection is closed
2025-08-18 13:00:00,730 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:170] - INFO: �ɹ������ݿ��ȡ���֤��Ϣ: XYCMDB
2025-08-18 13:00:00,750 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:165] - INFO: **************License�ѹ���*******************
2025-08-18 13:00:00,750 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-18 13:30:00, ���뻹�� 1800.00 �룬������...
2025-08-18 13:30:00,750 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:161] - INFO: ****************����ִ��ͬ�� (30���Ӽ��)**********************
2025-08-18 13:30:00,751 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:161] - WARNING: ���֤Ŀ¼������: E:\File\repository\xycmdbsync\config\license
2025-08-18 13:30:00,751 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:164] - INFO: ���Դ����ݿ��ȡ���֤��Ϣ
2025-08-18 13:30:00,754 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:73] - INFO: Connection to MySQL DB successful
2025-08-18 13:30:00,755 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:88] - INFO: MySQL connection is closed
2025-08-18 13:30:00,755 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:170] - INFO: �ɹ������ݿ��ȡ���֤��Ϣ: XYCMDB
2025-08-18 13:30:00,776 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:165] - INFO: **************License�ѹ���*******************
2025-08-18 13:30:00,777 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-18 14:00:00, ���뻹�� 1800.00 �룬������...
2025-08-18 14:00:00,778 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:161] - INFO: ****************����ִ��ͬ�� (30���Ӽ��)**********************
2025-08-18 14:00:00,778 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:161] - WARNING: ���֤Ŀ¼������: E:\File\repository\xycmdbsync\config\license
2025-08-18 14:00:00,778 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:164] - INFO: ���Դ����ݿ��ȡ���֤��Ϣ
2025-08-18 14:00:00,781 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:73] - INFO: Connection to MySQL DB successful
2025-08-18 14:00:00,782 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:88] - INFO: MySQL connection is closed
2025-08-18 14:00:00,783 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:170] - INFO: �ɹ������ݿ��ȡ���֤��Ϣ: XYCMDB
2025-08-18 14:00:00,806 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:165] - INFO: **************License�ѹ���*******************
2025-08-18 14:00:00,806 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-18 14:30:00, ���뻹�� 1800.00 �룬������...
2025-08-18 14:30:00,806 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:161] - INFO: ****************����ִ��ͬ�� (30���Ӽ��)**********************
2025-08-18 14:30:00,806 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:161] - WARNING: ���֤Ŀ¼������: E:\File\repository\xycmdbsync\config\license
2025-08-18 14:30:00,806 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:164] - INFO: ���Դ����ݿ��ȡ���֤��Ϣ
2025-08-18 14:30:00,809 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:73] - INFO: Connection to MySQL DB successful
2025-08-18 14:30:00,811 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:88] - INFO: MySQL connection is closed
2025-08-18 14:30:00,811 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:170] - INFO: �ɹ������ݿ��ȡ���֤��Ϣ: XYCMDB
2025-08-18 14:30:00,832 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:165] - INFO: **************License�ѹ���*******************
2025-08-18 14:30:00,832 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-18 15:00:00, ���뻹�� 1800.00 �룬������...
2025-08-18 15:00:00,833 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:161] - INFO: ****************����ִ��ͬ�� (30���Ӽ��)**********************
2025-08-18 15:00:00,834 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:161] - WARNING: ���֤Ŀ¼������: E:\File\repository\xycmdbsync\config\license
2025-08-18 15:00:00,834 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:164] - INFO: ���Դ����ݿ��ȡ���֤��Ϣ
2025-08-18 15:00:00,837 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:73] - INFO: Connection to MySQL DB successful
2025-08-18 15:00:00,839 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:88] - INFO: MySQL connection is closed
2025-08-18 15:00:00,839 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:170] - INFO: �ɹ������ݿ��ȡ���֤��Ϣ: XYCMDB
2025-08-18 15:00:00,858 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:165] - INFO: **************License�ѹ���*******************
2025-08-18 15:00:00,859 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-18 15:30:00, ���뻹�� 1800.00 �룬������...
2025-08-18 15:30:00,859 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:161] - INFO: ****************����ִ��ͬ�� (30���Ӽ��)**********************
2025-08-18 15:30:00,860 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:161] - WARNING: ���֤Ŀ¼������: E:\File\repository\xycmdbsync\config\license
2025-08-18 15:30:00,860 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:164] - INFO: ���Դ����ݿ��ȡ���֤��Ϣ
2025-08-18 15:30:00,863 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:73] - INFO: Connection to MySQL DB successful
2025-08-18 15:30:00,865 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:88] - INFO: MySQL connection is closed
2025-08-18 15:30:00,865 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:170] - INFO: �ɹ������ݿ��ȡ���֤��Ϣ: XYCMDB
2025-08-18 15:30:00,886 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:165] - INFO: **************License�ѹ���*******************
2025-08-18 15:30:00,886 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-18 16:00:00, ���뻹�� 1800.00 �룬������...
2025-08-18 16:00:00,887 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:161] - INFO: ****************����ִ��ͬ�� (30���Ӽ��)**********************
2025-08-18 16:00:00,888 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:161] - WARNING: ���֤Ŀ¼������: E:\File\repository\xycmdbsync\config\license
2025-08-18 16:00:00,888 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:164] - INFO: ���Դ����ݿ��ȡ���֤��Ϣ
2025-08-18 16:00:00,890 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:73] - INFO: Connection to MySQL DB successful
2025-08-18 16:00:00,892 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:88] - INFO: MySQL connection is closed
2025-08-18 16:00:00,892 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:170] - INFO: �ɹ������ݿ��ȡ���֤��Ϣ: XYCMDB
2025-08-18 16:00:00,912 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:165] - INFO: **************License�ѹ���*******************
2025-08-18 16:00:00,912 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-18 16:30:00, ���뻹�� 1800.00 �룬������...
2025-08-18 16:30:00,912 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:161] - INFO: ****************����ִ��ͬ�� (30���Ӽ��)**********************
2025-08-18 16:30:00,913 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:161] - WARNING: ���֤Ŀ¼������: E:\File\repository\xycmdbsync\config\license
2025-08-18 16:30:00,913 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:164] - INFO: ���Դ����ݿ��ȡ���֤��Ϣ
2025-08-18 16:30:00,916 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:73] - INFO: Connection to MySQL DB successful
2025-08-18 16:30:00,918 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:88] - INFO: MySQL connection is closed
2025-08-18 16:30:00,918 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:170] - INFO: �ɹ������ݿ��ȡ���֤��Ϣ: XYCMDB
2025-08-18 16:30:00,938 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:165] - INFO: **************License�ѹ���*******************
2025-08-18 16:30:00,938 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-18 17:00:00, ���뻹�� 1800.00 �룬������...
2025-08-18 17:00:00,939 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:161] - INFO: ****************����ִ��ͬ�� (30���Ӽ��)**********************
2025-08-18 17:00:00,939 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:161] - WARNING: ���֤Ŀ¼������: E:\File\repository\xycmdbsync\config\license
2025-08-18 17:00:00,939 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:164] - INFO: ���Դ����ݿ��ȡ���֤��Ϣ
2025-08-18 17:00:00,945 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:73] - INFO: Connection to MySQL DB successful
2025-08-18 17:00:00,947 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:88] - INFO: MySQL connection is closed
2025-08-18 17:00:00,947 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:170] - INFO: �ɹ������ݿ��ȡ���֤��Ϣ: XYCMDB
2025-08-18 17:00:00,977 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:165] - INFO: **************License�ѹ���*******************
2025-08-18 17:00:00,977 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-18 17:30:00, ���뻹�� 1800.00 �룬������...
2025-08-18 17:30:00,978 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:161] - INFO: ****************����ִ��ͬ�� (30���Ӽ��)**********************
2025-08-18 17:30:00,979 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:161] - WARNING: ���֤Ŀ¼������: E:\File\repository\xycmdbsync\config\license
2025-08-18 17:30:00,979 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:164] - INFO: ���Դ����ݿ��ȡ���֤��Ϣ
2025-08-18 17:30:00,982 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:73] - INFO: Connection to MySQL DB successful
2025-08-18 17:30:00,983 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:88] - INFO: MySQL connection is closed
2025-08-18 17:30:00,984 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:170] - INFO: �ɹ������ݿ��ȡ���֤��Ϣ: XYCMDB
2025-08-18 17:30:01,006 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:165] - INFO: **************License�ѹ���*******************
2025-08-18 17:30:01,006 - 13764-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-18 18:00:00, ���뻹�� 1799.00 �룬������...
