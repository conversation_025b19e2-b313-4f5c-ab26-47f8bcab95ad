2025-08-25 16:33:47,558 - 5436-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:279] - INFO: ********************************************************************************
2025-08-25 16:33:47,559 - 5436-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:280] - INFO: ��־ϵͳ�ѳ�ʼ������־Ŀ¼: E:\File\repository\xycmdbsync\logs
2025-08-25 16:33:47,560 - 5436-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:281] - INFO: ��־ϵͳ�ѳ�ʼ������־��ת��СΪ: 300M
2025-08-25 16:33:47,561 - 5436-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:282] - INFO: ��־ϵͳ�ѳ�ʼ������־������СΪ50���ļ�
2025-08-25 16:33:47,561 - 5436-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:283] - INFO: ��־ϵͳ�ѳ�ʼ������ǰ��־����Ϊ��20
2025-08-25 16:33:47,562 - 5436-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:284] - INFO: ********************************************************************************
2025-08-25 16:33:47,637 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:44] - INFO: ��ʼ��������...
2025-08-25 16:33:47,689 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:69] - INFO: Loaded base config from E:\File\repository\xycmdbsync\config\base_config.yaml
2025-08-25 16:33:47,693 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:88] - INFO: Found cloud sources: source_1, source_2
2025-08-25 16:33:47,693 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for cloud_side from E:\File\repository\xycmdbsync\config\endpoints\cloud_side.yaml
2025-08-25 16:33:47,703 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for internal_side from E:\File\repository\xycmdbsync\config\endpoints\internal_side.yaml
2025-08-25 16:33:47,877 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:106] - INFO: Loaded transformers from E:\File\repository\xycmdbsync\config\transformers\common.yaml
2025-08-25 16:33:47,934 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_AZONE from E:\File\repository\xycmdbsync\config\models\cloud_azone.yaml
2025-08-25 16:33:48,104 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_BMS from E:\File\repository\xycmdbsync\config\models\cloud_bms.yaml
2025-08-25 16:33:48,183 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cce_cluster.yaml
2025-08-25 16:33:48,247 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CONTAINER from E:\File\repository\xycmdbsync\config\models\cloud_cce_container.yaml
2025-08-25 16:33:48,298 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NAMESPACE from E:\File\repository\xycmdbsync\config\models\cloud_cce_namespace.yaml
2025-08-25 16:33:48,380 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NODE from E:\File\repository\xycmdbsync\config\models\cloud_cce_node.yaml
2025-08-25 16:33:48,446 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_POD from E:\File\repository\xycmdbsync\config\models\cloud_cce_pod.yaml
2025-08-25 16:33:48,515 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_SERVICE from E:\File\repository\xycmdbsync\config\models\cloud_cce_service.yaml
2025-08-25 16:33:48,644 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudCronJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_cronjob.yaml
2025-08-25 16:33:48,771 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDaemonset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_daemonset.yaml
2025-08-25 16:33:48,940 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDeployment from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_deployment.yaml
2025-08-25 16:33:49,121 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_job.yaml
2025-08-25 16:33:49,266 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudStatefulset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_statefulset.yaml
2025-08-25 16:33:49,343 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cluster.yaml
2025-08-25 16:33:49,612 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_HOST from E:\File\repository\xycmdbsync\config\models\cloud_host.yaml
2025-08-25 16:33:49,667 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_OBS_BUCKET from E:\File\repository\xycmdbsync\config\models\cloud_obs_bucket.yaml
2025-08-25 16:33:49,726 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_SFS_SHARE from E:\File\repository\xycmdbsync\config\models\cloud_sfs_share.yaml
2025-08-25 16:33:49,869 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VM_NOVA from E:\File\repository\xycmdbsync\config\models\cloud_vm_nova.yaml
2025-08-25 16:33:49,930 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VOLUME from E:\File\repository\xycmdbsync\config\models\cloud_volume.yaml
2025-08-25 16:33:50,085 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_kafka from E:\File\repository\xycmdbsync\config\models\middleware_kafka.yaml
2025-08-25 16:33:50,149 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_nacos from E:\File\repository\xycmdbsync\config\models\middleware_nacos.yaml
2025-08-25 16:33:50,228 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_redis from E:\File\repository\xycmdbsync\config\models\middleware_redis.yaml
2025-08-25 16:33:50,396 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_rocketmq from E:\File\repository\xycmdbsync\config\models\middleware_rocket_mq.yaml
2025-08-25 16:33:50,542 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_optical_switch from E:\File\repository\xycmdbsync\config\models\network_access_optical_switch.yaml
2025-08-25 16:33:50,670 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_switch from E:\File\repository\xycmdbsync\config\models\network_access_switch.yaml
2025-08-25 16:33:50,804 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for back_bone_router from E:\File\repository\xycmdbsync\config\models\network_backbone_router.yaml
2025-08-25 16:33:50,860 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_bandwidths from E:\File\repository\xycmdbsync\config\models\network_cloud_bandwidths.yaml
2025-08-25 16:33:50,922 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_floating_ips from E:\File\repository\xycmdbsync\config\models\network_cloud_floating_ips.yaml
2025-08-25 16:33:50,979 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_network from E:\File\repository\xycmdbsync\config\models\network_cloud_network.yaml
2025-08-25 16:33:51,049 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_port from E:\File\repository\xycmdbsync\config\models\network_cloud_port.yaml
2025-08-25 16:33:51,115 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_router from E:\File\repository\xycmdbsync\config\models\network_cloud_router.yaml
2025-08-25 16:33:51,179 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_subnet from E:\File\repository\xycmdbsync\config\models\network_cloud_subnet.yaml
2025-08-25 16:33:51,310 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_optical_switch from E:\File\repository\xycmdbsync\config\models\network_core_optical_switch.yaml
2025-08-25 16:33:51,439 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_switch from E:\File\repository\xycmdbsync\config\models\network_core_switch.yaml
2025-08-25 16:33:51,565 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for network_device_port from E:\File\repository\xycmdbsync\config\models\network_device_port.yaml
2025-08-25 16:33:51,687 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall from E:\File\repository\xycmdbsync\config\models\network_fire_wall.yaml
2025-08-25 16:33:51,784 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall_policy_log from E:\File\repository\xycmdbsync\config\models\network_fire_wall_policy_log.yaml
2025-08-25 16:33:51,907 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for land_balance_device from E:\File\repository\xycmdbsync\config\models\network_land_balance_device.yaml
2025-08-25 16:33:51,958 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool.yaml
2025-08-25 16:33:52,009 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool_men from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool_men.yaml
2025-08-25 16:33:52,073 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_service from E:\File\repository\xycmdbsync\config\models\network_load_balance_service.yaml
2025-08-25 16:33:52,204 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for non_backbone_router from E:\File\repository\xycmdbsync\config\models\network_non_backbone_router.yaml
2025-08-25 16:33:52,271 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for sys_deviceLink from E:\File\repository\xycmdbsync\config\models\network_sys_devicelink.yaml
2025-08-25 16:33:52,354 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_waf_engine from E:\File\repository\xycmdbsync\config\models\secure_cloud_waf_engine.yaml
2025-08-25 16:33:52,431 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_BusinessRegion from E:\File\repository\xycmdbsync\config\models\sys_businessregion.yaml
2025-08-25 16:33:52,465 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DataCenter from E:\File\repository\xycmdbsync\config\models\sys_datacenter.yaml
2025-08-25 16:33:52,518 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DistributedStorage from E:\File\repository\xycmdbsync\config\models\sys_distributedstorage.yaml
2025-08-25 16:33:52,656 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_LUN from E:\File\repository\xycmdbsync\config\models\sys_lun.yaml
2025-08-25 16:33:52,822 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_PhysicalServer from E:\File\repository\xycmdbsync\config\models\sys_physicalserver.yaml
2025-08-25 16:33:52,868 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack from E:\File\repository\xycmdbsync\config\models\sys_rack.yaml
2025-08-25 16:33:52,917 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_IntegratedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_integratedcabinet.yaml
2025-08-25 16:33:52,960 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_ShieldedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_shieldedcabinet.yaml
2025-08-25 16:33:53,006 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_ResourcePool from E:\File\repository\xycmdbsync\config\models\sys_resourcepool.yaml
2025-08-25 16:33:53,030 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Room from E:\File\repository\xycmdbsync\config\models\sys_room.yaml
2025-08-25 16:33:53,075 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageCluster from E:\File\repository\xycmdbsync\config\models\sys_storagecluster.yaml
2025-08-25 16:33:53,128 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageNode from E:\File\repository\xycmdbsync\config\models\sys_storagenode.yaml
2025-08-25 16:33:53,196 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StoragePool from E:\File\repository\xycmdbsync\config\models\sys_storagepool.yaml
2025-08-25 16:33:53,380 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorDevice from E:\File\repository\xycmdbsync\config\models\sys_stordevice.yaml
2025-08-25 16:33:53,609 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_X86Server from E:\File\repository\xycmdbsync\config\models\sys_x86server.yaml
2025-08-25 16:33:53,609 - 5436-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:50] - INFO: ���ü������
2025-08-25 16:33:53,872 - 5436-MainThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:45] - INFO: MySQL���ӳس�ʼ���ɹ�����С: 5
2025-08-25 16:33:53,877 - 5436-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_1
2025-08-25 16:33:53,877 - 5436-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_2
2025-08-25 16:33:53,878 - 5436-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:136] - INFO: Initialized cloud API client for source: source_1
2025-08-25 16:33:53,878 - 5436-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:150] - INFO: Initialized internal API client
2025-08-25 16:33:53,878 - 5436-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:212] - INFO: Loading data from cache
2025-08-25 16:34:07,083 - 5436-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:350] - INFO: Sync record database tables initialized successfully
2025-08-25 16:34:07,093 - 5436-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:350] - INFO: Sync record database tables initialized successfully
2025-08-25 16:34:07,117 - 5436-RestApiThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:47] - INFO: �����߳�: restful_api��Դ�ϱ��ӿ�.
2025-08-25 16:34:07,118 - 5436-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:208] - INFO: ������(����)��ʼ�����: ���=30����, ��ʼ=00:00
2025-08-25 16:34:07,119 - 5436-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:209] - INFO: ִ��ʱ���: ['00:00', '00:30', '01:00', '01:30', '02:00', '02:30', '03:00', '03:30', '04:00', '04:30', '05:00', '05:30', '06:00', '06:30', '07:00', '07:30', '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00', '17:30', '18:00', '18:30', '19:00', '19:30', '20:00', '20:30', '21:00', '21:30', '22:00', '22:30', '23:00', '23:30']
2025-08-25 16:34:07,120 - 5436-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:81] - INFO: �����߳�: async_model��ʱ����ͬ������
2025-08-25 16:34:07,120 - 5436-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:82] - INFO: ��������: ÿ30����ִ��һ�Σ���00:00��ʼ
2025-08-25 16:34:07,121 - 5436-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:83] - INFO: ִ��ʱ���: 00:00, 00:30, 01:00, 01:30, 02:00, 02:30, 03:00, 03:30, 04:00, 04:30, 05:00, 05:30, 06:00, 06:30, 07:00, 07:30, 08:00, 08:30, 09:00, 09:30, 10:00, 10:30, 11:00, 11:30, 12:00, 12:30, 13:00, 13:30, 14:00, 14:30, 15:00, 15:30, 16:00, 16:30, 17:00, 17:30, 18:00, 18:30, 19:00, 19:30, 20:00, 20:30, 21:00, 21:30, 22:00, 22:30, 23:00, 23:30
2025-08-25 16:34:07,121 - 5436-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:84] - INFO: ÿ��ִ�д���: 48 ��
2025-08-25 16:34:07,121 - 5436-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-25 17:00:00, ���뻹�� 1553.00 �룬������...
2025-08-25 16:34:55,018 - 3188-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:279] - INFO: ********************************************************************************
2025-08-25 16:34:55,018 - 3188-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:280] - INFO: ��־ϵͳ�ѳ�ʼ������־Ŀ¼: E:\File\repository\xycmdbsync\logs
2025-08-25 16:34:55,019 - 3188-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:281] - INFO: ��־ϵͳ�ѳ�ʼ������־��ת��СΪ: 300M
2025-08-25 16:34:55,019 - 3188-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:282] - INFO: ��־ϵͳ�ѳ�ʼ������־������СΪ50���ļ�
2025-08-25 16:34:55,020 - 3188-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:283] - INFO: ��־ϵͳ�ѳ�ʼ������ǰ��־����Ϊ��20
2025-08-25 16:34:55,020 - 3188-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:284] - INFO: ********************************************************************************
2025-08-25 16:34:55,091 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:44] - INFO: ��ʼ��������...
2025-08-25 16:34:55,147 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:69] - INFO: Loaded base config from E:\File\repository\xycmdbsync\config\base_config.yaml
2025-08-25 16:34:55,151 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:88] - INFO: Found cloud sources: source_1, source_2
2025-08-25 16:34:55,151 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for cloud_side from E:\File\repository\xycmdbsync\config\endpoints\cloud_side.yaml
2025-08-25 16:34:55,160 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for internal_side from E:\File\repository\xycmdbsync\config\endpoints\internal_side.yaml
2025-08-25 16:34:55,335 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:106] - INFO: Loaded transformers from E:\File\repository\xycmdbsync\config\transformers\common.yaml
2025-08-25 16:34:55,396 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_AZONE from E:\File\repository\xycmdbsync\config\models\cloud_azone.yaml
2025-08-25 16:34:55,573 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_BMS from E:\File\repository\xycmdbsync\config\models\cloud_bms.yaml
2025-08-25 16:34:55,649 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cce_cluster.yaml
2025-08-25 16:34:55,714 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CONTAINER from E:\File\repository\xycmdbsync\config\models\cloud_cce_container.yaml
2025-08-25 16:34:55,763 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NAMESPACE from E:\File\repository\xycmdbsync\config\models\cloud_cce_namespace.yaml
2025-08-25 16:34:55,846 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NODE from E:\File\repository\xycmdbsync\config\models\cloud_cce_node.yaml
2025-08-25 16:34:55,915 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_POD from E:\File\repository\xycmdbsync\config\models\cloud_cce_pod.yaml
2025-08-25 16:34:55,989 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_SERVICE from E:\File\repository\xycmdbsync\config\models\cloud_cce_service.yaml
2025-08-25 16:34:56,125 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudCronJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_cronjob.yaml
2025-08-25 16:34:56,256 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDaemonset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_daemonset.yaml
2025-08-25 16:34:56,389 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDeployment from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_deployment.yaml
2025-08-25 16:34:56,517 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_job.yaml
2025-08-25 16:34:56,718 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudStatefulset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_statefulset.yaml
2025-08-25 16:34:56,842 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cluster.yaml
2025-08-25 16:34:57,134 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_HOST from E:\File\repository\xycmdbsync\config\models\cloud_host.yaml
2025-08-25 16:34:57,192 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_OBS_BUCKET from E:\File\repository\xycmdbsync\config\models\cloud_obs_bucket.yaml
2025-08-25 16:34:57,250 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_SFS_SHARE from E:\File\repository\xycmdbsync\config\models\cloud_sfs_share.yaml
2025-08-25 16:34:57,388 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VM_NOVA from E:\File\repository\xycmdbsync\config\models\cloud_vm_nova.yaml
2025-08-25 16:34:57,449 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VOLUME from E:\File\repository\xycmdbsync\config\models\cloud_volume.yaml
2025-08-25 16:34:57,614 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_kafka from E:\File\repository\xycmdbsync\config\models\middleware_kafka.yaml
2025-08-25 16:34:57,680 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_nacos from E:\File\repository\xycmdbsync\config\models\middleware_nacos.yaml
2025-08-25 16:34:57,760 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_redis from E:\File\repository\xycmdbsync\config\models\middleware_redis.yaml
2025-08-25 16:34:57,921 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_rocketmq from E:\File\repository\xycmdbsync\config\models\middleware_rocket_mq.yaml
2025-08-25 16:34:58,049 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_optical_switch from E:\File\repository\xycmdbsync\config\models\network_access_optical_switch.yaml
2025-08-25 16:34:58,189 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_switch from E:\File\repository\xycmdbsync\config\models\network_access_switch.yaml
2025-08-25 16:34:58,319 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for back_bone_router from E:\File\repository\xycmdbsync\config\models\network_backbone_router.yaml
2025-08-25 16:34:58,372 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_bandwidths from E:\File\repository\xycmdbsync\config\models\network_cloud_bandwidths.yaml
2025-08-25 16:34:58,440 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_floating_ips from E:\File\repository\xycmdbsync\config\models\network_cloud_floating_ips.yaml
2025-08-25 16:34:58,502 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_network from E:\File\repository\xycmdbsync\config\models\network_cloud_network.yaml
2025-08-25 16:34:58,583 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_port from E:\File\repository\xycmdbsync\config\models\network_cloud_port.yaml
2025-08-25 16:34:58,649 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_router from E:\File\repository\xycmdbsync\config\models\network_cloud_router.yaml
2025-08-25 16:34:58,719 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_subnet from E:\File\repository\xycmdbsync\config\models\network_cloud_subnet.yaml
2025-08-25 16:34:58,860 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_optical_switch from E:\File\repository\xycmdbsync\config\models\network_core_optical_switch.yaml
2025-08-25 16:34:58,991 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_switch from E:\File\repository\xycmdbsync\config\models\network_core_switch.yaml
2025-08-25 16:34:59,122 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for network_device_port from E:\File\repository\xycmdbsync\config\models\network_device_port.yaml
2025-08-25 16:34:59,241 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall from E:\File\repository\xycmdbsync\config\models\network_fire_wall.yaml
2025-08-25 16:34:59,341 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall_policy_log from E:\File\repository\xycmdbsync\config\models\network_fire_wall_policy_log.yaml
2025-08-25 16:34:59,467 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for land_balance_device from E:\File\repository\xycmdbsync\config\models\network_land_balance_device.yaml
2025-08-25 16:34:59,517 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool.yaml
2025-08-25 16:34:59,570 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool_men from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool_men.yaml
2025-08-25 16:34:59,644 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_service from E:\File\repository\xycmdbsync\config\models\network_load_balance_service.yaml
2025-08-25 16:34:59,774 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for non_backbone_router from E:\File\repository\xycmdbsync\config\models\network_non_backbone_router.yaml
2025-08-25 16:34:59,845 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for sys_deviceLink from E:\File\repository\xycmdbsync\config\models\network_sys_devicelink.yaml
2025-08-25 16:34:59,929 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_waf_engine from E:\File\repository\xycmdbsync\config\models\secure_cloud_waf_engine.yaml
2025-08-25 16:35:00,006 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_BusinessRegion from E:\File\repository\xycmdbsync\config\models\sys_businessregion.yaml
2025-08-25 16:35:00,037 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DataCenter from E:\File\repository\xycmdbsync\config\models\sys_datacenter.yaml
2025-08-25 16:35:00,088 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DistributedStorage from E:\File\repository\xycmdbsync\config\models\sys_distributedstorage.yaml
2025-08-25 16:35:00,226 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_LUN from E:\File\repository\xycmdbsync\config\models\sys_lun.yaml
2025-08-25 16:35:00,392 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_PhysicalServer from E:\File\repository\xycmdbsync\config\models\sys_physicalserver.yaml
2025-08-25 16:35:00,436 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack from E:\File\repository\xycmdbsync\config\models\sys_rack.yaml
2025-08-25 16:35:00,484 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_IntegratedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_integratedcabinet.yaml
2025-08-25 16:35:00,527 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_ShieldedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_shieldedcabinet.yaml
2025-08-25 16:35:00,572 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_ResourcePool from E:\File\repository\xycmdbsync\config\models\sys_resourcepool.yaml
2025-08-25 16:35:00,596 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Room from E:\File\repository\xycmdbsync\config\models\sys_room.yaml
2025-08-25 16:35:00,642 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageCluster from E:\File\repository\xycmdbsync\config\models\sys_storagecluster.yaml
2025-08-25 16:35:00,693 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageNode from E:\File\repository\xycmdbsync\config\models\sys_storagenode.yaml
2025-08-25 16:35:00,755 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StoragePool from E:\File\repository\xycmdbsync\config\models\sys_storagepool.yaml
2025-08-25 16:35:00,933 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorDevice from E:\File\repository\xycmdbsync\config\models\sys_stordevice.yaml
2025-08-25 16:35:01,163 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_X86Server from E:\File\repository\xycmdbsync\config\models\sys_x86server.yaml
2025-08-25 16:35:01,164 - 3188-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:50] - INFO: ���ü������
2025-08-25 16:35:01,281 - 3188-MainThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:45] - INFO: MySQL���ӳس�ʼ���ɹ�����С: 5
2025-08-25 16:35:01,285 - 3188-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_1
2025-08-25 16:35:01,285 - 3188-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_2
2025-08-25 16:35:01,285 - 3188-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:136] - INFO: Initialized cloud API client for source: source_1
2025-08-25 16:35:01,286 - 3188-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:150] - INFO: Initialized internal API client
2025-08-25 16:35:01,286 - 3188-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:212] - INFO: Loading data from cache
2025-08-25 16:35:01,301 - 3188-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:350] - INFO: Sync record database tables initialized successfully
2025-08-25 16:35:01,311 - 3188-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:350] - INFO: Sync record database tables initialized successfully
2025-08-25 16:35:01,324 - 3188-RestApiThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:47] - INFO: �����߳�: restful_api��Դ�ϱ��ӿ�.
2025-08-25 16:35:01,339 - 3188-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:208] - INFO: ������(����)��ʼ�����: ���=30����, ��ʼ=00:00
2025-08-25 16:35:01,340 - 3188-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:209] - INFO: ִ��ʱ���: ['00:00', '00:30', '01:00', '01:30', '02:00', '02:30', '03:00', '03:30', '04:00', '04:30', '05:00', '05:30', '06:00', '06:30', '07:00', '07:30', '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00', '17:30', '18:00', '18:30', '19:00', '19:30', '20:00', '20:30', '21:00', '21:30', '22:00', '22:30', '23:00', '23:30']
2025-08-25 16:35:01,340 - 3188-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:81] - INFO: �����߳�: async_model��ʱ����ͬ������
2025-08-25 16:35:01,340 - 3188-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:82] - INFO: ��������: ÿ30����ִ��һ�Σ���00:00��ʼ
2025-08-25 16:35:01,341 - 3188-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:83] - INFO: ִ��ʱ���: 00:00, 00:30, 01:00, 01:30, 02:00, 02:30, 03:00, 03:30, 04:00, 04:30, 05:00, 05:30, 06:00, 06:30, 07:00, 07:30, 08:00, 08:30, 09:00, 09:30, 10:00, 10:30, 11:00, 11:30, 12:00, 12:30, 13:00, 13:30, 14:00, 14:30, 15:00, 15:30, 16:00, 16:30, 17:00, 17:30, 18:00, 18:30, 19:00, 19:30, 20:00, 20:30, 21:00, 21:30, 22:00, 22:30, 23:00, 23:30
2025-08-25 16:35:01,341 - 3188-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:84] - INFO: ÿ��ִ�д���: 48 ��
2025-08-25 16:35:01,341 - 3188-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-25 17:00:00, ���뻹�� 1499.00 �룬������...
2025-08-25 17:00:00,342 - 3188-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:161] - INFO: ****************����ִ��ͬ�� (30���Ӽ��)**********************
2025-08-25 17:00:00,342 - 3188-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:161] - WARNING: ���֤Ŀ¼������: E:\File\repository\xycmdbsync\config\license
2025-08-25 17:00:00,342 - 3188-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:164] - INFO: ���Դ����ݿ��ȡ���֤��Ϣ
2025-08-25 17:00:00,345 - 3188-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:73] - INFO: Connection to MySQL DB successful
2025-08-25 17:00:00,347 - 3188-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:88] - INFO: MySQL connection is closed
2025-08-25 17:00:00,347 - 3188-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:170] - INFO: �ɹ������ݿ��ȡ���֤��Ϣ: XYCMDB
2025-08-25 17:00:00,409 - 3188-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:165] - INFO: **************License�ѹ���*******************
2025-08-25 17:00:00,410 - 3188-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-25 17:30:00, ���뻹�� 1800.00 �룬������...
2025-08-25 17:04:49,263 - 19504-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:279] - INFO: ********************************************************************************
2025-08-25 17:04:49,263 - 19504-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:280] - INFO: ��־ϵͳ�ѳ�ʼ������־Ŀ¼: E:\File\repository\xycmdbsync\logs
2025-08-25 17:04:49,264 - 19504-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:281] - INFO: ��־ϵͳ�ѳ�ʼ������־��ת��СΪ: 300M
2025-08-25 17:04:49,264 - 19504-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:282] - INFO: ��־ϵͳ�ѳ�ʼ������־������СΪ50���ļ�
2025-08-25 17:04:49,265 - 19504-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:283] - INFO: ��־ϵͳ�ѳ�ʼ������ǰ��־����Ϊ��20
2025-08-25 17:04:49,265 - 19504-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:284] - INFO: ********************************************************************************
2025-08-25 17:04:49,324 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:44] - INFO: ��ʼ��������...
2025-08-25 17:04:49,378 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:69] - INFO: Loaded base config from E:\File\repository\xycmdbsync\config\base_config.yaml
2025-08-25 17:04:49,381 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:88] - INFO: Found cloud sources: source_1, source_2
2025-08-25 17:04:49,381 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for cloud_side from E:\File\repository\xycmdbsync\config\endpoints\cloud_side.yaml
2025-08-25 17:04:49,391 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for internal_side from E:\File\repository\xycmdbsync\config\endpoints\internal_side.yaml
2025-08-25 17:04:49,563 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:106] - INFO: Loaded transformers from E:\File\repository\xycmdbsync\config\transformers\common.yaml
2025-08-25 17:04:49,626 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_AZONE from E:\File\repository\xycmdbsync\config\models\cloud_azone.yaml
2025-08-25 17:04:49,839 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_BMS from E:\File\repository\xycmdbsync\config\models\cloud_bms.yaml
2025-08-25 17:04:49,947 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cce_cluster.yaml
2025-08-25 17:04:50,029 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CONTAINER from E:\File\repository\xycmdbsync\config\models\cloud_cce_container.yaml
2025-08-25 17:04:50,091 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NAMESPACE from E:\File\repository\xycmdbsync\config\models\cloud_cce_namespace.yaml
2025-08-25 17:04:50,193 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NODE from E:\File\repository\xycmdbsync\config\models\cloud_cce_node.yaml
2025-08-25 17:04:50,277 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_POD from E:\File\repository\xycmdbsync\config\models\cloud_cce_pod.yaml
2025-08-25 17:04:50,364 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_SERVICE from E:\File\repository\xycmdbsync\config\models\cloud_cce_service.yaml
2025-08-25 17:04:50,505 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudCronJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_cronjob.yaml
2025-08-25 17:04:50,640 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDaemonset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_daemonset.yaml
2025-08-25 17:04:50,780 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDeployment from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_deployment.yaml
2025-08-25 17:04:50,919 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_job.yaml
2025-08-25 17:04:51,112 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudStatefulset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_statefulset.yaml
2025-08-25 17:04:51,249 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cluster.yaml
2025-08-25 17:04:51,520 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_HOST from E:\File\repository\xycmdbsync\config\models\cloud_host.yaml
2025-08-25 17:04:51,577 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_OBS_BUCKET from E:\File\repository\xycmdbsync\config\models\cloud_obs_bucket.yaml
2025-08-25 17:04:51,637 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_SFS_SHARE from E:\File\repository\xycmdbsync\config\models\cloud_sfs_share.yaml
2025-08-25 17:04:51,775 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VM_NOVA from E:\File\repository\xycmdbsync\config\models\cloud_vm_nova.yaml
2025-08-25 17:04:51,833 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VOLUME from E:\File\repository\xycmdbsync\config\models\cloud_volume.yaml
2025-08-25 17:04:51,988 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_kafka from E:\File\repository\xycmdbsync\config\models\middleware_kafka.yaml
2025-08-25 17:04:52,051 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_nacos from E:\File\repository\xycmdbsync\config\models\middleware_nacos.yaml
2025-08-25 17:04:52,129 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_redis from E:\File\repository\xycmdbsync\config\models\middleware_redis.yaml
2025-08-25 17:04:52,289 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_rocketmq from E:\File\repository\xycmdbsync\config\models\middleware_rocket_mq.yaml
2025-08-25 17:04:52,418 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_optical_switch from E:\File\repository\xycmdbsync\config\models\network_access_optical_switch.yaml
2025-08-25 17:04:52,558 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_switch from E:\File\repository\xycmdbsync\config\models\network_access_switch.yaml
2025-08-25 17:04:52,679 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for back_bone_router from E:\File\repository\xycmdbsync\config\models\network_backbone_router.yaml
2025-08-25 17:04:52,731 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_bandwidths from E:\File\repository\xycmdbsync\config\models\network_cloud_bandwidths.yaml
2025-08-25 17:04:52,794 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_floating_ips from E:\File\repository\xycmdbsync\config\models\network_cloud_floating_ips.yaml
2025-08-25 17:04:52,858 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_network from E:\File\repository\xycmdbsync\config\models\network_cloud_network.yaml
2025-08-25 17:04:52,931 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_port from E:\File\repository\xycmdbsync\config\models\network_cloud_port.yaml
2025-08-25 17:04:52,992 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_router from E:\File\repository\xycmdbsync\config\models\network_cloud_router.yaml
2025-08-25 17:04:53,051 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_subnet from E:\File\repository\xycmdbsync\config\models\network_cloud_subnet.yaml
2025-08-25 17:04:53,185 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_optical_switch from E:\File\repository\xycmdbsync\config\models\network_core_optical_switch.yaml
2025-08-25 17:04:53,324 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_switch from E:\File\repository\xycmdbsync\config\models\network_core_switch.yaml
2025-08-25 17:04:53,474 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for network_device_port from E:\File\repository\xycmdbsync\config\models\network_device_port.yaml
2025-08-25 17:04:53,608 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall from E:\File\repository\xycmdbsync\config\models\network_fire_wall.yaml
2025-08-25 17:04:53,706 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall_policy_log from E:\File\repository\xycmdbsync\config\models\network_fire_wall_policy_log.yaml
2025-08-25 17:04:53,831 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for land_balance_device from E:\File\repository\xycmdbsync\config\models\network_land_balance_device.yaml
2025-08-25 17:04:53,885 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool.yaml
2025-08-25 17:04:53,941 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool_men from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool_men.yaml
2025-08-25 17:04:54,010 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_service from E:\File\repository\xycmdbsync\config\models\network_load_balance_service.yaml
2025-08-25 17:04:54,145 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for non_backbone_router from E:\File\repository\xycmdbsync\config\models\network_non_backbone_router.yaml
2025-08-25 17:04:54,215 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for sys_deviceLink from E:\File\repository\xycmdbsync\config\models\network_sys_devicelink.yaml
2025-08-25 17:04:54,308 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_waf_engine from E:\File\repository\xycmdbsync\config\models\secure_cloud_waf_engine.yaml
2025-08-25 17:04:54,394 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_BusinessRegion from E:\File\repository\xycmdbsync\config\models\sys_businessregion.yaml
2025-08-25 17:04:54,423 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DataCenter from E:\File\repository\xycmdbsync\config\models\sys_datacenter.yaml
2025-08-25 17:04:54,474 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DistributedStorage from E:\File\repository\xycmdbsync\config\models\sys_distributedstorage.yaml
2025-08-25 17:04:54,609 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_LUN from E:\File\repository\xycmdbsync\config\models\sys_lun.yaml
2025-08-25 17:04:54,778 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_PhysicalServer from E:\File\repository\xycmdbsync\config\models\sys_physicalserver.yaml
2025-08-25 17:04:54,822 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack from E:\File\repository\xycmdbsync\config\models\sys_rack.yaml
2025-08-25 17:04:54,872 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_IntegratedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_integratedcabinet.yaml
2025-08-25 17:04:54,925 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_ShieldedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_shieldedcabinet.yaml
2025-08-25 17:04:54,979 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_ResourcePool from E:\File\repository\xycmdbsync\config\models\sys_resourcepool.yaml
2025-08-25 17:04:55,006 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Room from E:\File\repository\xycmdbsync\config\models\sys_room.yaml
2025-08-25 17:04:55,062 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageCluster from E:\File\repository\xycmdbsync\config\models\sys_storagecluster.yaml
2025-08-25 17:04:55,119 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageNode from E:\File\repository\xycmdbsync\config\models\sys_storagenode.yaml
2025-08-25 17:04:55,188 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StoragePool from E:\File\repository\xycmdbsync\config\models\sys_storagepool.yaml
2025-08-25 17:04:55,366 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorDevice from E:\File\repository\xycmdbsync\config\models\sys_stordevice.yaml
2025-08-25 17:04:55,588 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_X86Server from E:\File\repository\xycmdbsync\config\models\sys_x86server.yaml
2025-08-25 17:04:55,588 - 19504-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:50] - INFO: ���ü������
2025-08-25 17:04:55,712 - 19504-MainThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:45] - INFO: MySQL���ӳس�ʼ���ɹ�����С: 5
2025-08-25 17:04:55,716 - 19504-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_1
2025-08-25 17:04:55,716 - 19504-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_2
2025-08-25 17:04:55,716 - 19504-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:136] - INFO: Initialized cloud API client for source: source_1
2025-08-25 17:04:55,717 - 19504-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:150] - INFO: Initialized internal API client
2025-08-25 17:04:55,717 - 19504-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:212] - INFO: Loading data from cache
2025-08-25 17:04:55,731 - 19504-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:355] - INFO: Sync record database tables initialized successfully
2025-08-25 17:04:55,813 - 19504-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:1035] - INFO: Running migration: 20250825_add_operation_type_column
2025-08-25 17:05:05,105 - 19504-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:1039] - INFO: Migration applied: 20250825_add_operation_type_column
2025-08-25 17:05:05,109 - 19504-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:1035] - INFO: Running migration: 20250825_update_operation_type_data
2025-08-25 17:05:11,863 - 19504-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:1081] - INFO: Updated 103236 rows in sync_changes
2025-08-25 17:05:11,893 - 19504-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:1039] - INFO: Migration applied: 20250825_update_operation_type_data
2025-08-25 17:05:11,928 - 19504-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:355] - INFO: Sync record database tables initialized successfully
2025-08-25 17:05:11,987 - 19504-RestApiThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:47] - INFO: �����߳�: restful_api��Դ�ϱ��ӿ�.
2025-08-25 17:05:12,010 - 19504-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:208] - INFO: ������(����)��ʼ�����: ���=30����, ��ʼ=00:00
2025-08-25 17:05:12,011 - 19504-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:209] - INFO: ִ��ʱ���: ['00:00', '00:30', '01:00', '01:30', '02:00', '02:30', '03:00', '03:30', '04:00', '04:30', '05:00', '05:30', '06:00', '06:30', '07:00', '07:30', '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00', '17:30', '18:00', '18:30', '19:00', '19:30', '20:00', '20:30', '21:00', '21:30', '22:00', '22:30', '23:00', '23:30']
2025-08-25 17:05:12,012 - 19504-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:81] - INFO: �����߳�: async_model��ʱ����ͬ������
2025-08-25 17:05:12,012 - 19504-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:82] - INFO: ��������: ÿ30����ִ��һ�Σ���00:00��ʼ
2025-08-25 17:05:12,012 - 19504-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:83] - INFO: ִ��ʱ���: 00:00, 00:30, 01:00, 01:30, 02:00, 02:30, 03:00, 03:30, 04:00, 04:30, 05:00, 05:30, 06:00, 06:30, 07:00, 07:30, 08:00, 08:30, 09:00, 09:30, 10:00, 10:30, 11:00, 11:30, 12:00, 12:30, 13:00, 13:30, 14:00, 14:30, 15:00, 15:30, 16:00, 16:30, 17:00, 17:30, 18:00, 18:30, 19:00, 19:30, 20:00, 20:30, 21:00, 21:30, 22:00, 22:30, 23:00, 23:30
2025-08-25 17:05:12,013 - 19504-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:84] - INFO: ÿ��ִ�д���: 48 ��
2025-08-25 17:05:12,013 - 19504-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-25 17:30:00, ���뻹�� 1488.00 �룬������...
2025-08-25 17:06:46,790 - 21200-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:279] - INFO: ********************************************************************************
2025-08-25 17:06:46,791 - 21200-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:280] - INFO: ��־ϵͳ�ѳ�ʼ������־Ŀ¼: E:\File\repository\xycmdbsync\logs
2025-08-25 17:06:46,791 - 21200-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:281] - INFO: ��־ϵͳ�ѳ�ʼ������־��ת��СΪ: 300M
2025-08-25 17:06:46,792 - 21200-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:282] - INFO: ��־ϵͳ�ѳ�ʼ������־������СΪ50���ļ�
2025-08-25 17:06:46,792 - 21200-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:283] - INFO: ��־ϵͳ�ѳ�ʼ������ǰ��־����Ϊ��20
2025-08-25 17:06:46,792 - 21200-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:284] - INFO: ********************************************************************************
2025-08-25 17:06:46,851 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:44] - INFO: ��ʼ��������...
2025-08-25 17:06:46,903 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:69] - INFO: Loaded base config from E:\File\repository\xycmdbsync\config\base_config.yaml
2025-08-25 17:06:46,907 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:88] - INFO: Found cloud sources: source_1, source_2
2025-08-25 17:06:46,907 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for cloud_side from E:\File\repository\xycmdbsync\config\endpoints\cloud_side.yaml
2025-08-25 17:06:46,916 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for internal_side from E:\File\repository\xycmdbsync\config\endpoints\internal_side.yaml
2025-08-25 17:06:47,087 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:106] - INFO: Loaded transformers from E:\File\repository\xycmdbsync\config\transformers\common.yaml
2025-08-25 17:06:47,146 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_AZONE from E:\File\repository\xycmdbsync\config\models\cloud_azone.yaml
2025-08-25 17:06:47,319 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_BMS from E:\File\repository\xycmdbsync\config\models\cloud_bms.yaml
2025-08-25 17:06:47,393 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cce_cluster.yaml
2025-08-25 17:06:47,458 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CONTAINER from E:\File\repository\xycmdbsync\config\models\cloud_cce_container.yaml
2025-08-25 17:06:47,506 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NAMESPACE from E:\File\repository\xycmdbsync\config\models\cloud_cce_namespace.yaml
2025-08-25 17:06:47,589 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NODE from E:\File\repository\xycmdbsync\config\models\cloud_cce_node.yaml
2025-08-25 17:06:47,656 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_POD from E:\File\repository\xycmdbsync\config\models\cloud_cce_pod.yaml
2025-08-25 17:06:47,728 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_SERVICE from E:\File\repository\xycmdbsync\config\models\cloud_cce_service.yaml
2025-08-25 17:06:47,869 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudCronJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_cronjob.yaml
2025-08-25 17:06:47,998 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDaemonset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_daemonset.yaml
2025-08-25 17:06:48,133 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDeployment from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_deployment.yaml
2025-08-25 17:06:48,262 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_job.yaml
2025-08-25 17:06:48,397 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudStatefulset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_statefulset.yaml
2025-08-25 17:06:48,468 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cluster.yaml
2025-08-25 17:06:48,706 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_HOST from E:\File\repository\xycmdbsync\config\models\cloud_host.yaml
2025-08-25 17:06:48,757 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_OBS_BUCKET from E:\File\repository\xycmdbsync\config\models\cloud_obs_bucket.yaml
2025-08-25 17:06:48,814 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_SFS_SHARE from E:\File\repository\xycmdbsync\config\models\cloud_sfs_share.yaml
2025-08-25 17:06:48,952 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VM_NOVA from E:\File\repository\xycmdbsync\config\models\cloud_vm_nova.yaml
2025-08-25 17:06:49,011 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VOLUME from E:\File\repository\xycmdbsync\config\models\cloud_volume.yaml
2025-08-25 17:06:49,165 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_kafka from E:\File\repository\xycmdbsync\config\models\middleware_kafka.yaml
2025-08-25 17:06:49,227 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_nacos from E:\File\repository\xycmdbsync\config\models\middleware_nacos.yaml
2025-08-25 17:06:49,304 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_redis from E:\File\repository\xycmdbsync\config\models\middleware_redis.yaml
2025-08-25 17:06:49,462 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_rocketmq from E:\File\repository\xycmdbsync\config\models\middleware_rocket_mq.yaml
2025-08-25 17:06:49,591 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_optical_switch from E:\File\repository\xycmdbsync\config\models\network_access_optical_switch.yaml
2025-08-25 17:06:49,724 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_switch from E:\File\repository\xycmdbsync\config\models\network_access_switch.yaml
2025-08-25 17:06:49,843 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for back_bone_router from E:\File\repository\xycmdbsync\config\models\network_backbone_router.yaml
2025-08-25 17:06:49,894 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_bandwidths from E:\File\repository\xycmdbsync\config\models\network_cloud_bandwidths.yaml
2025-08-25 17:06:49,956 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_floating_ips from E:\File\repository\xycmdbsync\config\models\network_cloud_floating_ips.yaml
2025-08-25 17:06:50,015 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_network from E:\File\repository\xycmdbsync\config\models\network_cloud_network.yaml
2025-08-25 17:06:50,090 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_port from E:\File\repository\xycmdbsync\config\models\network_cloud_port.yaml
2025-08-25 17:06:50,152 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_router from E:\File\repository\xycmdbsync\config\models\network_cloud_router.yaml
2025-08-25 17:06:50,212 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_subnet from E:\File\repository\xycmdbsync\config\models\network_cloud_subnet.yaml
2025-08-25 17:06:50,342 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_optical_switch from E:\File\repository\xycmdbsync\config\models\network_core_optical_switch.yaml
2025-08-25 17:06:50,467 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_switch from E:\File\repository\xycmdbsync\config\models\network_core_switch.yaml
2025-08-25 17:06:50,594 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for network_device_port from E:\File\repository\xycmdbsync\config\models\network_device_port.yaml
2025-08-25 17:06:50,715 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall from E:\File\repository\xycmdbsync\config\models\network_fire_wall.yaml
2025-08-25 17:06:50,811 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall_policy_log from E:\File\repository\xycmdbsync\config\models\network_fire_wall_policy_log.yaml
2025-08-25 17:06:50,933 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for land_balance_device from E:\File\repository\xycmdbsync\config\models\network_land_balance_device.yaml
2025-08-25 17:06:50,983 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool.yaml
2025-08-25 17:06:51,033 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool_men from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool_men.yaml
2025-08-25 17:06:51,099 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_service from E:\File\repository\xycmdbsync\config\models\network_load_balance_service.yaml
2025-08-25 17:06:51,223 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for non_backbone_router from E:\File\repository\xycmdbsync\config\models\network_non_backbone_router.yaml
2025-08-25 17:06:51,288 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for sys_deviceLink from E:\File\repository\xycmdbsync\config\models\network_sys_devicelink.yaml
2025-08-25 17:06:51,370 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_waf_engine from E:\File\repository\xycmdbsync\config\models\secure_cloud_waf_engine.yaml
2025-08-25 17:06:51,445 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_BusinessRegion from E:\File\repository\xycmdbsync\config\models\sys_businessregion.yaml
2025-08-25 17:06:51,475 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DataCenter from E:\File\repository\xycmdbsync\config\models\sys_datacenter.yaml
2025-08-25 17:06:51,527 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DistributedStorage from E:\File\repository\xycmdbsync\config\models\sys_distributedstorage.yaml
2025-08-25 17:06:51,661 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_LUN from E:\File\repository\xycmdbsync\config\models\sys_lun.yaml
2025-08-25 17:06:51,823 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_PhysicalServer from E:\File\repository\xycmdbsync\config\models\sys_physicalserver.yaml
2025-08-25 17:06:51,865 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack from E:\File\repository\xycmdbsync\config\models\sys_rack.yaml
2025-08-25 17:06:51,911 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_IntegratedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_integratedcabinet.yaml
2025-08-25 17:06:51,954 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_ShieldedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_shieldedcabinet.yaml
2025-08-25 17:06:51,999 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_ResourcePool from E:\File\repository\xycmdbsync\config\models\sys_resourcepool.yaml
2025-08-25 17:06:52,023 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Room from E:\File\repository\xycmdbsync\config\models\sys_room.yaml
2025-08-25 17:06:52,069 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageCluster from E:\File\repository\xycmdbsync\config\models\sys_storagecluster.yaml
2025-08-25 17:06:52,121 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageNode from E:\File\repository\xycmdbsync\config\models\sys_storagenode.yaml
2025-08-25 17:06:52,183 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StoragePool from E:\File\repository\xycmdbsync\config\models\sys_storagepool.yaml
2025-08-25 17:06:52,355 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorDevice from E:\File\repository\xycmdbsync\config\models\sys_stordevice.yaml
2025-08-25 17:06:52,582 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_X86Server from E:\File\repository\xycmdbsync\config\models\sys_x86server.yaml
2025-08-25 17:06:52,583 - 21200-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:50] - INFO: ���ü������
2025-08-25 17:06:52,698 - 21200-MainThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:45] - INFO: MySQL���ӳس�ʼ���ɹ�����С: 5
2025-08-25 17:06:52,702 - 21200-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_1
2025-08-25 17:06:52,702 - 21200-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_2
2025-08-25 17:06:52,702 - 21200-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:136] - INFO: Initialized cloud API client for source: source_1
2025-08-25 17:06:52,702 - 21200-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:150] - INFO: Initialized internal API client
2025-08-25 17:06:52,703 - 21200-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:212] - INFO: Loading data from cache
2025-08-25 17:06:52,717 - 21200-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:355] - INFO: Sync record database tables initialized successfully
2025-08-25 17:06:52,735 - 21200-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:355] - INFO: Sync record database tables initialized successfully
2025-08-25 17:06:52,755 - 21200-RestApiThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:47] - INFO: �����߳�: restful_api��Դ�ϱ��ӿ�.
2025-08-25 17:06:52,757 - 21200-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:208] - INFO: ������(����)��ʼ�����: ���=30����, ��ʼ=00:00
2025-08-25 17:06:52,758 - 21200-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:209] - INFO: ִ��ʱ���: ['00:00', '00:30', '01:00', '01:30', '02:00', '02:30', '03:00', '03:30', '04:00', '04:30', '05:00', '05:30', '06:00', '06:30', '07:00', '07:30', '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00', '17:30', '18:00', '18:30', '19:00', '19:30', '20:00', '20:30', '21:00', '21:30', '22:00', '22:30', '23:00', '23:30']
2025-08-25 17:06:52,759 - 21200-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:81] - INFO: �����߳�: async_model��ʱ����ͬ������
2025-08-25 17:06:52,759 - 21200-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:82] - INFO: ��������: ÿ30����ִ��һ�Σ���00:00��ʼ
2025-08-25 17:06:52,759 - 21200-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:83] - INFO: ִ��ʱ���: 00:00, 00:30, 01:00, 01:30, 02:00, 02:30, 03:00, 03:30, 04:00, 04:30, 05:00, 05:30, 06:00, 06:30, 07:00, 07:30, 08:00, 08:30, 09:00, 09:30, 10:00, 10:30, 11:00, 11:30, 12:00, 12:30, 13:00, 13:30, 14:00, 14:30, 15:00, 15:30, 16:00, 16:30, 17:00, 17:30, 18:00, 18:30, 19:00, 19:30, 20:00, 20:30, 21:00, 21:30, 22:00, 22:30, 23:00, 23:30
2025-08-25 17:06:52,760 - 21200-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:84] - INFO: ÿ��ִ�д���: 48 ��
2025-08-25 17:06:52,760 - 21200-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-25 17:30:00, ���뻹�� 1388.00 �룬������...
2025-08-25 17:10:13,987 - 14240-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:279] - INFO: ********************************************************************************
2025-08-25 17:10:13,987 - 14240-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:280] - INFO: ��־ϵͳ�ѳ�ʼ������־Ŀ¼: E:\File\repository\xycmdbsync\logs
2025-08-25 17:10:13,988 - 14240-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:281] - INFO: ��־ϵͳ�ѳ�ʼ������־��ת��СΪ: 300M
2025-08-25 17:10:13,989 - 14240-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:282] - INFO: ��־ϵͳ�ѳ�ʼ������־������СΪ50���ļ�
2025-08-25 17:10:13,989 - 14240-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:283] - INFO: ��־ϵͳ�ѳ�ʼ������ǰ��־����Ϊ��20
2025-08-25 17:10:13,990 - 14240-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:284] - INFO: ********************************************************************************
2025-08-25 17:10:14,063 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:44] - INFO: ��ʼ��������...
2025-08-25 17:10:14,128 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:69] - INFO: Loaded base config from E:\File\repository\xycmdbsync\config\base_config.yaml
2025-08-25 17:10:14,132 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:88] - INFO: Found cloud sources: source_1, source_2
2025-08-25 17:10:14,132 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for cloud_side from E:\File\repository\xycmdbsync\config\endpoints\cloud_side.yaml
2025-08-25 17:10:14,143 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for internal_side from E:\File\repository\xycmdbsync\config\endpoints\internal_side.yaml
2025-08-25 17:10:14,336 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:106] - INFO: Loaded transformers from E:\File\repository\xycmdbsync\config\transformers\common.yaml
2025-08-25 17:10:14,393 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_AZONE from E:\File\repository\xycmdbsync\config\models\cloud_azone.yaml
2025-08-25 17:10:14,559 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_BMS from E:\File\repository\xycmdbsync\config\models\cloud_bms.yaml
2025-08-25 17:10:14,636 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cce_cluster.yaml
2025-08-25 17:10:14,700 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CONTAINER from E:\File\repository\xycmdbsync\config\models\cloud_cce_container.yaml
2025-08-25 17:10:14,749 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NAMESPACE from E:\File\repository\xycmdbsync\config\models\cloud_cce_namespace.yaml
2025-08-25 17:10:14,831 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NODE from E:\File\repository\xycmdbsync\config\models\cloud_cce_node.yaml
2025-08-25 17:10:14,898 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_POD from E:\File\repository\xycmdbsync\config\models\cloud_cce_pod.yaml
2025-08-25 17:10:14,968 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_SERVICE from E:\File\repository\xycmdbsync\config\models\cloud_cce_service.yaml
2025-08-25 17:10:15,101 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudCronJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_cronjob.yaml
2025-08-25 17:10:15,235 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDaemonset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_daemonset.yaml
2025-08-25 17:10:15,380 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDeployment from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_deployment.yaml
2025-08-25 17:10:15,515 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_job.yaml
2025-08-25 17:10:15,654 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudStatefulset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_statefulset.yaml
2025-08-25 17:10:15,728 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cluster.yaml
2025-08-25 17:10:15,978 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_HOST from E:\File\repository\xycmdbsync\config\models\cloud_host.yaml
2025-08-25 17:10:16,032 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_OBS_BUCKET from E:\File\repository\xycmdbsync\config\models\cloud_obs_bucket.yaml
2025-08-25 17:10:16,088 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_SFS_SHARE from E:\File\repository\xycmdbsync\config\models\cloud_sfs_share.yaml
2025-08-25 17:10:16,227 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VM_NOVA from E:\File\repository\xycmdbsync\config\models\cloud_vm_nova.yaml
2025-08-25 17:10:16,286 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VOLUME from E:\File\repository\xycmdbsync\config\models\cloud_volume.yaml
2025-08-25 17:10:16,452 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_kafka from E:\File\repository\xycmdbsync\config\models\middleware_kafka.yaml
2025-08-25 17:10:16,529 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_nacos from E:\File\repository\xycmdbsync\config\models\middleware_nacos.yaml
2025-08-25 17:10:16,631 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_redis from E:\File\repository\xycmdbsync\config\models\middleware_redis.yaml
2025-08-25 17:10:16,821 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_rocketmq from E:\File\repository\xycmdbsync\config\models\middleware_rocket_mq.yaml
2025-08-25 17:10:16,957 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_optical_switch from E:\File\repository\xycmdbsync\config\models\network_access_optical_switch.yaml
2025-08-25 17:10:17,101 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_switch from E:\File\repository\xycmdbsync\config\models\network_access_switch.yaml
2025-08-25 17:10:17,237 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for back_bone_router from E:\File\repository\xycmdbsync\config\models\network_backbone_router.yaml
2025-08-25 17:10:17,297 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_bandwidths from E:\File\repository\xycmdbsync\config\models\network_cloud_bandwidths.yaml
2025-08-25 17:10:17,360 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_floating_ips from E:\File\repository\xycmdbsync\config\models\network_cloud_floating_ips.yaml
2025-08-25 17:10:17,419 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_network from E:\File\repository\xycmdbsync\config\models\network_cloud_network.yaml
2025-08-25 17:10:17,490 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_port from E:\File\repository\xycmdbsync\config\models\network_cloud_port.yaml
2025-08-25 17:10:17,554 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_router from E:\File\repository\xycmdbsync\config\models\network_cloud_router.yaml
2025-08-25 17:10:17,616 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_subnet from E:\File\repository\xycmdbsync\config\models\network_cloud_subnet.yaml
2025-08-25 17:10:17,748 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_optical_switch from E:\File\repository\xycmdbsync\config\models\network_core_optical_switch.yaml
2025-08-25 17:10:17,897 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_switch from E:\File\repository\xycmdbsync\config\models\network_core_switch.yaml
2025-08-25 17:10:18,062 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for network_device_port from E:\File\repository\xycmdbsync\config\models\network_device_port.yaml
2025-08-25 17:10:18,249 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall from E:\File\repository\xycmdbsync\config\models\network_fire_wall.yaml
2025-08-25 17:10:18,382 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall_policy_log from E:\File\repository\xycmdbsync\config\models\network_fire_wall_policy_log.yaml
2025-08-25 17:10:18,517 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for land_balance_device from E:\File\repository\xycmdbsync\config\models\network_land_balance_device.yaml
2025-08-25 17:10:18,565 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool.yaml
2025-08-25 17:10:18,615 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool_men from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool_men.yaml
2025-08-25 17:10:18,679 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_service from E:\File\repository\xycmdbsync\config\models\network_load_balance_service.yaml
2025-08-25 17:10:18,801 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for non_backbone_router from E:\File\repository\xycmdbsync\config\models\network_non_backbone_router.yaml
2025-08-25 17:10:18,866 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for sys_deviceLink from E:\File\repository\xycmdbsync\config\models\network_sys_devicelink.yaml
2025-08-25 17:10:18,949 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_waf_engine from E:\File\repository\xycmdbsync\config\models\secure_cloud_waf_engine.yaml
2025-08-25 17:10:19,030 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_BusinessRegion from E:\File\repository\xycmdbsync\config\models\sys_businessregion.yaml
2025-08-25 17:10:19,059 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DataCenter from E:\File\repository\xycmdbsync\config\models\sys_datacenter.yaml
2025-08-25 17:10:19,111 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DistributedStorage from E:\File\repository\xycmdbsync\config\models\sys_distributedstorage.yaml
2025-08-25 17:10:19,249 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_LUN from E:\File\repository\xycmdbsync\config\models\sys_lun.yaml
2025-08-25 17:10:19,419 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_PhysicalServer from E:\File\repository\xycmdbsync\config\models\sys_physicalserver.yaml
2025-08-25 17:10:19,462 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack from E:\File\repository\xycmdbsync\config\models\sys_rack.yaml
2025-08-25 17:10:19,508 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_IntegratedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_integratedcabinet.yaml
2025-08-25 17:10:19,555 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_ShieldedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_shieldedcabinet.yaml
2025-08-25 17:10:19,603 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_ResourcePool from E:\File\repository\xycmdbsync\config\models\sys_resourcepool.yaml
2025-08-25 17:10:19,628 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Room from E:\File\repository\xycmdbsync\config\models\sys_room.yaml
2025-08-25 17:10:19,677 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageCluster from E:\File\repository\xycmdbsync\config\models\sys_storagecluster.yaml
2025-08-25 17:10:19,728 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageNode from E:\File\repository\xycmdbsync\config\models\sys_storagenode.yaml
2025-08-25 17:10:19,791 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StoragePool from E:\File\repository\xycmdbsync\config\models\sys_storagepool.yaml
2025-08-25 17:10:19,966 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorDevice from E:\File\repository\xycmdbsync\config\models\sys_stordevice.yaml
2025-08-25 17:10:20,192 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_X86Server from E:\File\repository\xycmdbsync\config\models\sys_x86server.yaml
2025-08-25 17:10:20,192 - 14240-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:50] - INFO: ���ü������
2025-08-25 17:10:20,315 - 14240-MainThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:45] - INFO: MySQL���ӳس�ʼ���ɹ�����С: 5
2025-08-25 17:10:20,319 - 14240-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_1
2025-08-25 17:10:20,319 - 14240-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_2
2025-08-25 17:10:20,319 - 14240-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:136] - INFO: Initialized cloud API client for source: source_1
2025-08-25 17:10:20,320 - 14240-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:150] - INFO: Initialized internal API client
2025-08-25 17:10:20,320 - 14240-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:212] - INFO: Loading data from cache
2025-08-25 17:10:20,334 - 14240-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:355] - INFO: Sync record database tables initialized successfully
2025-08-25 17:10:20,343 - 14240-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:355] - INFO: Sync record database tables initialized successfully
2025-08-25 17:10:20,355 - 14240-RestApiThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:47] - INFO: �����߳�: restful_api��Դ�ϱ��ӿ�.
2025-08-25 17:10:20,370 - 14240-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:208] - INFO: ������(����)��ʼ�����: ���=30����, ��ʼ=00:00
2025-08-25 17:10:20,370 - 14240-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:209] - INFO: ִ��ʱ���: ['00:00', '00:30', '01:00', '01:30', '02:00', '02:30', '03:00', '03:30', '04:00', '04:30', '05:00', '05:30', '06:00', '06:30', '07:00', '07:30', '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00', '17:30', '18:00', '18:30', '19:00', '19:30', '20:00', '20:30', '21:00', '21:30', '22:00', '22:30', '23:00', '23:30']
2025-08-25 17:10:20,371 - 14240-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:81] - INFO: �����߳�: async_model��ʱ����ͬ������
2025-08-25 17:10:20,371 - 14240-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:82] - INFO: ��������: ÿ30����ִ��һ�Σ���00:00��ʼ
2025-08-25 17:10:20,372 - 14240-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:83] - INFO: ִ��ʱ���: 00:00, 00:30, 01:00, 01:30, 02:00, 02:30, 03:00, 03:30, 04:00, 04:30, 05:00, 05:30, 06:00, 06:30, 07:00, 07:30, 08:00, 08:30, 09:00, 09:30, 10:00, 10:30, 11:00, 11:30, 12:00, 12:30, 13:00, 13:30, 14:00, 14:30, 15:00, 15:30, 16:00, 16:30, 17:00, 17:30, 18:00, 18:30, 19:00, 19:30, 20:00, 20:30, 21:00, 21:30, 22:00, 22:30, 23:00, 23:30
2025-08-25 17:10:20,372 - 14240-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:84] - INFO: ÿ��ִ�д���: 48 ��
2025-08-25 17:10:20,372 - 14240-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-25 17:30:00, ���뻹�� 1180.00 �룬������...
