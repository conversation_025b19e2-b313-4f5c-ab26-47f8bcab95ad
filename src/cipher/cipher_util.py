import os
import sys

import base64

from Crypto import Random
from Crypto.Cipher import AES
from src.cipher import constants
from src.cipher.file_util import FileUtil
from src.cipher.license_util import LicenseUtil
from src.cipher.root_key_generator import RootKeyGenerator

curPath = os.path.abspath(os.path.dirname(__file__))
rootPath = os.path.split(curPath)[0]
sys.path.append(rootPath)


class CipherUtil(object):
    ARRAY_LENGTH_3 = 3
    ARRAY_LENGTH_2 = 2
    KEY_SIZE_16 = 16
    KEY_SIZE_32 = 32

    @staticmethod
    def pad(data):
        length = CipherUtil.KEY_SIZE_16 - (len(data) % CipherUtil.KEY_SIZE_16)
        return data.encode(encoding='utf-8') + (chr(length) * length).\
            encode(encoding='utf-8')

    @staticmethod
    def un_pad(data):
        return data[:-(data[-1] if type(data[-1]) == int else ord(data[-1]))]

    @staticmethod
    def encrypt(raw_data, key_id='1'):
        dir_path = FileUtil.get_path()
        separator = '\\' if FileUtil.IS_WINDOWS else '/'
        work_key = FileUtil.get_key(dir_path, FileUtil.WORK_FILE_NAME,
                                    separator)
        if not work_key:
            raise Exception('not found work key !')
        decrypt_work_key = CipherUtil.decrypt_work_key(work_key)
        random_iv = Random.new().read(CipherUtil.KEY_SIZE_16)
        cipher = AES.new(decrypt_work_key[:CipherUtil.KEY_SIZE_32],
                         AES.MODE_CBC, random_iv)
        encrypted_data = cipher.encrypt(CipherUtil.pad(raw_data))
        str_iv = ''.join(["%02X" % x for x in random_iv]).strip()
        str_data = ''.join(["%02X" % x for x in encrypted_data]).strip()
        delimiter = constants.Cipher.delimiter
        encrypt_result = key_id + delimiter + str_iv + delimiter + str_data
        return encrypt_result

    @staticmethod
    def decrypt(raw_data):
        delimiter = constants.Cipher.delimiter
        array = raw_data.split(delimiter)
        if len(array) != CipherUtil.ARRAY_LENGTH_3:
            raise Exception('rawData illegal')
        dir_path = FileUtil.get_path()
        separator = '\\' if FileUtil.IS_WINDOWS else '/'
        work_key = FileUtil.get_key(dir_path, FileUtil.WORK_FILE_NAME,
                                    separator)
        if not work_key:
            raise Exception('not found work key!')
        decrypt_work_key = CipherUtil.decrypt_work_key(work_key)
        iv = bytes.fromhex(array[1])
        cipher = AES.new(decrypt_work_key[:CipherUtil.KEY_SIZE_32],
                         AES.MODE_CBC, iv)
        data = bytes.fromhex(array[CipherUtil.ARRAY_LENGTH_2])
        decrypt_result = CipherUtil.un_pad(cipher.decrypt(data))
        return decrypt_result.decode()

    @staticmethod
    def decrypt_work_key(work_key):
        """
        Decrypt the work key
        :param work_key:
        :return: decrypt_work_key
        """
        root_key = RootKeyGenerator.generate_root_key()
        s_byte = base64.b64decode(work_key)
        iv_byte = s_byte[:CipherUtil.KEY_SIZE_16]
        encrypted = s_byte[CipherUtil.KEY_SIZE_16:]
        cipher = AES.new(root_key, AES.MODE_CBC, iv_byte)
        decrypt_work_key = cipher.decrypt(encrypted)
        return decrypt_work_key

    @staticmethod
    def decrypt_license(license_data):
        # license_data_str = LicenseUtil.get_license_str(license_data)
        new_license_str = LicenseUtil.get_decode_data(license_data, 'data')
        return new_license_str
