import os
import sys
import platform
curPath = os.path.abspath(os.path.dirname(__file__))
rootPath = os.path.split(curPath)[0]
sys.path.append(rootPath)


class FileUtil(object):

    IS_WINDOWS = 'Windows' in platform.system()
    SEC_LINUX_DIR = os.path.join(curPath, 'easyview/base')
    SEC_WINDOWS_DIR = os.path.join(curPath, 'easyview/base')
    BASE_FILE_NAME = "baseV3.txt"
    WORK_FILE_NAME = "workV3.txt"
    SALT_FILE_NAME = "saltV3.txt"
    PATH = None

    def __init__(self):
        pass

    @staticmethod
    def get_path():
        path = FileUtil.PATH
        if path is None:
            if FileUtil.IS_WINDOWS:
                return FileUtil.SEC_WINDOWS_DIR
            else:
                return FileUtil.SEC_LINUX_DIR
        FileUtil.PATH = path
        return path

    @staticmethod
    def get_key(dir_path, file_name, separator):
        file_is_exists = os.path.exists(dir_path + separator + file_name)
        if file_is_exists:
            file = open(dir_path + separator + file_name, 'r')
            key = file.read()
            file.close()
            return key
        else:
            raise Exception('file not found!!')
