import base64
import io
import jwt
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.backends import default_backend

# 定义公钥和私钥
PUBLICKEY = (
    "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCkmjM3cI/4NRkXARv2cSEopY40"
    "MwUeqbcHHzmfrF017Q1GdRTOarQ66PrBKgp58pn+Yi8OW7WFm1ifsSm4/G3M+w3t"
    "3HJSC0jCN7O+kWtJXNXqi1OlcmNRbr2azIQD3C0uCtjN+0yNtkkprlf9c6nNrx+R"
    "AFxXGFjkSBpZvc+yTQIDAQAB"
)

PRIVATEKEY = (
    "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAKSaMzdwj/g1GRcB"
    "G/ZxISiljjQzBR6ptwcfOZ+sXTXtDUZ1FM5qtDro+sEqCnnymf5iLw5btYWbWJ+x"
    "Kbj8bcz7De3cclILSMI3s76Ra0lc1eqLU6VyY1FuvZrMhAPcLS4K2M37TI22SSmu"
    "V/1zqc2vH5EAXFcYWORIGlm9z7JNAgMBAAECgYAro5IWjaottO4c8jdWo/GVlCba"
    "Yfhmg7o5kia11X200U8msd29R/7Wa3QniBzU7eOz1JYDKX87l87sL40PNSVquNl6"
    "huTMeLlY3k56iD6qNUtP5xS6dP4VbxMVad7CbzW/2vda233plFah7A6uWSMIyCHsH"
    "2zvbpRumpfYbgLKgQJBANhb/"
    "/kuwSZ0F7wV1HqpnKkf5wMuulxikLN8gVwvZ2PsSaY1bU6pZj6fID8hCVux4hDqlT"
    "3BVBs+DfICFe4O1CECQQDCwp37FW3VnQW4QZCz"
    "8t04lmvuCLHzzDw6WvM0icQthVgwulXkN3azwqNzmLEOtcCE5gz20kA2/vkQQ754"
    "/1itAkEAiOPEk+syB9th/P2u3yEbXqIxroDNJpCyTu2Shzny53TjsEaTbzac2zgg"
    "1fBYFGz6xIs3e8grJ8VeW2VYbLNzIQJAMtHvwU3VLDFeBbduF9aerR5rKWCxdqlr"
    "adrDrYjpXOt3mLd2pCdo3P1EA2zrfqgkhUk2Rs2UHgWoXfsfX5lZvQJAbe9KKlxm"
    "yErktmGlNMI4b7jeCFPkvrLFAEGyA594XGzuZ0j66jc9kK3puoq8yJwIRoC7JIEC"
    "bNmudCWDsV4XPw=="
)


class LicenseUtil(object):

    @staticmethod
    def get_license_str(data):
        try:
            # 确保字符串是bytes类型
            if isinstance(data, str):
                data = data.encode('utf-8')

            # 补充Base64填充符
            padding = 4 - (len(data) % 4)
            if padding != 4:
                data += b'=' * padding

            # 解码Base64数据
            decoded_bytes = base64.b64decode(data)

            # 尝试多种编码解码，按可能性排序
            encodings = ["utf-8", "gbk", "gb2312", "iso-8859-1", "cp1252"]
            license_str = None

            for encoding in encodings:
                try:
                    # 直接尝试完整解码
                    license_str = decoded_bytes.decode(encoding)
                    break
                except UnicodeDecodeError:
                    # 尝试按块解码（针对部分区域编码错误的情况）
                    try:
                        in_stream = io.BytesIO(decoded_bytes)
                        license_str = ""
                        chunk_size = 1024
                        while True:
                            chunk = in_stream.read(chunk_size)
                            if not chunk:
                                break
                            # 对每个块使用当前编码尝试解码
                            license_str += chunk.decode(encoding)
                        in_stream.close()
                        break
                    except UnicodeDecodeError:
                        continue

            if license_str is None:
                # 所有编码都失败，使用替换模式解码（最后手段）
                license_str = decoded_bytes.decode('utf-8', errors='replace')
                print("警告：使用替换模式解码，可能存在乱码")

            return license_str

        except Exception as e:
            print(f"get license str error: {e}")
            return None

    @staticmethod
    def get_public_key(key):
        key_bytes = base64.b64decode(key)
        public_key = serialization.load_der_public_key(
            key_bytes,
            backend=default_backend()
        )
        return public_key

    @staticmethod
    def get_private_key(key):
        key_bytes = base64.b64decode(key)
        private_key = serialization.load_der_private_key(
            key_bytes,
            password=None,
            backend=default_backend()
        )
        return private_key

    @staticmethod
    def get_decode_data(encrypt_data, key):
        try:
            # 获取公钥和私钥
            rsa_public_key = LicenseUtil().get_public_key(PUBLICKEY)
            rsa_private_key = LicenseUtil().get_private_key(PRIVATEKEY)
            # 创建算法对象
            algorithm = 'RS256'
            # 创建JWT验证器
            verifier = jwt.decode(encrypt_data, rsa_public_key,
                                          algorithms=[algorithm],
                                          issuer="ghca")
            # 获取指定键的值
            result = verifier.get(key)
            return result
        except Exception as e:
            print(f"Error: {e}")
            return None
