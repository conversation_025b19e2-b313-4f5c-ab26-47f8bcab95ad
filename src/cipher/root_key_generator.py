import os
import sys


import hashlib
from Crypto import Random
from src.cipher import constants
from src.cipher.file_util import FileUtil

curPath = os.path.abspath(os.path.dirname(__file__))
rootPath = os.path.split(curPath)[0]
sys.path.append(rootPath)


class RootKeyGenerator(object):

    @staticmethod
    def generate_root_key():
        dir_path = FileUtil.get_path()
        separator = '\\' if FileUtil.IS_WINDOWS else '/'
        base_key = FileUtil.get_key(dir_path, FileUtil.BASE_FILE_NAME,
                                    separator)
        if not base_key:
            raise Exception('not found base key!')
        CODE_COMPONENT = [10, 21, 30, 69, 85, 120, 21, 17, 73, 121, 11, 2, 3,
                          69, 85, 100, 21, 33, 76, 12, 17, 63, 25, 53, 69, 8,
                          69, 49, 73, 13, 11, 40]
        hex_byte = bytes.fromhex(base_key)
        if len(hex_byte) != len(CODE_COMPONENT) or not hex_byte:
            raise Exception('ROOT KEY Illegeal')
        result_byte = bytearray()
        for i in range(0, len(hex_byte)):
            result_byte.append(CODE_COMPONENT[i] ^ hex_byte[i])
        if 'PBKDF2' == constants.Cipher.rootKey.get('algorithm'):
            temp_key = ''.join(["%02X" % x for x in result_byte]).strip()
            dir_path = FileUtil.get_path()
            separator = '\\' if FileUtil.IS_WINDOWS else '/'
            salt_key = FileUtil.get_key(dir_path, FileUtil.SALT_FILE_NAME,
                                        separator)
            if not salt_key:
                raise Exception('not found salt key!')
            iteration = constants.Cipher.rootKey.get('iteration')
            if not temp_key or iteration < 10000:
                raise Exception('params illegal')
            salt_byte = bytes.fromhex(salt_key)
            temp_array = bytearray()
            for i in temp_key:
                temp_array.append(ord(i))
            dk = hashlib.pbkdf2_hmac(
                hash_name=constants.Cipher.pbkdf2.get('hash_name'),
                password=temp_array,
                salt=salt_byte,
                iterations=iteration,
                dklen=32)
            return dk
        return result_byte

    @staticmethod
    def get_sha1prng_key(size):
        transform_str = '0123456789abcdef'
        byte_key = Random.new().read(size)
        str_list = []
        for i in byte_key:
            str_list.append(transform_str[(i & 0xf0) >> 4])
            str_list.append(transform_str[i & 0x0f])
        return ''.join(str_list)
