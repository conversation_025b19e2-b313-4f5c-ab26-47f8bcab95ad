import json
import threading
import time
import os
import requests
import sys

from flask import Flask, jsonify, request
from pathlib import Path

# 获取项目根目录（src 的父目录）
project_root = str(Path(__file__).resolve().parent.parent)
sys.path.append(project_root)  # 将根目录添加到系统路径
# 现在可以使用绝对导入
import logging
from src.cipher.cipher_util import CipherUtil
from src.logger_config import setup_logging
from src.config_loader import cmdb_config
from src.sync_manager import SyncManager
from src.record_config import SyncRecordManager
from scheduler import HourlyScheduler
from scheduler import create_scheduler_from_config
from src.license.licensecheck import CheckLicense

# 确保日志配置已设置
# setup_logging()
logger = logging.getLogger(__name__)


# 创建全局变量
app = Flask(__name__)
sync_manager = SyncManager()
shutdown_event = threading.Event()

# 初始化同步记录管理器
record_manager = SyncRecordManager()
curPath = os.path.abspath(os.path.dirname(__file__))
rootPath = os.path.split(curPath)[0]
sys.path.append(rootPath)

baseDir = Path(rootPath)


# API接口
def restful_api():
    try:
        logger.info("启动线程: restful_api资源上报接口.")
        app.config['JSON_AS_ASCII'] = False
        server_port = cmdb_config.get('base', {}).get('server', {}).get('port', 23601)
        app.run(host="0.0.0.0", port=server_port)
    except Exception as E:
        logger.error("restful_api接口启动失败: %s ", repr(E))


# 定时调度同步任务
def async_model():
    """定时调度同步任务

    使用配置化的调度器，支持灵活的时间间隔设置
    配置示例：
    sync:
      interval_minutes: 30  # 30分钟执行一次
      start_time: "00:00"   # 从00:00开始
    或者：
    sync:
      interval_hours: 3     # 3小时执行一次
      start_hour: 0         # 从0点开始
    """

    # 从配置中获取调度间隔
    sync_config = cmdb_config.get('base', {}).get('sync', {})

    # 创建调度器
    scheduler = None
    schedule_info = None

    try:
        scheduler = create_scheduler_from_config(sync_config)
        schedule_info = scheduler.get_schedule_info()

        logger.info(f"启动线程: async_model定时调度同步任务")
        logger.info(f"调度配置: {schedule_info['description']}")
        logger.info(f"执行时间点: {', '.join(schedule_info['execution_times'])}")
        logger.info(f"每天执行次数: {schedule_info['executions_per_day']} 次")
    except Exception as e:
        logger.error(f"调度器初始化失败: {e}")
        logger.info("回退到默认2小时调度逻辑")
        scheduler = None
        schedule_info = None

    if not scheduler:
        logger.info(f"启动线程: async_model定时调度同步任务 (默认2小时间隔)")

    server_port = cmdb_config.get('base', {}).get('server', {}).get('port', 23601)
    primary_ip = cmdb_config.get('base', {}).get('server', {}).get('primary_ip')
    driver_role = cmdb_config.get('base', {}).get('server', {}).get('role')
    primary_url = f'http://{primary_ip}:{server_port}/driver/heartbeat/check'

    while not shutdown_event.is_set():
        if scheduler:
            # 使用调度器计算下一次执行时间
            try:

                result = scheduler.get_next_execution_time()
                next_execution_time, sleep_seconds = result[0], result[1]

                if sleep_seconds > 0:
                    next_time_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(next_execution_time))
                    logger.info(f"下一次同步时间: {next_time_str}, 距离还有 {sleep_seconds:.2f} 秒，休眠中...")
                    shutdown_event.wait(timeout=sleep_seconds)
                    if shutdown_event.is_set():
                        break
            except Exception as e:
                logger.error(f"调度器执行失败: {e}")
                # 回退到简单的1小时等待
                shutdown_event.wait(timeout=3600)
                if shutdown_event.is_set():
                    break
        else:
            # 回退逻辑：每2小时执行一次
            now = time.localtime()
            current_hour = now.tm_hour

            # 找到下一个偶数小时
            if current_hour % 2 == 0:
                next_hour = current_hour + 2
            else:
                next_hour = current_hour + 1

            # 处理跨天
            if next_hour >= 24:
                next_hour = next_hour - 24
                next_execution_time = time.mktime((now.tm_year, now.tm_mon, now.tm_mday + 1, next_hour, 0, 0, 0, 0, -1))
            else:
                next_execution_time = time.mktime((now.tm_year, now.tm_mon, now.tm_mday, next_hour, 0, 0, 0, 0, -1))

            sleep_seconds = next_execution_time - time.time()
            if sleep_seconds > 0:
                logger.info(f"下一次同步时间: {next_hour:02d}:00, 距离还有 {sleep_seconds:.2f} 秒，休眠中...")
                shutdown_event.wait(timeout=sleep_seconds)
                if shutdown_event.is_set():
                    break

        # 检查备份角色
        if driver_role == 'backup':
            try:
                if requests.get(primary_url).status_code == 200:
                    continue
            except Exception as e:
                logger.error(repr(e))

        # 生成间隔描述（使用缓存的 schedule_info）
        if schedule_info:
            if 'interval_minutes' in schedule_info:
                interval_desc = f"{schedule_info['interval_minutes']}分钟间隔"
            else:
                interval_desc = f"{schedule_info['interval_hours']}小时间隔"
        else:
            interval_desc = "2小时间隔(默认)"

        logger.info(f"****************整点执行同步 ({interval_desc})**********************")

        # 引入license检查
        if not CheckLicense.compare_license():
            logger.info("**************License已过期*******************")
            continue

        time.sleep(2)
        model_sync_thread = threading.Thread(
            target=sync_manager.sync_all_models,
            name='cmdb_model_sync_thread',
            daemon=True
        )
        model_sync_thread.start()

    logger.info("async_model线程已停止")


# 信号处理
def signal_handler(signum, frame):
    logger.info(f"收到信号 {signum}，准备关闭应用...")
    shutdown_event.set()  # 通知所有线程退出

    # 给线程一些时间优雅退出
    time.sleep(2)
    sys.exit(0)


# API路由（保持不变）
@app.route("/driver/heartbeat/check", methods=["GET"])
def driver_heartbeat_check():
    data = {"status": "OK"}
    logger.info("心跳检测正常.")
    return jsonify(data)
@app.route("/driver/check/license", methods=["GET"])
def driver_check_license():
    if not CheckLicense.compare_license():
        return jsonify({"success": f"Model  sync Success"}), 500
    data = {"status": "OK"}
    logger.info("license检测正常.")
    return jsonify(data)

@app.route("/driver/sync_all_models", methods=["GET"])
def sync_all_models():
    sync_manager.sync_all_models()
    return jsonify({"success": f"Model  sync Success"}), 200

@app.route("/v1/system/action/connectivity", methods=["POST"])
def driver_connectivity():
    data = {"data": {"status": "success"}}
    logger.info("连通性测试完成。")
    return jsonify(data)

@app.route("/v1/system/cipher/pwd", methods=["POST"])
def cipher_pwd():
    args = request.get_json()
    logger.info(f"<UNK>: {args}")
    if "encrypt" in args:
        rs = CipherUtil.encrypt(args.get("encrypt"))
        logger.info(f'加密后：{rs}')
    if "decrypt" in args:
        rs1 = CipherUtil.decrypt(args.get("decrypt"))
        logger.info(f'解密后：{rs1}')
    data = {"data": {"status": "success"}}
    logger.info("加解密完成。")
    return jsonify(data)

@app.route("/v1/cmdb/sync/<string:class_name>", methods=["GET"])
def sync_model(class_name):
    args = request.args
    try:
        if class_name == "firewall_policy_log":
            sync_manager.sync_cloud_firewall_policy_log()
            return jsonify({"success": f"Model {class_name} sync Success"}), 200
        if class_name == "sys_deviceLink":
            sync_manager.collect_sys_device_link()
            return jsonify({"success": f"Model {class_name} sync Success"}), 200
        if class_name not in cmdb_config['models']:
            return jsonify({"error": f"Model {class_name} not found"}), 404
        model_config = cmdb_config['models'][class_name]
        if model_config.get('enabled', True):
            # 先全量获取云内侧和行内侧数据
            sync_manager.fetch_all_cloud_data(force_refresh=args.get('force', ''), model_id=class_name, source_id=args.get('source',''))
            sync_manager.fetch_all_internal_data(force_refresh=args.get('force', ''), model_id=class_name)
            logger.info(f"开始同步模型: {class_name}")
            # 执行同步并获取同步记录
            sync_record = sync_manager.sync_model(class_name, cmdb_config['models'][class_name])
            if sync_record:
                # 返回成功状态和同步记录ID
                return jsonify({
                    "model": class_name,
                    "record_id": sync_record.id,
                    "source_id": args.get('source', ''),
                    "sync_direction": sync_record.sync_direction,
                    "start_time": sync_record.start_time.isoformat(),
                    "end_time": sync_record.end_time.isoformat() if sync_record.end_time else None,
                    "sync_status": sync_record.status,
                    "statistics": {
                        "total_records": sync_record.total_records,
                        "success_count": sync_record.success_count,
                        "skipped_count": sync_record.skipped_count,
                        "failed_count": sync_record.failed_count,
                        "created_count": sync_record.created_count,
                        "updated_count": sync_record.updated_count,
                        "deleted_count": sync_record.deleted_count,
                        "cloud_to_internal_count": sync_record.cloud_to_internal_count,
                        "internal_to_cloud_count": sync_record.internal_to_cloud_count
                    },
                    "source_statistics": sync_record.source_statistics
                })
            else:
                return jsonify({"status": "skipped", "model": class_name, "reason": "行内或云内缺少数据，未进行同步"})
        else:
            return jsonify({"status": "skipped", "model": class_name, "reason": "Model is disabled"})
    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"同步模型失败: {str(e)}")
        return jsonify({"error": f"Sync failed: {str(e)}"}), 500


# 以下接口为模拟行内的接口
@app.route("/api/authenticate", methods=["POST"])
def internal_authenticate():
    return jsonify({"id_token": "success"})


@app.route("/cmdb/api/rest/updateItem", methods=["POST"])
def update_items():
    args = request.get_json()
    if args is None or "CIs" not in args:
        logger.error("Invalid request: missing 'CIs' in request body")
        return jsonify({"error": "Missing 'CIs' in request body"}), 400

    try:
        cis_data = args.get("CIs")
        if isinstance(cis_data, str):
            # 如果是字符串，尝试解析为JSON
            return json.loads(cis_data)
        elif isinstance(cis_data, (dict, list)):
            # 如果已经是字典或列表，直接返回
            return jsonify(cis_data)
        else:
            logger.error(f"Invalid 'CIs' data type: {type(cis_data)}")
            return jsonify({"error": "Invalid 'CIs' data type"}), 400
    except Exception as e:
        logger.error(f"Error processing updateItem request: {str(e)}")
        return jsonify({"error": str(e)}), 500


@app.route("/cmdb/api/class/getCommonDynamicData", methods=["POST"])
def getCommonDynamicData():
    try:
        args = request.get_json()
        class_name = args.get("className")
        if int(args.get('pageNum')) > 1:
            return []
        logger.info(f"获取动态数据: {class_name}")

        file_path = baseDir / f'./internalApiData/{class_name}.json'
        with open(file_path, 'r', encoding='utf-8') as f:
            data_result = f.read()

        return json.loads(data_result)

    except FileNotFoundError:
        logger.error(f"数据文件不存在: {file_path}")
        return jsonify({"error": "Data not found"}), 404
    except Exception as e:
        logger.error(f"获取动态数据失败: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


# 获取同步记录列表
@app.route("/v1/cmdb/sync_records", methods=["GET"])
def get_sync_records():
    model_id = request.args.get('model_id')
    limit = int(request.args.get('limit', 10))
    offset = int(request.args.get('offset', 0))

    if model_id:
        records = record_manager.get_records_by_model(model_id, limit, offset)
        total = record_manager.get_records_count(model_id)
    else:
        # 获取所有模型的记录
        records = []
        for model_id in cmdb_config['models'].keys():
            model_records = record_manager.get_records_by_model(model_id, limit, offset)
            records.extend(model_records)

        # 按时间排序
        records.sort(key=lambda x: x['start_time'], reverse=True)
        records = records[:limit]
        total = record_manager.get_records_count()

    return jsonify({
        "total": total,
        "records": records
    })

# 手动触发同步
@app.route("/v1/cmdb/sync", methods=["POST"])
def trigger_sync():
    data = request.json
    model_id = data.get('model_id')
    force_refresh = data.get('force_refresh', False)

    try:
        if model_id:
            # 同步单个模型
            model_config = cmdb_config['models'].get(model_id)
            if not model_config:
                return jsonify({"error": f"Model {model_id} not found"}), 404
            # 全量同步前先获取数据
            sync_manager.fetch_all_cloud_data(force_refresh, model_id=model_id)
            sync_manager.fetch_all_internal_data(force_refresh, model_id=model_id)
            sync_record = sync_manager.sync_model(model_id, model_config)

            return jsonify({
                "success": True,
                "message": f"Sync for model {model_id} completed",
                "record_id": sync_record.id
            })
        else:
            # 同步所有模型
            sync_records = sync_manager.initialize_sync(force_refresh)
            return jsonify({
                "success": True,
                "message": "Sync for all models completed",
                "record_ids": [record.id for record in sync_records]
            })
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

# 获取单个同步记录
@app.route("/v1/cmdb/sync_records/<string:record_id>", methods=["GET"])
def get_sync_record(record_id):
    record = record_manager.get_record(record_id)

    if record:
        return jsonify(record)
    else:
        return jsonify({"error": "Record not found"}), 404

# 删除同步记录
@app.route("/v1/cmdb/sync_records/<string:record_id>", methods=["DELETE"])
def delete_sync_record(record_id):
    success = record_manager.delete_record(record_id)

    if success:
        return jsonify({"status": "success"})
    else:
        return jsonify({"error": "Failed to delete record"}), 500


if __name__ == "__main__":
    # 启动线程1 ---> restful_api资源上报接口
    RestApiThread = threading.Thread(target=restful_api, name='RestApiThread')
    RestApiThread.start()

    # 启动线程2 ---> 同步模型上报任务
    PerformanceThread = threading.Thread(
        target=async_model, name='AsyncModelThread')
    PerformanceThread.start()
