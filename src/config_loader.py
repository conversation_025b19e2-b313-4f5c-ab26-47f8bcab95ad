import os
import yaml
import sys
import copy
import json
from pathlib import Path

from typing import Dict, Any, List
import logging
#
# # 导入日志配置
# from .logger_config import setup_logging
# from src.logger_config import logger
logger = logging.getLogger(__name__)


curPath = os.path.abspath(os.path.dirname(__file__))
rootPath = os.path.split(curPath)[0]
sys.path.append(rootPath)

baseDir = Path(rootPath)

# 全局变量，用于存储已加载的配置
_config_instance = None
_config_loaded = False


class ConfigLoader:

    def __init__(self, base_config_path: Path = baseDir / "config/base_config.yaml"):
        # 确保日志配置已设置
        # setup_logging()
        self.logger = logger
        self.base_config_path = base_config_path
        self.base_config = {}
        self.endpoints = {}
        self.transformers = {}
        self.models = {}
    
    def load_config(self) -> Dict[str, Any]:
        """加载所有配置并组合成一个完整的配置对象"""
        global _config_loaded
        if not _config_loaded:
            self.logger.info("开始加载配置...")
            self._load_base_config()
            self._load_endpoints()
            self._load_transformers()
            self._load_models()
            _config_loaded = True
            self.logger.info("配置加载完成")
        else:
            self.logger.info("使用已加载的配置")
        
        # 组合配置
        config = {
            "base": self.base_config,
            "endpoints": self.endpoints,
            "transformers": self.transformers,
            "models": self.models
        }
        
        return config
    
    def _load_base_config(self):
        """加载基础配置文件"""
        try:
            with open(self.base_config_path, 'r', encoding='utf-8') as f:
                self.base_config = yaml.safe_load(f)
            self.logger.info(f"Loaded base config from {self.base_config_path}")
        except Exception as e:
            self.logger.error(f"Failed to load base config: {e}")
            raise
    
    def _load_endpoints(self):
        """加载API端点配置文件"""
        endpoints_dir = baseDir / self.base_config.get('global', {}).get('endpoints_dir', './config/endpoints')
        
        for api_name in ['cloud_side', 'internal_side']:
            endpoint_path = os.path.join(endpoints_dir, f"{api_name}.yaml")
            try:
                with open(endpoint_path, 'r', encoding='utf-8') as f:
                    self.endpoints[api_name] = yaml.safe_load(f).get('endpoints', {})

                # 如果是云内侧端点，记录数据源信息
                if api_name == 'cloud_side':
                    cloud_sources = self.base_config.get('apis', {}).get('cloud_side', {}).keys()
                    if cloud_sources:
                        self.logger.info(f"Found cloud sources: {', '.join(cloud_sources)}")

                self.logger.info(f"Loaded endpoints for {api_name} from {endpoint_path}")
            except Exception as e:
                self.logger.error(f"Failed to load endpoints for {api_name}: {e}")
                self.endpoints[api_name] = {}
    
    def _load_transformers(self):
        """加载通用转换函数配置文件"""
        transformers_dir = baseDir / self.base_config.get('global', {}).get('transformers_dir', './config/transformers')
        
        for file_name in os.listdir(transformers_dir):
            if file_name.endswith('.yaml'):
                transformer_path = os.path.join(transformers_dir, file_name)
                try:
                    with open(transformer_path, 'r', encoding='utf-8') as f:
                        transformers = yaml.safe_load(f).get('transformers', {})
                        self.transformers.update(transformers)
                    self.logger.info(f"Loaded transformers from {transformer_path}")
                except Exception as e:
                    self.logger.error(f"Failed to load transformers from {transformer_path}: {e}")
    
    def _load_models(self):
        """加载所有模型配置文件"""
        models_dir = baseDir / self.base_config.get('global', {}).get('models_dir', './config/models')
        
        for file_name in os.listdir(models_dir):
            if file_name.endswith('.yaml'):
                model_path = os.path.join(models_dir, file_name)
                try:
                    with open(model_path, 'r', encoding='utf-8') as f:
                        model_config = yaml.safe_load(f)
                        model_id = model_config.get('model_id')
                        if model_id:
                            # 添加模型特有的转换函数到通用转换函数中
                            model_transformers = model_config.get('transformers', {})
                            for name, func in model_transformers.items():
                                self.transformers[name] = func
                                # 更新模型配置中的转换函数引用
                                for mapping in model_config.get('field_mappings', []):
                                    if mapping.get('transform') == name:
                                        mapping['transform'] = name

                            # 移除模型配置中的转换函数部分，避免重复
                            model_config.pop('transformers', None)

                            # 处理检查模板
                            if 'check_templates' in model_config:
                                self._process_check_templates(model_config)

                            self.models[model_id] = model_config
                            self.logger.info(f"Loaded model config for {model_id} from {model_path}")
                        else:
                            self.logger.warning(f"Model config in {model_path} has no model_id, skipping")
                except Exception as e:
                    self.logger.error(f"Failed to load model config from {model_path}: {e}")

    def _add_params_to_check(self, check, params):
        """将参数添加到检查对象中

        Args:
            check: 检查对象
            params: 要添加的参数
        """
        for key, value in params.items():
            # 避免覆盖已有的字段
            if key not in check:
                check[key] = value

            # 将参数也添加到每个检查项中
            for check_item in check.get('checks', []):
                if key not in check_item:
                    check_item[key] = value

        return check

    def _process_check_templates(self, model_config):
        """处理模型配置中的检查模板

        Args:
            model_config: 模型配置
        """
        if 'check_templates' not in model_config:
            return

        # 加载检查模板配置
        templates_path = baseDir / self.base_config.get('global', {}).get('templates_dir', './config/check_templates.yaml')

        try:
            with open(templates_path, 'r', encoding='utf-8') as f:
                templates_config = yaml.safe_load(f)
                templates = templates_config.get('templates', {})
        except Exception as e:
            self.logger.error(f"Failed to load check templates: {e}")
            return

        # 首先处理条件组模板
        condition_groups = {}
        processed_templates = []

        for template_usage in model_config['check_templates']:
            template_type = template_usage.get('template')

            # 处理条件组模板
            if template_type == 'condition_group':
                group_id = template_usage.get('id')
                if group_id:
                    condition_groups[group_id] = template_usage.get('params', {})
                    # 保留条件组定义，不进行处理
                    processed_templates.append(template_usage)
                continue

            # 处理其他模板
            if template_type not in templates:
                self.logger.warning(f"Template not found: {template_type} for model {model_config.get('model_id')}")
                # 保留未知模板，不进行处理
                processed_templates.append(template_usage)
                continue

            # 获取模板
            template_config = templates[template_type]
            template = template_config['template']

            # 获取参数
            params = template_usage.get('params', {})

            # 处理条件引用
            condition_ref = params.get('condition_ref')
            if condition_ref and condition_ref in condition_groups:
                # 将条件组参数合并到当前参数中
                condition_params = condition_groups[condition_ref]
                for key, value in condition_params.items():
                    if key not in params:
                        params[key] = value

            # 获取作用域，优先使用模型配置中指定的作用域，如果没有则使用默认值"field"
            scope = template_usage.get('scope', 'field')

            # 深拷贝模板
            check = copy.deepcopy(template)

            # 添加作用域
            check['scope'] = scope

            # 先将参数添加到检查对象中
            check = self._add_params_to_check(check, params)

            # 将模板转换为字符串
            check_str = json.dumps(check)

            # 替换参数
            try:
                # 处理字符串中的占位符替换
                for key, value in params.items():
                    placeholder = f'"{{{key}}}"'
                    if isinstance(value, list):
                        # 列表需要特殊处理，确保JSON格式正确
                        value_json = json.dumps(value)
                        # 移除外层引号，因为我们要替换的是已经在JSON字符串中的值
                        check_str = check_str.replace(placeholder, value_json)
                    elif isinstance(value, (int, float, bool)):
                        # 数字和布尔值不需要引号
                        check_str = check_str.replace(placeholder, str(value).lower() if isinstance(value, bool) else str(value))
                    else:
                        # 字符串和其他类型，确保正确转义
                        value_str = json.dumps(str(value))[1:-1]  # 移除外层引号
                        check_str = check_str.replace(placeholder, f'"{value_str}"')

                # 将字符串转换回对象
                try:
                    check = json.loads(check_str)
                except json.JSONDecodeError as e:
                    self.logger.error(f"Failed to parse template JSON after parameter replacement: {e}")
                    self.logger.debug(f"Problematic JSON string: {check_str}")
            except Exception as e:
                self.logger.error(f"Error during template parameter replacement: {e}")

            # 添加到处理后的检查列表
            processed_templates.append(check)

        # 更新模型配置中的检查模板
        model_config['check_templates'] = processed_templates

        # 将处理后的条件组添加到filter_checks中
        if 'filter_checks' not in model_config:
            model_config['filter_checks'] = []

        # 将处理后的检查添加到filter_checks
        for check in processed_templates:
            if check.get('template') != 'condition_group':  # 排除条件组模板
                model_config['filter_checks'].append(check)

        # 添加条件组到filter_checks
        for group_id, group_params in condition_groups.items():
            condition_check = {
                'type': 'condition_group',
                'id': group_id,
                'params': group_params
            }
            model_config['filter_checks'].append(condition_check)

        # 删除模板使用指令
        del model_config['check_templates']

        self.logger.debug(
            f"Processed {len(processed_templates)} check templates for model {model_config.get('model_id')}")

def get_config(config_path=None):
    """获取配置单例"""
    global _config_instance
    if _config_instance is None:
        if config_path is None:
            config_path = baseDir / "config/base_config.yaml"
        loader = ConfigLoader(config_path)
        _config_instance = loader.load_config()
    return _config_instance

# 创建配置加载器实例并加载配置
config_path = baseDir / "config/base_config.yaml"
cmdb_config = get_config(config_path)