from mysql.connector.pooling import MySQLConnectionPool
import mysql.connector
from mysql.connector import Error
# from src.logger_config import logger
from src.config_loader import cmdb_config
from src.cipher.cipher_util import CipherUtil
import logging
logger = logging.getLogger(__name__)
# # 确保日志配置已设置
# setup_logging()

base_config = cmdb_config.get('base')
host_name = base_config.get('database').get('host_name')
user_name = base_config.get('database').get('user_name')
user_password = CipherUtil.decrypt(base_config.get('database').get('password'))
db_name = base_config.get('database').get('db_name')
port = base_config.get('database').get('port')


class DatabaseUtils:

    def __init__(self):
        self.pool = None
        self._init_connection_pool()

    def _init_connection_pool(self, pool_size=5):
        """初始化数据库连接池

        Args:
            pool_size: 连接池大小
        """
        try:
            dbconfig = {
                "host": host_name,
                "user": user_name,
                "password": user_password,
                "database": db_name,
                "port": port
            }
            self.pool = MySQLConnectionPool(
                pool_name="cmdb_pool",
                pool_size=pool_size,
                **dbconfig
            )
            logger.info(f"MySQL连接池初始化成功，大小: {pool_size}")
        except Error as e:
            logger.error(f"初始化连接池时发生错误: '{e}'")
            # 如果连接池初始化失败，仍然可以使用普通连接
            self.pool = None

    def get_connection(self):
        """从连接池获取连接，如果连接池不可用则创建新连接"""
        if self.pool:
            try:
                return self.pool.get_connection()
            except Error as e:
                logger.error(f"从连接池获取连接时发生错误: '{e}'")
                # 如果从连接池获取连接失败，尝试创建新连接

        # 使用普通方式创建连接
        return self.create_connection()

    def create_connection(self):
        """创建数据库连接"""
        try:
            connection = mysql.connector.connect(
                host=host_name,
                user=user_name,
                passwd=user_password,
                database=db_name,
                port=port
            )
            logger.info("Connection to MySQL DB successful")
            return connection
        except Error as e:
            logger.error(f"The error '{e}' occurred")
            return None


    def query_license_info(self, license_type):
        # 创建数据库连接
        connection = self.create_connection()
        license_query = f"SELECT id, type, data FROM t_license where type = '{license_type}' "
        result = self.execute_query(connection, license_query)
               # 关闭连接
        if connection.is_connected():
            connection.close()
            logger.info("MySQL connection is closed")

        return result

    def execute_query(self, connection, query):
        cursor = connection.cursor()
        try:
            cursor.execute(query)
            return cursor.fetchall()
        except Error as e:
            logger.error(f"The error '{e}' occurred")
        finally:
            cursor.close()


    def execute_update(self, connection, query, params=None):
        """执行更新操作（UPDATE, INSERT, DELETE）

        Args:
            connection: 数据库连接
            query: SQL查询语句
            params: 查询参数（可选）

        Returns:
            int: 受影响的行数
            None: 如果发生错误
        """
        cursor = connection.cursor()
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            # 提交事务
            connection.commit()

            # 返回受影响的行数
            return cursor.rowcount
        except Error as e:
            logger.error(f"执行更新操作时发生错误: '{e}'")
            # 回滚事务
            connection.rollback()
            return None
        finally:
            cursor.close()

    def update_record(self, table_name, data, condition):
        """更新表中的记录

        Args:
            table_name: 表名
            data: 包含要更新的字段名和值的字典
            condition: WHERE条件（字符串）

        Returns:
            int: 受影响的行数
            bool: 操作是否成功
        """
        connection = self.create_connection()
        if not connection:
            return False

        try:
            # 构建UPDATE语句
            set_clause = ', '.join([f"{field} = %s" for field in data.keys()])
            query = f"UPDATE {table_name} SET {set_clause} WHERE {condition}"

            cursor = connection.cursor()
            cursor.execute(query, list(data.values()))
            connection.commit()

            affected_rows = cursor.rowcount
            logger.info(f"成功更新表 {table_name} 中的 {affected_rows} 条记录")
            return affected_rows
        except Error as e:
            logger.error(f"更新记录时发生错误: '{e}'")
            connection.rollback()
            return False
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()

    def delete_record(self, table_name, condition):
        """从表中删除记录

        Args:
            table_name: 表名
            condition: WHERE条件（字符串）

        Returns:
            int: 受影响的行数
            bool: 操作是否成功
        """
        connection = self.create_connection()
        if not connection:
            return False

        try:
            # 构建DELETE语句
            query = f"DELETE FROM {table_name} WHERE {condition}"

            cursor = connection.cursor()
            cursor.execute(query)
            connection.commit()

            affected_rows = cursor.rowcount
            logger.info(f"成功从表 {table_name} 中删除 {affected_rows} 条记录")
            return affected_rows
        except Error as e:
            logger.error(f"删除记录时发生错误: '{e}'")
            connection.rollback()
            return False
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()

    def execute_alter_table(self, query):
        """执行表结构修改操作

        Args:
            query: ALTER TABLE SQL语句

        Returns:
            bool: 操作是否成功
        """
        connection = self.create_connection()
        if not connection:
            return False

        try:
            cursor = connection.cursor()
            cursor.execute(query)
            connection.commit()

            logger.info(f"成功执行表结构修改: {query[:50]}...")
            return True
        except Error as e:
            logger.error(f"表结构修改时发生错误: '{e}'")
            connection.rollback()
            return False
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()

    def execute_batch_insert(self, table_name, fields, values_list):
        """批量插入数据

        Args:
            table_name: 表名
            fields: 字段名列表
            values_list: 值列表的列表

        Returns:
            int: 插入的记录数
            bool: 操作是否成功
        """
        connection = self.create_connection()
        if not connection:
            return False

        try:
            # 构建INSERT语句
            fields_str = ', '.join(fields)
            placeholders = ', '.join(['%s'] * len(fields))
            query = f"INSERT INTO {table_name} ({fields_str}) VALUES ({placeholders})"

            cursor = connection.cursor()
            cursor.executemany(query, values_list)
            connection.commit()

            affected_rows = cursor.rowcount
            logger.info(f"成功向表 {table_name} 批量插入 {affected_rows} 条记录")
            return affected_rows
        except Error as e:
            logger.error(f"批量插入记录时发生错误: '{e}'")
            connection.rollback()
            return False
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()

    def begin_transaction(self):
        """开始一个事务，返回数据库连接

        Returns:
            connection: 数据库连接对象
        """
        connection = self.create_connection()
        if connection:
            connection.autocommit = False
            logger.info("开始数据库事务")
        return connection

    def commit_transaction(self, connection):
        """提交事务

        Args:
            connection: 数据库连接

        Returns:
            bool: 操作是否成功
        """
        try:
            if connection and connection.is_connected():
                connection.commit()
                logger.info("事务已提交")
                return True
            return False
        except Error as e:
            logger.error(f"提交事务时发生错误: '{e}'")
            return False

    def rollback_transaction(self, connection):
        """回滚事务

        Args:
            connection: 数据库连接

        Returns:
            bool: 操作是否成功
        """
        try:
            if connection and connection.is_connected():
                connection.rollback()
                logger.info("事务已回滚")
                return True
            return False
        except Error as e:
            logger.error(f"回滚事务时发生错误: '{e}'")
            return False

