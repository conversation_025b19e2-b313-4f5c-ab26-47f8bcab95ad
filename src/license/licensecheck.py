import json
import socket
from datetime import datetime


import psutil
import os
import sys
from pathlib import Path
import uuid
from click.types import UUIDParameterType

from src.cipher.cipher_util import CipherUtil
from src.database.database_utils import DatabaseUtils
from src.config_loader import cmdb_config
# from src.logger_config import logger
import logging

# # 确保日志配置已设置
# setup_logging()
logger = logging.getLogger(__name__)

darabase_manage = DatabaseUtils()

# 获取当前文件所在目录的绝对路径
curPath = os.path.abspath(os.path.dirname(__file__))
# 向上取两级目录（当前是取一级，再取一次就是两级）
rootPath = os.path.split(os.path.split(curPath)[0])[0]
sys.path.append(rootPath)
baseDir = Path(rootPath)

LICENSE_TYPE = cmdb_config.get('base').get('global').get('license_type', 'XYCMDB')

class License:

    def __init__(self, id, type, data):
        self.id = id
        self.type = type
        self.data = data

    def __repr__(self):
        return f"License(id={self.id}, type='{self.type}', data='{self.data}')"


class CheckLicense:

    def get_license_from_db(self):
        result = darabase_manage.query_license_info(LICENSE_TYPE)
        return [License(*license) for license in result]

    def get_network_interfaces_mac_addresses(self):
        mac_addresses = []
        net_if_addrs = psutil.net_if_addrs()
        for interface, addrs in net_if_addrs.items():
            for addr in addrs:
                if addr.family == psutil.AF_LINK:  # AF_LINK 表示物理地址
                    mac_addresses.append(addr.address.lower().replace("-",":"))
        return mac_addresses

    def compare_local_time(self, comparison_time_str):
        comparison_time_str = comparison_time_str
        current_time = datetime.now()
        comparison_time = datetime.strptime(comparison_time_str,
                                            "%Y-%m-%d %H:%M:%S")
        return current_time <= comparison_time

    def get_internal_ip(self):
        ip_list = []
        for interface, addrs in psutil.net_if_addrs().items():
            for addr in addrs:
                if addr.family == socket.AF_INET:
                    ip_list.append(addr.address)
        return ip_list

    def read_file_to_string(self):
        """读取许可证文件内容并处理数据库事务（包含回滚）

        首先检查许可证目录中是否有文件，如果有则读取文件内容，
        先执行数据库事务（删除旧数据、插入新数据），成功后再删除文件，
        事务失败则回滚并保留文件。如果文件不存在，则尝试从数据库读取。

        Returns:
            str: 文件内容或数据库中的许可证信息
            False: 如果文件和数据库中都没有许可证信息
        """
        # 检查文件是否存在
        license_file_dir = baseDir / cmdb_config.get('base', {}).get('global', {}).get('license_dir',
                                                                                       './config/license')

        # 确保目录存在
        if os.path.exists(license_file_dir):
            # 获取目录中的文件列表
            files = os.listdir(license_file_dir)
            if files:
                # 读取第一个文件
                file_name = files[0]
                license_file_path = os.path.join(license_file_dir, file_name)
                try:
                    # 检查文件是否存在
                    if os.path.isfile(license_file_path):
                        # 先读取文件内容（此时不删除文件）
                        with open(license_file_path, 'r', encoding='utf-8') as file:
                            content = file.read()

                        logger.info(f"成功读取许可证文件: {license_file_path}")

                        # 数据库操作：使用事务确保原子性
                        conn = None
                        try:
                            # 创建数据库连接
                            conn = darabase_manage.create_connection()
                            # 关闭自动提交，开启事务
                            conn.autocommit = False

                            # 1. 只删除指定类型的许可证信息（添加WHERE条件）
                            darabase_manage.execute_update(
                                conn,
                                "DELETE FROM t_license WHERE type = %s",
                                (LICENSE_TYPE,)  # 作为参数传入，避免SQL注入
                            )

                            # 2. 准备插入新数据
                            insert_id = str(uuid.uuid4()).replace('-', '')
                            now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                            # 3. 插入新的许可证信息
                            darabase_manage.execute_update(
                                conn,
                                "INSERT INTO t_license (id, name, data, upload_time, activation_time, type) VALUES (%s, %s, %s, %s, %s, %s)",
                                (insert_id, LICENSE_TYPE, content, now, now, LICENSE_TYPE)
                            )

                            # 所有操作成功，提交事务
                            conn.commit()
                            logger.info(f"成功更新数据库中的许可证信息: {LICENSE_TYPE}")

                            # 数据库操作成功后，再删除文件
                            os.remove(license_file_path)
                            logger.info(f"成功删除许可证文件: {license_file_path}")

                            return content

                        except Exception as db_error:
                            # 发生错误，回滚事务
                            if conn:
                                conn.rollback()
                                logger.info("数据库操作失败，已执行回滚")
                            logger.error(f"更新数据库中的许可证信息时出错: {db_error}")
                            # 事务失败，不删除文件，以便后续重试
                            return None
                        finally:
                            # 确保连接关闭
                            if conn:
                                conn.close()

                    else:
                        logger.warning(f"许可证文件不是一个常规文件: {license_file_path}")
                except Exception as e:
                    logger.error(f"读取许可证文件时出错: {license_file_path}, 错误: {e}")
        else:
            logger.warning(f"许可证目录不存在: {license_file_dir}")

        # 如果文件不存在或读取失败，尝试从数据库读取
        logger.info("尝试从数据库读取许可证信息")
        try:
            db_license_info = darabase_manage.query_license_info(LICENSE_TYPE)
            if db_license_info and len(db_license_info) > 0:
                # 返回数据库中的第一条许可证信息
                license_data = db_license_info[0][2]  # 假设第三列是data
                logger.info(f"成功从数据库读取许可证信息: {LICENSE_TYPE}")
                return license_data
            else:
                logger.warning(f"数据库中不存在许可证信息: {LICENSE_TYPE}")
                return None
        except Exception as db_error:
            logger.error(f"从数据库读取许可证信息时出错: {db_error}")
            return None

    @staticmethod
    def compare_license():
        check_license = CheckLicense()
        # 读取许可证文件内容
        license_file_info = check_license.read_file_to_string()
        if not license_file_info:
            return False
        # 获取部署服务器的MAC地址
        local_mac_addresses = check_license.get_network_interfaces_mac_addresses()
        # 获取部署服务器的IP地址
        local_ips = check_license.get_internal_ip()
        # 用于存储比较的IP和MAC地址
        compare_ips = set()
        compare_macs = set()
        expiredate_list = []
        # payload_base64 = license_file_info.split('.')[1]  # 提取中间部分
        decrypted_data = json.loads(CipherUtil.decrypt_license(license_file_info))
        max_expiredate_item = decrypted_data.get('license', {}).get('bbxx', {}).get('jsrq')
        # 找到expiredate最大的一个
        # max_expiredate_item = max(spfunction_list, key=lambda x: datetime.strptime(x['expiredate'], "%Y-%m-%d"))
        # expiredate_list.append({'expiredate': expiredate})
        # 提取IP和MAC地址
        ip_string = decrypted_data.get('license', {}).get('xkxx', {}).get('qdkz', {}).get('ips', '')
        macs_string = decrypted_data.get('license', {}).get('xkxx', {}).get('qdkz', {}).get('macs', '')
        compare_ips.update(ip_string.strip(';').split(';'))
        compare_macs.update(macs_string.lower().strip(';').split(';'))
        # 找到最大过期时间
        # max_expiredate_item = max(expiredate_list, key=lambda x: datetime.strptime(x['expiredate'], "%Y-%m-%d"))
        # 比较当前时间与最大过期时间
        if not check_license.compare_local_time(max_expiredate_item):
            return False
        # 比对IP和MAC地址
        valid_ips = set(compare_ips).intersection(local_ips)
        valid_macs = set(compare_macs).intersection(local_mac_addresses)
        if valid_ips and valid_macs:
            return True
        return False
