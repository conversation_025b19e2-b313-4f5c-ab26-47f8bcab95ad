import logging
import logging.handlers
import os
import time
import datetime
import zipfile
import glob
import re
import threading
import shutil
import urllib3
import sys
from pathlib import Path
import atexit

# 禁用第三方库的日志
logging.getLogger('werkzeug').setLevel("WARN")
logging.getLogger('urllib3').setLevel("WARN")
urllib3.disable_warnings()

curPath = os.path.abspath(os.path.dirname(__file__))
rootPath = os.path.split(curPath)[0]
sys.path.append(rootPath)
baseDir = Path(rootPath)
LOG_PATH = baseDir / "logs"
os.makedirs(LOG_PATH, exist_ok=True)


class MidnightRotatingFileHandler(logging.handlers.RotatingFileHandler):
    """自定义日志处理器：按日期轮转 + 按大小轮转 + 凌晨强制轮转"""

    def __init__(self, filename, mode='a', maxBytes=0, backupCount=0, encoding=None, delay=False):
        # 设置基本文件名和轮转参数
        self.base_filename = filename
        self.current_date = self.get_current_date()
        self.maxBytes = maxBytes
        self.backupCount = backupCount
        self.mode = mode
        self.delay = delay

        # 创建当前日期的日志文件
        filename = self.get_filename_with_date()
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        super().__init__(filename, mode, maxBytes, backupCount, encoding, delay)

        # 设置午夜轮转定时器
        self.set_midnight_timer()
        # 注册退出时清理
        atexit.register(self.cleanup)

    def get_current_date(self):
        """获取当前日期字符串"""
        return datetime.datetime.now().strftime("%Y-%m-%d")

    def get_filename_with_date(self):
        """生成带日期的文件名"""
        return f"{self.base_filename}.{self.current_date}.log"

    def set_midnight_timer(self):
        """设置午夜轮转定时器"""
        now = datetime.datetime.now()
        midnight = now.replace(hour=0, minute=0, second=0, microsecond=0) + datetime.timedelta(days=1)
        seconds_until_midnight = (midnight - now).total_seconds()

        # 使用线程定时器触发轮转
        self.timer = threading.Timer(seconds_until_midnight, self.force_rollover)
        self.timer.daemon = True  # 设置为守护线程
        self.timer.start()

    def force_rollover(self):
        """强制轮转日志文件，即使没有新日志写入"""
        print(f"[{datetime.datetime.now()}] 执行强制日志轮转")

        # 检查日期是否变化
        new_date = self.get_current_date()
        date_changed = new_date != self.current_date

        # 关闭当前文件流
        if self.stream:
            self.stream.close()
            self.stream = None

        # 如果日期变化，执行完整的轮转
        if date_changed:
            # 保存旧日期用于压缩
            old_date = self.current_date

            # 更新日期
            self.current_date = new_date
            self.baseFilename = self.get_filename_with_date()

            # 重新打开新日期的日志文件
            if not self.delay:
                self.stream = self._open()

            # 压缩旧日期的日志
            self.compress_logs_by_date(old_date)

        # 为下一天设置定时器
        self.set_midnight_timer()

    def compress_logs_by_date(self, date_to_compress):
        """压缩指定日期的日志文件"""
        print(f"[{datetime.datetime.now()}] 压缩 {date_to_compress} 的日志文件")
        print(f"日志文件名称:{self.base_filename}")
        print(f"日志文件名称:{date_to_compress}")
        # 只处理当前类型的日志文件
        pattern = re.compile(rf"^{re.escape(date_to_compress)}\.log(\.\d+)?$")

        # 查找所有匹配的文件
        files_to_compress = []
        print(glob.glob(f"{self.base_filename}.*.log*"))
        for fname in glob.glob(f"{self.base_filename}.*.log*"):
            print(f"fname:{fname}")
            if date_to_compress in fname:
            # if pattern.match(os.path.basename(fname)):
                files_to_compress.append(fname)

        # 创建压缩文件（如果存在文件且压缩文件不存在）
        if files_to_compress:
            zip_filename = f"{self.base_filename}.{date_to_compress}.zip"
            if not os.path.exists(zip_filename):
                try:
                    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
                        for f in files_to_compress:
                            if os.path.exists(f):
                                # 添加文件到zip，保留基本文件名
                                zipf.write(f, os.path.basename(f))
                    print(f"[{datetime.datetime.now()}] 创建压缩文件: {zip_filename}")
                except Exception as e:
                    print(f"[{datetime.datetime.now()}] 压缩文件失败: {str(e)}")

            # 删除原始文件（确保压缩文件存在）
            if os.path.exists(zip_filename):
                for f in files_to_compress:
                    if os.path.exists(f):
                        try:
                            os.remove(f)
                            print(f"[{datetime.datetime.now()}] 删除源文件: {f}")
                        except Exception as e:
                            print(f"[{datetime.datetime.now()}] 删除文件失败: {str(e)}")
        else:
            print(f"[{datetime.datetime.now()}] 没有找到 {date_to_compress} 的日志文件可压缩")

    def shouldRollover(self, record):
        """检查是否需要轮转（日期变化或文件过大）"""
        current_date = self.get_current_date()
        if current_date != self.current_date:
            return True
        if self.stream is None:
            return False
        if self.maxBytes > 0:
            msg = "%s\n" % self.format(record)
            self.stream.seek(0, 2)
            if self.stream.tell() + len(msg) >= self.maxBytes:
                return True
        return False

    def doRollover(self):
        """执行日志轮转"""
        current_date = self.get_current_date()
        old_date = self.current_date

        # 处理日期变化的情况
        if current_date != old_date:
            # 关闭当前文件流
            if self.stream:
                self.stream.close()
                self.stream = None

            # 更新日期
            self.current_date = current_date
            self.baseFilename = self.get_filename_with_date()

            # 重新打开文件
            if not self.delay:
                self.stream = self._open()

            # 压缩旧日期的日志
            self.compress_logs_by_date(old_date)
            return

        # 处理文件过大的情况（日期未变化）
        if self.stream:
            self.stream.close()
            self.stream = None

        # 执行大小轮转
        if self.backupCount > 0:
            # 先重命名现有备份文件
            for i in range(self.backupCount - 1, 0, -1):
                sfn = f"{self.baseFilename}.{i}"
                dfn = f"{self.baseFilename}.{i + 1}"
                if os.path.exists(sfn):
                    if os.path.exists(dfn):
                        os.remove(dfn)
                    os.rename(sfn, dfn)

            # 重命名当前文件
            dfn = f"{self.baseFilename}.1"
            if os.path.exists(dfn):
                os.remove(dfn)
            if os.path.exists(self.baseFilename):
                os.rename(self.baseFilename, dfn)

        # 重新打开日志文件
        if not self.delay:
            self.stream = self._open()

    def cleanup(self):
        """清理资源，取消定时器"""
        if hasattr(self, 'timer') and self.timer:
            self.timer.cancel()


def setup_logging(log_path="logs",
                  max_bytes=10 * 1024 * 1024,  # 默认10MB
                  backup_count=50,  # 默认保留5个备份
                  log_level=logging.INFO):
    """配置日志系统

    :param log_path: 日志存储目录
    :param max_bytes: 单个日志文件最大字节数
    :param backup_count: 保留的备份文件数量
    :param log_level: 日志级别
    """

    # 创建日志目录
    os.makedirs(log_path, exist_ok=True)

    # 基础日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(process)d-%(threadName)s - '
        '%(pathname)s[line:%(lineno)d] - %(levelname)s: %(message)s')

    # 创建INFO日志处理器
    info_handler = MidnightRotatingFileHandler(
        filename=os.path.join(log_path, "info"),
        maxBytes=max_bytes,
        backupCount=backup_count
    )
    info_handler.setLevel(logging.DEBUG if log_level == logging.DEBUG else logging.INFO)
    info_handler.setFormatter(formatter)

    # 创建ERROR日志处理器
    error_handler = MidnightRotatingFileHandler(
        filename=os.path.join(log_path, "error"),
        maxBytes=max_bytes,
        backupCount=backup_count
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(formatter)

    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)

    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    root_logger.addHandler(info_handler)
    root_logger.addHandler(error_handler)

    # 添加控制台输出（可选）
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(log_level)
    root_logger.addHandler(console_handler)

setup_logging(
    log_path=str(LOG_PATH),  # 日志保存目录
    max_bytes=300 * 1024 * 1024,  # 1MB文件大小限制（测试用）
    backup_count=50,  # 保留50个备份文件
    log_level=logging.INFO  # 设置日志级别为DEBUG
)
# 获取日志记录器
logger = logging.getLogger(__name__)
logger.info("*" * 80)
logger.info(f"日志系统已初始化，日志目录: {LOG_PATH}")
logger.info(f"日志系统已初始化，日志轮转大小为: 300M")
logger.info(f"日志系统已初始化，日志保留大小为50个文件")
logger.info(f"日志系统已初始化，当前日志级别为：{logging.INFO}")
logger.info("*" * 80)