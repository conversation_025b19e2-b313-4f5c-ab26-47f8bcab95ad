#!/usr/bin/env python3
"""
通用的整点调度器
支持配置任意小时间隔的整点执行策略
"""

import time
import logging
from typing import Optional, Tuple

logger = logging.getLogger(__name__)

class HourlyScheduler:
    """整点调度器
    
    支持配置任意小时间隔的整点执行策略
    例如：1小时、2小时、3小时、4小时、6小时、8小时、12小时等
    """
    
    def __init__(self, interval_hours: int = 1, start_hour: int = 0):
        """
        初始化调度器
        
        Args:
            interval_hours: 执行间隔（小时），默认1小时
            start_hour: 起始小时（0-23），默认从0点开始
        """
        self.interval_hours = interval_hours
        self.start_hour = start_hour
        self._validate_config()
        
        # 计算有效的执行小时列表
        self.execution_hours = self._calculate_execution_hours()
        logger.info(f"调度器(整时)初始化完成: 间隔={interval_hours}小时, 起始={start_hour}点")
        logger.info(f"执行时间点: {[f'{h:02d}:00' for h in self.execution_hours]}")
    
    def _validate_config(self):
        """验证配置参数"""
        if not isinstance(self.interval_hours, int) or self.interval_hours < 1:
            raise ValueError(f"interval_hours 必须是大于0的整数，当前值: {self.interval_hours}")
        
        if not isinstance(self.start_hour, int) or not (0 <= self.start_hour <= 23):
            raise ValueError(f"start_hour 必须是0-23之间的整数，当前值: {self.start_hour}")
        
        if 24 % self.interval_hours != 0:
            logger.warning(f"间隔 {self.interval_hours} 小时不能整除24小时，可能导致执行时间不均匀")
    
    def _calculate_execution_hours(self) -> list:
        """计算24小时内的所有执行时间点"""
        execution_hours = []
        current_hour = self.start_hour
        
        while current_hour < 24:
            execution_hours.append(current_hour)
            current_hour += self.interval_hours
        
        return execution_hours
    
    def get_next_execution_time(self, current_time: Optional[time.struct_time] = None) -> Tuple[float, float, int]:
        """
        计算下一次执行时间
        
        Args:
            current_time: 当前时间，如果为None则使用系统当前时间
            
        Returns:
            tuple: (next_execution_timestamp, sleep_seconds, next_hour)
        """
        if current_time is None:
            now = time.localtime()
        else:
            now = current_time
        
        current_hour = now.tm_hour
        current_timestamp = time.mktime(now)
        
        # 找到下一个执行小时
        next_hour = self._find_next_execution_hour(current_hour)
        
        # 计算下一次执行的时间戳
        if next_hour <= current_hour:
            # 跨天到第二天
            next_execution_time = time.mktime((
                now.tm_year, now.tm_mon, now.tm_mday + 1,
                next_hour, 0, 0, 0, 0, -1
            ))
        else:
            # 今天内
            next_execution_time = time.mktime((
                now.tm_year, now.tm_mon, now.tm_mday,
                next_hour, 0, 0, 0, 0, -1
            ))
        
        sleep_seconds = next_execution_time - current_timestamp
        
        return next_execution_time, sleep_seconds, next_hour
    
    def _find_next_execution_hour(self, current_hour: int) -> int:
        """找到下一个执行小时"""
        # 在当天的执行时间中找到下一个大于当前小时的时间
        for hour in self.execution_hours:
            if hour > current_hour:
                return hour
        
        # 如果当天没有更晚的执行时间，返回第二天的第一个执行时间
        return self.execution_hours[0]
    
    def is_execution_time(self, current_time: Optional[time.struct_time] = None) -> bool:
        """
        检查当前时间是否为执行时间点
        
        Args:
            current_time: 当前时间，如果为None则使用系统当前时间
            
        Returns:
            bool: 是否为执行时间点
        """
        if current_time is None:
            now = time.localtime()
        else:
            now = current_time
        
        # 检查是否为整点且在执行时间列表中
        return now.tm_min == 0 and now.tm_sec == 0 and now.tm_hour in self.execution_hours
    
    def get_schedule_info(self) -> dict:
        """获取调度信息"""
        return {
            'interval_hours': self.interval_hours,
            'start_hour': self.start_hour,
            'execution_hours': self.execution_hours,
            'execution_times': [f'{h:02d}:00' for h in self.execution_hours],
            'executions_per_day': len(self.execution_hours),
            'description': f'每{self.interval_hours}小时执行一次，从{self.start_hour:02d}:00开始'
        }

def create_scheduler_from_config(config: dict) -> HourlyScheduler:
    """
    从配置创建调度器
    
    Args:
        config: 配置字典，包含 interval_hours 和可选的 start_hour
        
    Returns:
        HourlyScheduler: 调度器实例
    """
    interval_hours = config.get('interval_hours', 1)
    start_hour = config.get('start_hour', 0)
    
    return HourlyScheduler(interval_hours, start_hour)

# 预定义的常用调度器配置
SCHEDULER_PRESETS = {
    'hourly': {'interval_hours': 1, 'start_hour': 0},           # 每小时
    '2hourly': {'interval_hours': 2, 'start_hour': 0},          # 每2小时
    '3hourly': {'interval_hours': 3, 'start_hour': 0},          # 每3小时
    '4hourly': {'interval_hours': 4, 'start_hour': 0},          # 每4小时
    '6hourly': {'interval_hours': 6, 'start_hour': 0},          # 每6小时
    '8hourly': {'interval_hours': 8, 'start_hour': 0},          # 每8小时
    '12hourly': {'interval_hours': 12, 'start_hour': 0},        # 每12小时
    'daily': {'interval_hours': 24, 'start_hour': 0},           # 每天
    'business_hours': {'interval_hours': 4, 'start_hour': 8},   # 工作时间每4小时（8, 12, 16, 20点）
    'night_sync': {'interval_hours': 6, 'start_hour': 2},       # 夜间同步（2, 8, 14, 20点）
}

def get_preset_scheduler(preset_name: str) -> HourlyScheduler:
    """
    获取预设的调度器
    
    Args:
        preset_name: 预设名称
        
    Returns:
        HourlyScheduler: 调度器实例
    """
    if preset_name not in SCHEDULER_PRESETS:
        available_presets = ', '.join(SCHEDULER_PRESETS.keys())
        raise ValueError(f"未知的预设名称: {preset_name}. 可用预设: {available_presets}")
    
    config = SCHEDULER_PRESETS[preset_name]
    return HourlyScheduler(**config)


class FlexibleScheduler:
    """灵活调度器

    支持分钟级别的调度间隔
    例如：30分钟、45分钟、90分钟等
    """

    def __init__(self, interval_minutes: int = 60, start_time: str = "00:00"):
        """
        初始化调度器

        Args:
            interval_minutes: 执行间隔（分钟），默认60分钟
            start_time: 起始时间（HH:MM格式），默认"00:00"
        """
        self.interval_minutes = interval_minutes
        self.start_time = start_time
        self._validate_config()

        # 解析起始时间
        self.start_hour, self.start_minute = self._parse_start_time()

        # 计算24小时内的所有执行时间点
        self.execution_times = self._calculate_execution_times()
        logger.info(f"调度器(分钟)初始化完成: 间隔={interval_minutes}分钟, 起始={start_time}")
        logger.info(f"执行时间点: {[f'{h:02d}:{m:02d}' for h, m in self.execution_times]}")

    def _validate_config(self):
        """验证配置参数"""
        if not isinstance(self.interval_minutes, int) or self.interval_minutes < 1:
            raise ValueError(f"interval_minutes 必须是大于0的整数，当前值: {self.interval_minutes}")

        if self.interval_minutes > 1440:  # 24小时 = 1440分钟
            raise ValueError(f"interval_minutes 不能超过1440分钟，当前值: {self.interval_minutes}")

    def _parse_start_time(self):
        """解析起始时间"""
        try:
            parts = self.start_time.split(':')
            hour = int(parts[0])
            minute = int(parts[1]) if len(parts) > 1 else 0

            if not (0 <= hour <= 23):
                raise ValueError(f"小时必须在0-23之间，当前值: {hour}")
            if not (0 <= minute <= 59):
                raise ValueError(f"分钟必须在0-59之间，当前值: {minute}")

            return hour, minute
        except (ValueError, IndexError) as e:
            raise ValueError(f"start_time 格式错误，应为 HH:MM 格式，当前值: {self.start_time}")

    def _calculate_execution_times(self):
        """计算24小时内的所有执行时间点"""
        execution_times = []

        # 从起始时间开始，按间隔计算所有执行时间
        current_minutes = self.start_hour * 60 + self.start_minute

        while current_minutes < 1440:  # 24小时 = 1440分钟
            hour = current_minutes // 60
            minute = current_minutes % 60
            execution_times.append((hour, minute))
            current_minutes += self.interval_minutes

        return execution_times

    def get_next_execution_time(self, current_time=None):
        """计算下一次执行时间"""
        if current_time is None:
            now = time.localtime()
        else:
            now = current_time

        current_minutes = now.tm_hour * 60 + now.tm_min
        current_timestamp = time.mktime(now)

        # 找到下一个执行时间点
        next_hour, next_minute = self._find_next_execution_time(current_minutes)

        # 计算下一次执行的时间戳
        if (next_hour, next_minute) <= (now.tm_hour, now.tm_min):
            # 跨天到第二天
            next_execution_time = time.mktime((
                now.tm_year, now.tm_mon, now.tm_mday + 1,
                next_hour, next_minute, 0, 0, 0, -1
            ))
        else:
            # 今天内
            next_execution_time = time.mktime((
                now.tm_year, now.tm_mon, now.tm_mday,
                next_hour, next_minute, 0, 0, 0, -1
            ))

        sleep_seconds = next_execution_time - current_timestamp

        return next_execution_time, sleep_seconds, (next_hour, next_minute)

    def _find_next_execution_time(self, current_minutes):
        """找到下一个执行时间点"""
        # 在当天的执行时间中找到下一个大于当前时间的时间点
        for hour, minute in self.execution_times:
            time_minutes = hour * 60 + minute
            if time_minutes > current_minutes:
                return hour, minute

        # 如果当天没有更晚的执行时间，返回第二天的第一个执行时间
        return self.execution_times[0]

    def get_schedule_info(self):
        """获取调度信息"""
        return {
            'interval_minutes': self.interval_minutes,
            'interval_hours': self.interval_minutes / 60,
            'start_time': self.start_time,
            'execution_times': [f'{h:02d}:{m:02d}' for h, m in self.execution_times],
            'executions_per_day': len(self.execution_times),
            'description': f'每{self.interval_minutes}分钟执行一次，从{self.start_time}开始'
        }


# 工厂函数：根据配置创建合适的调度器
def create_scheduler_from_config(config: dict):
    """
    从配置创建调度器，自动选择合适的调度器类型

    Args:
        config: 配置字典

    Returns:
        调度器实例
    """
    # 检查是否有分钟级配置
    if 'interval_minutes' in config:
        interval_minutes = config.get('interval_minutes', 60)
        start_time = config.get('start_time', '00:00')
        return FlexibleScheduler(interval_minutes, start_time)

    # 使用小时级配置
    interval_hours = config.get('interval_hours', 1)
    start_hour = config.get('start_hour', 0)
    return HourlyScheduler(interval_hours, start_hour)

# 示例用法
if __name__ == "__main__":
    # 示例1: 创建2小时间隔的调度器
    scheduler_2h = HourlyScheduler(interval_hours=2)
    print("2小时调度器:", scheduler_2h.get_schedule_info())
    
    # 示例2: 创建3小时间隔的调度器
    scheduler_3h = HourlyScheduler(interval_hours=3)
    print("3小时调度器:", scheduler_3h.get_schedule_info())
    
    # 示例3: 使用预设
    scheduler_preset = get_preset_scheduler('3hourly')
    print("预设3小时调度器:", scheduler_preset.get_schedule_info())
    
    # 示例4: 计算下一次执行时间
    next_time, sleep_seconds, next_hour = scheduler_3h.get_next_execution_time()
    print(f"下一次执行: {next_hour:02d}:00, 等待 {sleep_seconds/3600:.2f} 小时")
