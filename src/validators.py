import logging
# from src.logger_config import logger

# 确保日志配置已设置
# setup_logging()
logger = logging.getLogger(__name__)

class FilterValidator:
    """基于条件的字段验证器"""

    def __init__(self, filter_checks):
        """初始化验证器
        
        Args:
            filter_checks: 过滤检查配置
        """
        self.filter_checks = filter_checks or []
        # 初始化条件组字典
        self.condition_groups = {}

        # 提取所有条件组
        for check in self.filter_checks:
            if check.get('template') == 'condition_group':
                group_id = check.get('id')
                if group_id:
                    self.condition_groups[group_id] = check.get('params', {})

    # 辅助方法 - 数值处理
    def is_number(self, x):
        """检查值是否可以转换为数字

        Args:
            x: 要检查的值

        Returns:
            bool: 是否可以转换为数字
        """
        if isinstance(x, (int, float)):
            return True
        if isinstance(x, str) and x.strip():
            try:
                float(x)
                return True
            except ValueError:
                return False
        return False

    def to_number(self, x):
        """尝试将值转换为数字，如果失败则返回None

        Args:
            x: 要转换的值

        Returns:
            float: 转换后的数值，如果转换失败则返回None
        """
        if self.is_number(x):
            try:
                return float(x)
            except (ValueError, TypeError):
                return None
        return None

    def compare_dates(self, date_value, min_date_str, format='%Y-%m-%d'):
        """比较日期值是否大于或等于最小日期

        Args:
            date_value: 要比较的日期值(字符串、时间戳等)
            min_date_str: 最小日期字符串(如 '2000-01-01')
            format: 日期字符串的格式，默认为'%Y-%m-%d'

        Returns:
            bool: date_value 是否大于或等于 min_date_str
        """
        import datetime
        import re

        # 记录输入值，用于调试
        logger.debug(f"比较日期: value='{date_value}', min_date='{min_date_str}', format='{format}'")
        
        # 如果date_value为None或空字符串，直接返回False
        if date_value is None or (isinstance(date_value, str) and not date_value.strip()):
            logger.debug("日期值为空，返回False")
            return False
        
        # 处理min_date_str
        # 1. 检查是否为模板变量格式 {variable_name}
        if isinstance(min_date_str, str):
            template_pattern = r'^\{[a-zA-Z_][a-zA-Z0-9_]*\}$'
            if re.match(template_pattern, min_date_str):
                logger.warning(f"最小日期参数是未替换的模板: {min_date_str}，使用默认值 '2000-01-01'")
                min_date_str = '2000-01-01'

        # 2. 确保min_date_str是有效的日期字符串
        try:
            # 尝试使用指定格式解析
            min_date = datetime.datetime.strptime(min_date_str, format)
            min_timestamp_ms = int(min_date.timestamp() * 1000)
            logger.debug(f"最小日期 '{min_date_str}' 使用格式 '{format}' 解析为毫秒时间戳: {min_timestamp_ms}")
        except (ValueError, TypeError):
            # 尝试其他常见格式
            min_timestamp_ms = None
            formats_to_try = ['%Y/%m/%d', '%Y-%m-%d', '%Y-%m-%d %H:%M:%S', '%Y-%m-%dT%H:%M:%S', '%Y-%m-%dT%H:%M:%SZ']

            for fmt in formats_to_try:
                try:
                    min_date = datetime.datetime.strptime(str(min_date_str), fmt)
                    min_timestamp_ms = int(min_date.timestamp() * 1000)
                    logger.debug(f"最小日期 '{min_date_str}' 使用格式 '{fmt}' 解析为毫秒时间戳: {min_timestamp_ms}")
                    break
                except (ValueError, TypeError):
                    continue

            # 如果所有格式都失败，使用默认值
            if min_timestamp_ms is None:
                logger.warning(f"无法解析最小日期 '{min_date_str}'，使用默认值 '2000-01-01'")
                min_date = datetime.datetime(2000, 1, 1)
                min_timestamp_ms = int(min_date.timestamp() * 1000)
        
        # 处理date_value
        value_timestamp_ms = None
        
        # 1. 如果是数字（时间戳）
        if self.is_number(date_value):
            try:
                value_timestamp_ms = float(date_value)
                # 如果是秒级时间戳，转换为毫秒级
                if value_timestamp_ms < 10000000000:  # 秒级时间戳通常小于10位数
                    value_timestamp_ms *= 1000
                logger.debug(f"数字时间戳 '{date_value}' 转换为毫秒时间戳: {value_timestamp_ms}")
            except (ValueError, TypeError):
                logger.error(f"无法将 '{date_value}' 转换为数字")
                return False
        
        # 2. 如果是ISO格式日期时间字符串
        elif isinstance(date_value, str) and 'T' in date_value:
            try:
                # 处理ISO格式 (2024-03-25T18:45:20Z 或 2024-03-25T18:45:20+00:00)
                iso_date = date_value.replace('Z', '+00:00')
                try:
                    dt = datetime.datetime.fromisoformat(iso_date)
                except (ValueError, AttributeError):
                    # Python 3.6不支持fromisoformat，使用strptime
                    dt = datetime.datetime.strptime(date_value.rstrip('Z'), '%Y-%m-%dT%H:%M:%S')
                
                value_timestamp_ms = int(dt.timestamp() * 1000)
                logger.debug(f"ISO日期 '{date_value}' 解析为毫秒时间戳: {value_timestamp_ms}")
            except Exception as e:
                logger.error(f"解析ISO日期失败: {e}")
                return False
        
        # 3. 如果是标准日期字符串
        elif isinstance(date_value, str):
            try:
                dt = datetime.datetime.strptime(date_value, format)
                value_timestamp_ms = int(dt.timestamp() * 1000)
                logger.debug(f"标准日期 '{date_value}' 使用格式 '{format}' 解析为毫秒时间戳: {value_timestamp_ms}")
            except ValueError:
                # 尝试其他常见格式
                formats_to_try = ['%Y/%m/%d', '%Y-%m-%d', '%d-%m-%Y', '%d/%m/%Y']
                for fmt in formats_to_try:
                    try:
                        dt = datetime.datetime.strptime(date_value, fmt)
                        value_timestamp_ms = int(dt.timestamp() * 1000)
                        logger.debug(f"日期 '{date_value}' 使用格式 '{fmt}' 解析为毫秒时间戳: {value_timestamp_ms}")
                        break
                    except ValueError:
                        continue
        
        # 如果无法解析日期值，返回False
        if value_timestamp_ms is None:
            logger.error(f"无法解析日期值: {date_value}")
            return False
        
        # 比较毫秒级时间戳
        result = value_timestamp_ms >= min_timestamp_ms
        logger.debug(f"比较结果: {result} (value_timestamp_ms={value_timestamp_ms} >= min_timestamp_ms={min_timestamp_ms})")
        
        return result

    def validate_record(self, item):
        """验证整个记录

        Args:
            item: 要验证的记录

        Returns:
            (bool, list): 验证结果和错误消息列表
        """
        errors = []

        for check in self.filter_checks:
            # 跳过条件组定义，它们不参与验证
            if check.get('template') == 'condition_group':
                continue

            # 只处理作用域为 'record' 的检查
            scope = check.get('scope', 'field')
            if scope != 'record':
                continue
            
            # 检查条件是否满足
            if self._evaluate_condition(check.get('condition', {}), item, check.get('condition_ref')):
                # 条件满足，执行检查
                for field_check in check.get('checks', []):
                    field = field_check.get('field')
                    rule = field_check.get('rule')
                    message = field_check.get('message', f"记录未通过配置项检查！")

                    if not rule:
                        continue

                    # 执行规则检查
                    if field:
                        # 检查特定字段
                        if not self._check_rule(rule, item.get(field), field_check):
                            errors.append(message)
                    else:
                        # 检查整个记录
                        if not self._check_record_rule(rule, item, field_check):
                            errors.append(message)

        return len(errors) == 0, errors

    def validate_field(self, item, field_name):
        """验证特定字段

        Args:
            item: 要验证的记录
            field_name: 字段名

        Returns:
            (bool, list): 验证结果和错误消息列表
        """
        errors = []

        for check in self.filter_checks:
            # 跳过条件组定义，它们不参与验证
            if check.get('template') == 'condition_group':
                continue

            # 只处理作用域为 'field' 的检查
            scope = check.get('scope', 'field')
            if scope != 'field':
                continue

            # 先收集与目标字段相关的检查项
            relevant_field_checks = []
            for field_check in check.get('checks', []):
                field = field_check.get('field')
                # 字段为None表示检查适用于所有字段，或者字段名匹配
                if field is None or field == field_name:
                    relevant_field_checks.append(field_check)
            # 如果没有相关字段检查，跳过此检查项
            if not relevant_field_checks:
                continue

            # 只有在有相关字段检查时才评估条件
            if self._evaluate_condition(check.get('condition', {}), item, check.get('condition_ref')):
                # 条件满足，执行已收集的相关字段检查
                for field_check in relevant_field_checks:
                    field = field_check.get('field')
                    rule = field_check.get('rule')
                    message = field_check.get('message', f"字段 {field} 未通过配置项检查！")
                    
                    if not rule:
                        # 如果没有指定规则，但有表达式，则使用custom规则
                        if 'expression' in field_check:
                            rule = 'custom'
                        else:
                            continue
                    
                    # 执行规则检查，传入上下文（整个记录）
                    if not self._check_rule(rule, item.get(field), field_check, context=item):
                        errors.append(message)
        
        return len(errors) == 0, errors

    def _evaluate_condition(self, condition, item, condition_ref=None):
        """评估条件
        
        Args:
            condition: 条件配置
            item: 要验证的项目
            condition_ref: 条件组引用ID

        Returns:
            bool: 条件是否满足
        """
        # 如果指定了条件组引用，使用条件组的条件
        if condition_ref and condition_ref in self.condition_groups:
            condition_params = self.condition_groups[condition_ref]
            # 从条件组参数中提取条件字段和值
            field = condition_params.get('condition_field')
            value = condition_params.get('condition_value')

            # 创建一个简单条件
            simple_condition = {
                'operator': 'eq',
                'field': field,
                'value': value
            }

            # 评估简单条件
            return self._evaluate_simple_condition(simple_condition, item)

        # 如果没有条件，则条件默认满足
        if not condition:
            return True
        
        operator = condition.get('operator', 'eq')
        
        # 处理复合条件
        if operator in ('and', 'or'):
            subconditions = condition.get('conditions', [])
            if not subconditions:
                return True
            
            results = [self._evaluate_condition(subcond, item) for subcond in subconditions]
            
            if operator == 'and':
                return all(results)
            else:  # operator == 'or'
                return any(results)
        
        # 处理简单条件
        return self._evaluate_simple_condition(condition, item)

    def _evaluate_simple_condition(self, condition, item):
        """评估简单条件

        Args:
            condition: 简单条件配置
            item: 要验证的项目

        Returns:
            bool: 条件是否满足
        """
        operator = condition.get('operator', 'eq')
        field = condition.get('field')
        value = condition.get('value')
        
        if not field:
            return True
        
        field_value = item.get(field)
        
        # 比较操作
        if operator == 'eq':
            return field_value == value
        elif operator == 'ne':
            return field_value != value
        elif operator == 'gt':
            try:
                return float(field_value) > float(value)
            except (ValueError, TypeError):
                return False
        elif operator == 'lt':
            try:
                return float(field_value) < float(value)
            except (ValueError, TypeError):
                return False
        elif operator == 'ge':
            try:
                return float(field_value) >= float(value)
            except (ValueError, TypeError):
                return False
        elif operator == 'le':
            try:
                return float(field_value) <= float(value)
            except (ValueError, TypeError):
                return False
        elif operator == 'in':
            if isinstance(value, list):
                return field_value in value
            return False
        elif operator == 'contains':
            if isinstance(field_value, str) and isinstance(value, str):
                return value in field_value
            return False
        elif operator == 'startswith':
            if isinstance(field_value, str) and isinstance(value, str):
                return field_value.startswith(value)
            return False
        elif operator == 'endswith':
            if isinstance(field_value, str) and isinstance(value, str):
                return field_value.endswith(value)
            return False
        elif operator == 'exists':
            return field_value is not None
        elif operator == 'not_exists':
            return field_value is None
        
        return True

    def _check_rule(self, rule, value, check_config, context=None):
        """检查规则

        Args:
            rule: 规则名称
            value: 字段值
            check_config: 检查配置
            context: 上下文数据（通常是整个记录）

        Returns:
            bool: 规则是否满足
        """
        if rule == 'required':
            return value is not None and value != ''
        elif rule == 'min_length':
            min_length = check_config.get('min_length', 0)
            return isinstance(value, str) and len(value) >= min_length
        elif rule == 'max_length':
            max_length = check_config.get('max_length', 0)
            return isinstance(value, str) and len(value) <= max_length
        elif rule == 'pattern':
            pattern = check_config.get('pattern')
            if not pattern or not isinstance(value, str):
                return True
            import re
            return bool(re.match(pattern, value))
        elif rule == 'min_value':
            min_value = check_config.get('min_value')
            try:
                return float(value) >= float(min_value)
            except (ValueError, TypeError):
                return False
        elif rule == 'max_value':
            max_value = check_config.get('max_value')
            try:
                return float(value) <= float(max_value)
            except (ValueError, TypeError):
                return False
        elif rule == 'custom':
            # 自定义规则，通过Python表达式实现
            expression = check_config.get('expression')
            if not expression:
                return True
            try:

                # 替换特例模版中的变量
                expression = self._replace_template_variables(expression, check_config)

                # 创建局部命名空间
                local_vars = {'value': value}
                # 如果有上下文，添加到局部变量中
                if context:
                    local_vars['context'] = context

                # 将check_config中的所有键值对添加到局部变量中
                for key, val in check_config.items():
                    if key not in ['expression', 'rule', 'field', 'message']:
                        local_vars[key] = val

                # 添加辅助方法引用
                local_vars['is_number'] = self.is_number
                local_vars['to_number'] = self.to_number
                local_vars['compare_dates'] = self.compare_dates

                # 执行表达式
                return eval(expression, {'__builtins__': {}}, local_vars)
            except Exception as e:
                logger.error(f"Error evaluating custom rule: {e}")
                return False

        return True

    def _check_record_rule(self, rule, record, check_config):
        """检查整条记录的规则

        Args:
            rule: 规则名称
            record: 整条记录
            check_config: 检查配置

        Returns:
            bool: 规则是否满足
        """
        if rule == 'custom':
            # 自定义规则，通过Python表达式实现
            expression = check_config.get('expression')
            if not expression:
                return True
            try:
                # 创建局部命名空间，传入整个记录
                local_vars = {'record': record}
                # 执行表达式
                return eval(expression, {'__builtins__': {}}, local_vars)
            except Exception as e:
                logger.error(f"Error evaluating custom record rule: {e}")
                return False
        elif rule == 'required_fields':
            # 检查多个必填字段
            required_fields = check_config.get('fields', [])
            for field in required_fields:
                if field not in record or record[field] is None or record[field] == '':
                    return False
            return True
        elif rule == 'conditional_required':
            # 条件必填：当某个字段满足条件时，其他字段必填
            condition_field = check_config.get('condition_field')
            condition_value = check_config.get('condition_value')
            required_field = check_config.get('required_field')

            if not condition_field or not required_field:
                return True

            # 检查条件是否满足
            if record.get(condition_field) == condition_value:
                # 条件满足，检查必填字段
                return required_field in record and record[required_field] is not None and record[required_field] != ''

            return True
        elif rule == 'field_dependency':
            # 字段依赖：当字段A存在时，字段B也必须存在
            dependent_field = check_config.get('dependent_field')
            required_field = check_config.get('required_field')

            if not dependent_field or not required_field:
                return True

            # 检查依赖字段是否存在且有值
            if dependent_field in record and record[dependent_field] is not None and record[dependent_field] != '':
                # 依赖字段存在，检查必填字段
                return required_field in record and record[required_field] is not None and record[required_field] != ''

            return True
        elif rule == 'field_exclusivity':
            # 字段互斥：字段A和字段B不能同时存在
            field_a = check_config.get('field_a')
            field_b = check_config.get('field_b')

            if not field_a or not field_b:
                return True

            # 检查两个字段是否同时存在且有值
            has_field_a = field_a in record and record[field_a] is not None and record[field_a] != ''
            has_field_b = field_b in record and record[field_b] is not None and record[field_b] != ''

            return not (has_field_a and has_field_b)

        return True

    def _replace_template_variables(self, expression, check_config):
        """替换表达式中的模板变量

        Args:
            expression: 表达式字符串
            check_config: 检查配置

        Returns:
            str: 替换后的表达式
        """
        import re

        # 检查表达式中是否包含未替换的模板变量
        template_pattern = r'\{([a-zA-Z_][a-zA-Z0-9_]*)\}'
        matches = re.findall(template_pattern, expression)

        if matches:
            logger.debug(f"表达式中包含未替换的模板变量: {matches}")

            # 遍历所有匹配的模板变量
            for var_name in matches:
                # 尝试从check_config中获取变量值
                if var_name in check_config:
                    var_value = check_config[var_name]

                    # 根据变量值的类型进行不同的替换
                    if isinstance(var_value, str):
                        # 字符串需要加引号
                        expression = expression.replace(f"'{{{var_name}}}'", f"'{var_value}'")
                        # 也替换没有引号的情况
                        expression = expression.replace(f"{{{var_name}}}", f"'{var_value}'")
                    else:
                        # 非字符串不需要加引号
                        expression = expression.replace(f"'{{{var_name}}}'", str(var_value))
                        expression = expression.replace(f"{{{var_name}}}", str(var_value))

                    logger.debug(f"替换模板变量 {var_name} 为 {var_value}，替换后表达式: {expression}")

        return expression