import os
import pytest
import logging

from src.config_loader import Config<PERSON>oader
from tests.mock_api_client import MockAPIClient

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@pytest.fixture
def env_name():
    """获取当前测试环境名称"""
    return os.environ.get('CMDB_SYNC_ENV', 'dev')

@pytest.fixture
def mock_enabled():
    """检查是否启用模拟"""
    return os.environ.get('CMDB_SYNC_MOCK', 'false').lower() == 'true'

@pytest.fixture
def config_path(env_name):
    """获取配置文件路径"""
    # 基础配置文件
    base_config_path = './config/base_config.yaml'
    
    # 环境特定配置文件
    env_config_path = f'./config/environments/{env_name}.yaml'
    
    return {
        'base': base_config_path,
        'env': env_config_path
    }

@pytest.fixture
def config(config_path):
    """加载配置"""
    try:
        # 加载配置
        config_loader = ConfigLoader(config_path['base'])
        config = config_loader.load_config()
        
        # 加载环境特定配置
        # 注意：在实际实现中，ConfigLoader应该支持加载环境特定配置
        # 这里简化处理，假设ConfigLoader已经处理了环境特定配置
        
        logger.info(f"Loaded configuration for testing")
        return config
    except Exception as e:
        logger.error(f"Failed to load configuration: {e}")
        raise

@pytest.fixture
def api_client(config, mock_enabled):
    """创建API客户端"""
    # 确保mock配置存在
    if 'base' not in config:
        config['base'] = {}
    if 'mock' not in config['base']:
        config['base']['mock'] = {}
    
    # 设置mock启用状态
    config['base']['mock']['enabled'] = mock_enabled
    
    # 创建模拟API客户端
    client = MockAPIClient(config)
    logger.info(f"Created API client with mock enabled: {mock_enabled}")
    
    return client

@pytest.fixture
def mock_data_dir():
    """获取模拟数据目录"""
    return './tests/mock_data'

@pytest.fixture
def prepare_mock_data(mock_data_dir):
    """准备模拟数据"""
    # 确保模拟数据目录存在
    os.makedirs(os.path.join(mock_data_dir, 'cloud_side'), exist_ok=True)
    os.makedirs(os.path.join(mock_data_dir, 'internal_side'), exist_ok=True)
    
    # 这里可以添加创建模拟数据文件的逻辑
    # 例如，如果文件不存在，则创建默认的模拟数据文件
    
    logger.info(f"Prepared mock data in {mock_data_dir}")
    
    # 返回清理函数
    def cleanup():
        # 这里可以添加清理逻辑，如删除临时文件
        pass
    
    return cleanup
