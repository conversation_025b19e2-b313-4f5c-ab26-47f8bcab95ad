import json
import os
import logging
from typing import Dict, Any, List

class MockAPIClient:
    """模拟API客户端，用于测试
    
    这个客户端可以根据配置选择使用真实API或模拟数据。
    当使用模拟数据时，它会从文件加载预定义的响应。
    它还会记录所有API调用，便于验证调用是否正确。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化模拟API客户端
        
        Args:
            config: 配置对象，包含API和模拟设置
        """
        self.config = config
        self.base_config = config.get('base', {})
        self.mock_config = self.base_config.get('mock', {})
        self.mock_enabled = self.mock_config.get('enabled', False)
        self.mock_data_dir = self.mock_config.get('data_dir', './tests/mock_data')
        self.calls = []  # 记录API调用
        self.logger = logging.getLogger(__name__)
    
    def call(self, api_name: str, endpoint: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """调用API，如果启用了模拟，则返回模拟数据
        
        Args:
            api_name: API名称，如'cloud_side'或'internal_side'
            endpoint: 端点名称，如'get_vms'
            params: 请求参数
            
        Returns:
            API响应数据
            
        Raises:
            Exception: 如果模拟数据加载失败或API调用失败
        """
        # 记录调用
        self.calls.append({
            'api_name': api_name,
            'endpoint': endpoint,
            'params': params
        })
        
        # 检查是否应该模拟此API
        should_mock = self.mock_enabled and self.mock_config.get(api_name, False)
        
        if should_mock:
            # 从文件加载模拟数据
            mock_file = os.path.join(self.mock_data_dir, api_name, f"{endpoint.replace('/', '_')}.json")
            try:
                with open(mock_file, 'r', encoding='utf-8') as f:
                    mock_data = json.load(f)
                self.logger.debug(f"Loaded mock data from {mock_file}")
                return mock_data
            except Exception as e:
                self.logger.error(f"Failed to load mock data from {mock_file}: {e}")
                raise Exception(f"Failed to load mock data from {mock_file}: {e}")
        else:
            # 调用真实API
            try:
                # 获取API配置
                api_config = self.base_config.get('apis', {}).get(api_name, {})
                base_url = api_config.get('base_url', '')
                
                # 获取端点URL
                endpoints = self.config.get('endpoints', {}).get(api_name, {})
                endpoint_url = endpoints.get(endpoint, '')
                
                if not endpoint_url:
                    self.logger.error(f"Endpoint not found: {api_name}.{endpoint}")
                    raise Exception(f"Endpoint not found: {api_name}.{endpoint}")
                
                # 构建完整URL
                url = f"{base_url}{endpoint_url}"
                
                # 这里实现真实API调用逻辑
                # 例如使用requests库调用API
                # response = requests.post(url, json=params, headers=headers)
                # return response.json()
                
                # 临时返回空对象，实际实现中应该替换为真实API调用
                self.logger.debug(f"Called real API: {url}")
                return {}
            except Exception as e:
                self.logger.error(f"API call failed: {api_name}.{endpoint}: {e}")
                raise Exception(f"API call failed: {api_name}.{endpoint}: {e}")
    
    def get_calls(self) -> List[Dict[str, Any]]:
        """获取记录的API调用
        
        Returns:
            API调用列表，每个调用包含api_name、endpoint和params
        """
        return self.calls
    
    def clear_calls(self):
        """清除记录的API调用"""
        self.calls = []
    
    def test_connection(self, api_name: str) -> bool:
        """测试API连接是否正常
        
        Args:
            api_name: API名称，如'cloud_side'或'internal_side'
            
        Returns:
            连接是否正常
            
        Raises:
            Exception: 如果连接测试失败
        """
        try:
            # 获取API配置
            api_config = self.base_config.get('apis', {}).get(api_name, {})
            base_url = api_config.get('base_url', '')
            
            # 检查是否应该模拟此API
            should_mock = self.mock_enabled and self.mock_config.get(api_name, False)
            
            if should_mock:
                # 模拟连接测试
                self.logger.info(f"Mock connection test for {api_name}")
                return True
            else:
                # 真实连接测试
                # 这里实现真实连接测试逻辑
                # 例如使用requests库调用API
                # response = requests.get(f"{base_url}/health")
                # return response.status_code == 200
                
                # 临时返回True，实际实现中应该替换为真实连接测试
                self.logger.info(f"Real connection test for {api_name}: {base_url}")
                return True
        except Exception as e:
            self.logger.error(f"Connection test failed for {api_name}: {e}")
            raise Exception(f"Connection test failed for {api_name}: {e}")
