#!/usr/bin/env python
# -*- coding: utf-8 -*-

import argparse
import os
import sys
import pytest

def main():
    """CMDB同步工具测试运行脚本
    
    这个脚本用于运行CMDB同步工具的测试，支持以下功能：
    - 选择测试环境（dev、test、prod）
    - 启用或禁用模拟
    - 选择测试类型（单元测试、集成测试、性能测试）
    - 指定要测试的模型
    - 生成测试报告
    """
    parser = argparse.ArgumentParser(description='运行CMDB同步工具测试')
    parser.add_argument('--env', default='dev', choices=['dev', 'test', 'prod'], help='环境')
    parser.add_argument('--mock', action='store_true', help='启用模拟')
    parser.add_argument('--unit', action='store_true', help='运行单元测试')
    parser.add_argument('--integration', action='store_true', help='运行集成测试')
    parser.add_argument('--performance', action='store_true', help='运行性能测试')
    parser.add_argument('--model', help='指定要测试的模型')
    parser.add_argument('--report', action='store_true', help='生成测试报告')
    parser.add_argument('--coverage', action='store_true', help='生成覆盖率报告')
    args = parser.parse_args()
    
    # 设置环境变量
    os.environ['CMDB_SYNC_ENV'] = args.env
    if args.mock:
        os.environ['CMDB_SYNC_MOCK'] = 'true'
    else:
        os.environ.pop('CMDB_SYNC_MOCK', None)
    
    # 构建pytest参数
    pytest_args = []
    
    # 添加测试目录
    if args.unit:
        pytest_args.append('tests/unit')
    if args.integration:
        pytest_args.append('tests/integration')
    if args.performance:
        pytest_args.append('tests/performance')
    
    # 如果没有指定任何测试类型，运行所有测试
    if not (args.unit or args.integration or args.performance):
        pytest_args.append('tests')
    
    # 添加模型过滤
    if args.model:
        pytest_args.append(f'-k {args.model}')
    
    # 添加报告参数
    if args.report:
        pytest_args.append('--html=report.html')
    
    # 添加覆盖率参数
    if args.coverage:
        pytest_args.append('--cov=src')
        pytest_args.append('--cov-report=html')
    
    # 打印测试信息
    print(f"Running tests with environment: {args.env}")
    print(f"Mock enabled: {args.mock}")
    if args.model:
        print(f"Testing model: {args.model}")
    
    # 运行测试
    return pytest.main(pytest_args)

if __name__ == '__main__':
    sys.exit(main())
