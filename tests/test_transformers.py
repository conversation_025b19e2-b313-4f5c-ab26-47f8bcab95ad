def test_concat_fields():
    """测试 concat_fields 转换函数"""
    # 创建转换函数
    code = """
def concat_fields(item, related=None, params=None):
    if not params or 'fields' not in params:
        return None
        
    fields = params.get('fields', [])
    separator = params.get('separator', ',')
    skip_empty = params.get('skip_empty', True)
    
    values = []
    for field in fields:
        value = item.get(field)
        if value is not None and (not skip_empty or str(value).strip()):
            values.append(str(value).strip())
    
    if not values:
        return None
        
    return separator.join(values)
"""
    
    # 创建本地命名空间
    local_namespace = {}
    
    # 执行代码
    exec(code, globals(), local_namespace)
    
    # 获取转换函数
    concat_fields = local_namespace.get('concat_fields')
    
    # 测试用例
    test_cases = [
        # 基本测试
        {
            'item': {'AdministratorA': 'Alice', 'AdministratorB': 'Bob'},
            'params': {'fields': ['AdministratorA', 'AdministratorB'], 'separator': ';'},
            'expected': 'Alice;<PERSON>'
        },
        # 跳过空值
        {
            'item': {'AdministratorA': 'Alice', 'AdministratorB': ''},
            'params': {'fields': ['AdministratorA', 'AdministratorB'], 'separator': ';', 'skip_empty': True},
            'expected': 'Alice'
        },
        # 不跳过空值
        {
            'item': {'AdministratorA': 'Alice', 'AdministratorB': ''},
            'params': {'fields': ['AdministratorA', 'AdministratorB'], 'separator': ';', 'skip_empty': False},
            'expected': 'Alice;'
        },
        # 字段不存在
        {
            'item': {'AdministratorA': 'Alice'},
            'params': {'fields': ['AdministratorA', 'AdministratorB'], 'separator': ';'},
            'expected': 'Alice'
        },
        # 所有字段都不存在
        {
            'item': {},
            'params': {'fields': ['AdministratorA', 'AdministratorB'], 'separator': ';'},
            'expected': None
        }
    ]
    
    for i, test_case in enumerate(test_cases):
        result = concat_fields(test_case['item'], None, test_case['params'])
        print(result)
        assert result == test_case['expected'], f"Test case {i} failed: expected {test_case['expected']}, got {result}"

def test_calculate_expression():
    """测试 calculate_expression 转换函数"""
    # 创建转换函数
    code = """
def calculate_expression(item, related=None, params=None):
    if not params or 'expression' not in params:
        return 0
        
    expression = params.get('expression')
    format_str = params.get('format', '{value}')
    default = params.get('default', 0)
    
    # 替换表达式中的字段引用
    for field_name in item.keys():
        if field_name in expression:
            field_value = item.get(field_name)
            if field_value is None:
                field_value = 0
            try:
                # 尝试转换为数值
                numeric_value = float(field_value)
                expression = expression.replace(field_name, str(numeric_value))
            except (ValueError, TypeError):
                # 非数值字段，替换为0
                expression = expression.replace(field_name, '0')
    
    try:
        # 计算表达式
        result = eval(expression)
        
        # 应用格式化
        return format_str.format(value=result)
    except Exception as e:
        # 计算失败，返回默认值
        return default
"""

    # 创建本地命名空间
    local_namespace = {}

    # 执行代码
    exec(code, globals(), local_namespace)

    # 获取转换函数
    calculate_expression = local_namespace.get('calculate_expression')

    # 测试用例
    test_cases = [
        # 基本测试
        {
            'item': {'totalCapacity': 1000, 'usedCapacity': 300},
            'params': {'expression': 'totalCapacity - usedCapacity', 'format': '{value}'},
            'expected': '700.0'
        },
        # 格式化结果
        {
            'item': {'totalCapacity': 1000, 'usedCapacity': 300},
            'params': {'expression': 'totalCapacity - usedCapacity', 'format': '{value:.0f}'},
            'expected': '700'
        },
        # 复杂表达式
        {
            'item': {'totalCapacity': 1000, 'usedCapacity': 300, 'reservedCapacity': 100},
            'params': {'expression': 'totalCapacity - usedCapacity - reservedCapacity', 'format': '{value}'},
            'expected': '600.0'
        },
        # 字段不存在
        {
            'item': {'totalCapacity': 1000},
            'params': {'expression': 'totalCapacity - usedCapacity', 'format': '{value}', 'default': 'N/A'},
            'expected': 'N/A'
        },
        # 非数值字段
        {
            'item': {'totalCapacity': 1000, 'usedCapacity': 'unknown'},
            'params': {'expression': 'totalCapacity - usedCapacity', 'format': '{value}'},
            'expected': '1000.0'
        }
    ]

    for i, test_case in enumerate(test_cases):
        result = calculate_expression(test_case['item'], None, test_case['params'])
        print(result)
        assert result == test_case['expected'], f"Test case {i} failed: expected {test_case['expected']}, got {result}"
