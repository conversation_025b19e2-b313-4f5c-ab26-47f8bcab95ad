import pytest
import os
import yaml
import json
import copy
from pathlib import Path
from unittest.mock import patch, MagicMock

from src.validators import FilterValidator
from src.config_loader import ConfigLoader

# 创建测试目录
@pytest.fixture
def test_dir(tmp_path):
    """创建测试目录结构"""
    # 创建配置目录
    config_dir = tmp_path / "config"
    config_dir.mkdir()
    
    # 创建模型目录
    models_dir = config_dir / "models"
    models_dir.mkdir()
    
    return tmp_path

@pytest.fixture
def check_templates_yaml(test_dir):
    """创建测试用的check_templates.yaml文件"""
    templates_path = test_dir / "config" / "check_templates.yaml"
    
    # 定义测试模板
    templates_content = {
        "templates": {
            "numeric_range": {
                "description": "检查数值是否在指定范围内",
                "template": {
                    "type": "custom",
                    "rule": "custom",
                    "checks": [
                        {
                            "expression": "value is None or value < {max_value}"
                        }
                    ]
                }
            }
        }
    }
    
    # 写入文件
    with open(templates_path, 'w', encoding='utf-8') as f:
        yaml.dump(templates_content, f, allow_unicode=True)
    
    return templates_path

@pytest.fixture
def model_config_yaml(test_dir):
    """创建测试用的模型配置文件"""
    model_path = test_dir / "config" / "models" / "test_model.yaml"
    
    # 定义测试模型配置
    model_content = {
        "model_id": "test_model",
        "check_templates": [
            {
                "template": "numeric_range",
                "scope": "field",
                "params": {
                    "max_value": 10000
                }
            }
        ]
    }
    
    # 写入文件
    with open(model_path, 'w', encoding='utf-8') as f:
        yaml.dump(model_content, f, allow_unicode=True)
    
    return model_path

@pytest.fixture
def base_config_yaml(test_dir):
    """创建测试用的基础配置文件"""
    config_path = test_dir / "config" / "base_config.yaml"
    
    # 定义基础配置
    config_content = {
        "global": {
            "templates_dir": "./config/check_templates.yaml"
        },
        "models_dir": "./config/models"
    }
    
    # 写入文件
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config_content, f, allow_unicode=True)
    
    return config_path

@pytest.fixture
def mock_config_loader(test_dir, base_config_yaml, check_templates_yaml, model_config_yaml):
    """创建模拟的ConfigLoader"""
    with patch('src.config_loader.baseDir', test_dir):
        # 创建一个真实的ConfigLoader实例
        config_loader = ConfigLoader(base_config_yaml)
        
        # 手动添加模型配置
        model_id = 'test_model'
        with open(model_config_yaml, 'r', encoding='utf-8') as f:
            model_config = yaml.safe_load(f)

        # 手动处理检查模板
        with open(check_templates_yaml, 'r', encoding='utf-8') as f:
            templates_config = yaml.safe_load(f)
            templates = templates_config.get('templates', {})

        # 获取模板和参数
        template_type = 'numeric_range'
        template = templates[template_type]['template']
        params = {'max_value': 10000}

        # 创建检查对象
        check = copy.deepcopy(template)
        check['scope'] = 'field'
        
        # 添加参数
        for key, value in params.items():
            check[key] = value
            # 添加到checks中
            for check_item in check.get('checks', []):
                check_item[key] = value

        # 添加到filter_checks
        model_config['filter_checks'] = [check]

        # 添加到模型配置
        config_loader.models = {model_id: model_config}

        return config_loader

def test_max_value_validation(mock_config_loader):
    """测试max_value验证功能"""
    # 获取模型配置
    test_model_config = mock_config_loader.models.get('test_model', {})

    # 检查filter_checks是否存在
    assert 'filter_checks' in test_model_config
    filter_checks = test_model_config['filter_checks']

    # 检查是否有一个检查项
    assert len(filter_checks) == 1
    check = filter_checks[0]

    # 检查检查项的类型
    assert check['type'] == 'custom'

    # 检查max_value参数是否正确传递
    assert 'max_value' in check
    assert check['max_value'] == 10000

    # 检查检查项中的表达式
    assert 'checks' in check
    assert len(check['checks']) == 1
    assert 'expression' in check['checks'][0]
    assert check['checks'][0]['expression'] == 'value is None or value < {max_value}'

    # 检查max_value是否也添加到了检查项中
    assert 'max_value' in check['checks'][0]
    assert check['checks'][0]['max_value'] == 10000

    # 创建验证器
    validator = FilterValidator(filter_checks)

    # 测试验证功能 - 有效值
    valid_item = {'test_field': 5000}
    is_valid, errors = validator.validate_field(valid_item, 'test_field')
    assert is_valid is True
    assert not errors

    # 测试验证功能 - 无效值
    invalid_item = {'test_field': 15000}
    is_valid, errors = validator.validate_field(invalid_item, 'test_field')
    assert is_valid is False
    assert errors

def test_max_value_validation_direct():
    """直接测试FilterValidator的max_value验证功能"""
    # 创建一个包含max_value检查的配置
    filter_checks = [
        {
            "type": "custom",
            "rule": "custom",
            "scope": "field",
            "max_value": 10000,
            "checks": [
                {
                    "expression": "value is None or value < {max_value}",
                    "max_value": 10000
                }
            ]
        }
    ]
    
    # 创建验证器
    validator = FilterValidator(filter_checks)
    
    # 测试有效值
    valid_item = {'test_field': 5000}
    is_valid, errors = validator.validate_field(valid_item, 'test_field')
    assert is_valid is True
    assert not errors
    
    # 测试无效值
    invalid_item = {'test_field': 15000}
    is_valid, errors = validator.validate_field(invalid_item, 'test_field')
    assert is_valid is False
    assert errors
    
    # 测试None值
    none_item = {'test_field': None}
    is_valid, errors = validator.validate_field(none_item, 'test_field')
    assert is_valid is True
    assert not errors

def test_process_check_templates():
    """测试_process_check_templates方法"""
    # 创建模拟的ConfigLoader实例
    config_loader = MagicMock()
    config_loader.logger = MagicMock()
    
    # 创建模拟的base_config
    config_loader.base_config = {
        'global': {
            'templates_dir': './config/check_templates.yaml'
        }
    }
    
    # 创建模拟的模板配置
    templates_config = {
        'templates': {
            'numeric_range': {
                'description': '检查数值是否在指定范围内',
                'template': {
                    'type': 'custom',
                    'rule': 'custom',
                    'checks': [
                        {
                            'expression': 'value is None or value < {max_value}'
                        }
                    ]
                }
            }
        }
    }
    
    # 创建模拟的模型配置
    model_config = {
        'model_id': 'test_model',
        'check_templates': [
            {
                'template': 'numeric_range',
                'scope': 'field',
                'params': {
                    'max_value': 10000
                }
            }
        ]
    }
    
    # 定义一个简单的_add_params_to_check函数
    def mock_add_params_to_check(check, params):
        result = check.copy()
        # 将参数添加到检查对象中
        for key, value in params.items():
            result[key] = value
        # 将参数也添加到checks中的每个检查项中
        if 'checks' in result:
            for check_item in result['checks']:
                for key, value in params.items():
                    check_item[key] = value
        return result

    # 模拟open函数
    with patch('builtins.open', MagicMock()):
        # 模拟yaml.safe_load函数
        with patch('yaml.safe_load', return_value=templates_config):
            # 模拟Path
            with patch('pathlib.Path', MagicMock()):
                # 模拟_add_params_to_check方法
                with patch.object(config_loader, '_add_params_to_check', side_effect=mock_add_params_to_check):
                    # 调用_process_check_templates方法
                    from src.config_loader import ConfigLoader
                    ConfigLoader._process_check_templates(config_loader, model_config)
    
    # 检查结果
    assert 'filter_checks' in model_config
    assert len(model_config['filter_checks']) == 1
    
    check = model_config['filter_checks'][0]
    assert check['type'] == 'custom'
    assert check['rule'] == 'custom'
    assert check['scope'] == 'field'
    assert check['max_value'] == 10000
    
    assert 'checks' in check
    assert len(check['checks']) == 1
    assert 'expression' in check['checks'][0]
    assert check['checks'][0]['expression'] == 'value is None or value < {max_value}'
    assert check['checks'][0]['max_value'] == 10000