import os
from src.config_loader import Config<PERSON>oader

import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

def test_config_loader_loads_base_config(config_path):
    """测试配置加载器能够正确加载基础配置"""
    config_loader = ConfigLoader(config_path['base'])
    config = config_loader.load_config()
    
    # 验证基础配置已加载
    assert 'base' in config
    assert 'global' in config['base']
    assert 'apis' in config['base']
    assert 'cache' in config['base']
    assert 'logging' in config['base']

def test_config_loader_loads_endpoints(config_path):
    """测试配置加载器能够正确加载端点配置"""
    config_loader = ConfigLoader(config_path['base'])
    config = config_loader.load_config()
    
    # 验证端点配置已加载
    assert 'endpoints' in config
    assert 'cloud_side' in config['endpoints']
    assert 'internal_side' in config['endpoints']
    
    # 验证端点配置内容
    cloud_endpoints = config['endpoints']['cloud_side']
    assert 'get_vms' in cloud_endpoints
    assert 'get_volumes' in cloud_endpoints
    assert 'get_vms' in cloud_endpoints
    assert 'get_volumes' in cloud_endpoints
    assert 'get_vms' in cloud_endpoints
    assert 'get_volumes' in cloud_endpoints
    
    internal_endpoints = config['endpoints']['internal_side']
    assert 'get_ci' in internal_endpoints
    assert 'update_ci' in internal_endpoints

def test_config_loader_loads_transformers(config_path):
    """测试配置加载器能够正确加载转换函数配置"""
    config_loader = ConfigLoader(config_path['base'])
    config = config_loader.load_config()
    
    # 验证转换函数配置已加载
    assert 'transformers' in config
    assert 'format_datetime' in config['transformers']
    assert 'format_ip_info' in config['transformers']
    assert 'map_status' in config['transformers']

def test_config_loader_loads_models(config_path):
    """测试配置加载器能够正确加载模型配置"""
    config_loader = ConfigLoader(config_path['base'])
    config = config_loader.load_config()
    
    # 验证模型配置已加载
    assert 'models' in config
    assert 'pc_virtual_machine' in config['models']
    
    # 验证模型配置内容
    model = config['models']['pc_virtual_machine']
    assert 'model_id' in model
    assert 'cloud_side' in model
    assert 'internal_side' in model
    assert 'field_mappings' in model
    
    # 验证字段映射
    field_mappings = model['field_mappings']
    assert len(field_mappings) > 0
    assert 'internal_field' in field_mappings[0]
    assert 'cloud_field' in field_mappings[0]
    assert 'sync_direction' in field_mappings[0]

def test_config_loader_handles_model_transformers(config_path):
    """测试配置加载器能够正确处理模型特有的转换函数"""
    config_loader = ConfigLoader(config_path['base'])
    config = config_loader.load_config()
    
    # 验证模型特有的转换函数已加载到全局转换函数中
    transformers = config['transformers']
    assert 'pc_virtual_machine_get_host_name' in transformers
    assert 'pc_virtual_machine_get_service_ip' in transformers
    
    # 验证字段映射中的转换函数引用已更新
    model = config['models']['pc_virtual_machine']
    field_mappings = model['field_mappings']
    
    # 查找使用get_host_name的字段映射
    host_server_mapping = next((m for m in field_mappings if m['internal_field'] == 'HostedServer'), None)
    assert host_server_mapping is not None
    assert host_server_mapping['transform'] == 'pc_virtual_machine_get_host_name'
    
    # 查找使用get_service_ip的字段映射
    service_ip_mapping = next((m for m in field_mappings if m['internal_field'] == 'ServiceIPAddr'), None)
    assert service_ip_mapping is not None
    assert service_ip_mapping['transform'] == 'pc_virtual_machine_get_service_ip'
