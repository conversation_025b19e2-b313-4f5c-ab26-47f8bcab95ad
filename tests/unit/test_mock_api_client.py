import os
import json
import pytest
from tests.mock_api_client import MockAPIClient

@pytest.fixture
def mock_config():
    """创建用于测试的模拟配置"""
    return {
        'base': {
            'apis': {
                'cloud_side': {
                    'base_url': 'https://example.com',
                    'auth': {'type': 'none'}
                },
                'internal_side': {
                    'base_url': 'https://internal.example.com',
                    'auth': {'type': 'none'}
                }
            },
            'mock': {
                'enabled': True,
                'cloud_side': True,
                'internal_side': True,
                'data_dir': './tests/mock_data'
            }
        },
        'endpoints': {
            'cloud_side': {
                'get_vms': '/api/v1/vms',
                'get_volumes': '/api/v1/volumes'
            },
            'internal_side': {
                'get_ci': '/cmdb/api/class/getCommonDynamicData',
                'update_ci': '/cmdb/api/class/saveOrUpdateCIData'
            }
        }
    }

@pytest.fixture
def prepare_mock_data_files(mock_config):
    """准备模拟数据文件"""
    mock_data_dir = mock_config['base']['mock']['data_dir']
    
    # 确保目录存在
    os.makedirs(os.path.join(mock_data_dir, 'cloud_side'), exist_ok=True)
    os.makedirs(os.path.join(mock_data_dir, 'internal_side'), exist_ok=True)
    
    # 创建模拟数据文件
    cloud_vms_data = {
        'data': [
            {'id': 'vm1', 'name': 'VM 1', 'status': 'ACTIVE'},
            {'id': 'vm2', 'name': 'VM 2', 'status': 'SHUTOFF'}
        ]
    }
    
    internal_ci_data = {
        'data': [
            {'Code': 'PCVirtualHost_192.168.1.1', 'Description': 'Test VM 1'},
            {'Code': 'PCVirtualHost_192.168.1.2', 'Description': 'Test VM 2'}
        ]
    }
    
    # 写入模拟数据文件
    with open(os.path.join(mock_data_dir, 'cloud_side', '_api_v1_vms.json'), 'w') as f:
        json.dump(cloud_vms_data, f)
    
    with open(os.path.join(mock_data_dir, 'internal_side', '_cmdb_api_class_getCommonDynamicData.json'), 'w') as f:
        json.dump(internal_ci_data, f)
    
    # 返回清理函数
    def cleanup():
        # 删除创建的文件
        try:
            os.remove(os.path.join(mock_data_dir, 'cloud_side', '_api_v1_vms.json'))
            os.remove(os.path.join(mock_data_dir, 'internal_side', '_cmdb_api_class_getCommonDynamicData.json'))
        except:
            pass
    
    return cleanup

def test_mock_api_client_initialization(mock_config):
    """测试模拟API客户端的初始化"""
    client = MockAPIClient(mock_config)
    
    # 验证客户端已正确初始化
    assert client.mock_enabled == True
    assert client.mock_data_dir == './tests/mock_data'
    assert len(client.calls) == 0

def test_mock_api_client_calls_endpoint(mock_config, prepare_mock_data_files):
    """测试模拟API客户端能够正确调用端点"""
    client = MockAPIClient(mock_config)
    
    # 调用云内API
    result = client.call('cloud_side', 'get_x86servers')
    
    # 验证结果
    assert result is not None
    assert 'data' in result
    assert len(result['data']) == 2
    assert result['data'][0]['id'] == 'vm1'
    
    # 验证调用记录
    calls = client.get_calls()
    assert len(calls) == 1
    assert calls[0]['api_name'] == 'cloud_side'
    assert calls[0]['endpoint'] == 'get_vms'
    
    # 清理
    prepare_mock_data_files()

def test_mock_api_client_handles_missing_mock_data(mock_config):
    """测试模拟API客户端能够处理缺失的模拟数据"""
    client = MockAPIClient(mock_config)
    
    # 调用不存在模拟数据的端点
    with pytest.raises(Exception) as excinfo:
        client.call('cloud_side', 'get_nonexistent')
    
    # 验证异常
    assert "Failed to load mock data" in str(excinfo.value)
    
    # 验证调用记录
    calls = client.get_calls()
    assert len(calls) == 1
    assert calls[0]['api_name'] == 'cloud_side'
    assert calls[0]['endpoint'] == 'get_nonexistent'

def test_mock_api_client_clear_calls(mock_config, prepare_mock_data_files):
    """测试模拟API客户端能够清除调用记录"""
    client = MockAPIClient(mock_config)
    
    # 调用API
    client.call('cloud_side', 'get_vms')
    
    # 验证调用记录
    assert len(client.get_calls()) == 1
    
    # 清除调用记录
    client.clear_calls()
    
    # 验证调用记录已清除
    assert len(client.get_calls()) == 0
    
    # 清理
    prepare_mock_data_files()

def test_mock_api_client_test_connection(mock_config):
    """测试模拟API客户端的连接测试功能"""
    client = MockAPIClient(mock_config)
    
    # 测试连接
    result = client.test_connection('cloud_side')
    
    # 验证结果
    assert result == True
