import os
import json
import pytest
import time
import copy
from unittest.mock import patch, MagicMock, mock_open
from src.sync_manager import SyncManager

# 测试数据
TEST_CONFIG = {
    "api": {
        "cloud_side": {
            "base_url": "http://cloud-api.example.com",
            "auth": {
                "type": "token",
                "token_url": "/auth/token",
                "username": "test_user",
                "password": "test_pass",
                "token_expiry": 3600
            }
        },
        "internal_side": {
            "base_url": "http://internal-api.example.com",
            "auth": {
                "type": "basic",
                "username": "internal_user",
                "password": "internal_pass"
            }
        }
    },
    "models": {
        "test_model": {
            "model_id": "test_model",
            "model_name": "测试模型",
            "description": "测试模型配置",
            "sync_interval": 1800,
            "batch_size": 100,
            "enabled": True,
            "sync_enabled": True,
            "cloud_side": {
                "primary_model": "test_model",
                "primary_endpoint": "get_test_items",
                "primary_key": "id",
                "api_config": {
                    "result_path": "objList",
                    "params": {
                        "pageSize": 100,
                        "pageNo": 1,
                        "condition": {
                            "constraint": []
                        }
                    }
                },
                "related_models": [
                    {
                        "model": "related_model",
                        "endpoint": "get_related_items",
                        "join_key": "relatedId",
                        "foreign_key": "id",
                        "relation_type": "one_to_many",
                        "relation_method": "direct",
                        "result_path": "objList"
                    },
                    {
                        "model": "relation_table_model",
                        "relation_type": "one_to_many",
                        "relation_method": "relation_table",
                        "relation_config": {
                            "endpoint": "get_relations",
                            "params": {
                                "condition": {
                                    "constraint": [
                                        {
                                            "simple": {
                                                "name": "source_id",
                                                "value": "{id}",
                                                "operator": "equal"
                                            }
                                        }
                                    ]
                                }
                            },
                            "source_key": "source_id",
                            "target_key": "target_id",
                            "source_field": "id",
                            "result_path": "objList"
                        },
                        "target_config": {
                            "endpoint": "get_targets",
                            "params": {
                                "condition": {
                                    "constraint": [
                                        {
                                            "simple": {
                                                "name": "id",
                                                "value": "{target_ids}",
                                                "operator": "in"
                                            }
                                        }
                                    ]
                                }
                            },
                            "id_key": "id",
                            "result_path": "objList"
                        }
                    }
                ]
            },
            "internal_side": {
                "model": "InternalModel",
                "primary_key": "id",
                "get_endpoint": "get_ci",
                "update_endpoint": "update_ci",
                "get_params": {
                    "className": "InternalModel",
                    "itemNames": ["field1", "field2"],
                    "attribute": [],
                    "isRelatedCIId": True
                },
                "update_params": {
                    "className": "InternalModel"
                },
                "result_path": "data"
            },
            "field_mappings": [
                {
                    "internal_field": "field1",
                    "cloud_field": "field1",
                    "sync_direction": "cloud_to_internal",
                    "required": True
                },
                {
                    "internal_field": "field2",
                    "cloud_field": "field2",
                    "sync_direction": "internal_to_cloud",
                    "required": True
                },
                {
                    "internal_field": "status",
                    "cloud_field": "status",
                    "sync_direction": "cloud_to_internal",
                    "transform": "test_mapping",
                    "transform_params": {
                        "field": "status"
                    },
                    "required": True
                },
                {
                    "internal_field": "calculated_value",
                    "cloud_field": "custom",
                    "sync_direction": "cloud_to_internal",
                    "transform": "test_transform",
                    "required": True
                }
            ]
        }
    },
    "transformers": {
        "test_transform": {
            "type": "python",
            "code": "def transform_func(item):\n    return item.get('value', 0) * 2"
        },
        "test_mapping": {
            "type": "mapping",
            "mapping": {
                "1": "Active",
                "2": "Inactive",
                "default": "Unknown"
            }
        }
    },
    "endpoints": {
        "cloud_side": {
            "get_test_items": "/api/test-items",
            "get_related_items": "/api/related-items",
            "get_relations": "/api/relations",
            "get_targets": "/api/targets",
            "update_item": "/api/update-item"
        },
        "internal_side": {
            "get_ci": "/api/get-ci",
            "update_ci": "/api/update-ci",
            "test_endpoint": "/api/test"
        }
    }
}


@pytest.fixture
def sync_manager():
    """创建SyncManager实例"""
    with patch('os.makedirs'):
        # 创建配置的副本，以便修改
        config = copy.deepcopy(TEST_CONFIG)
        
        # 添加base配置，包含缓存目录设置
        config['base'] = {
            'global': {
                'cache_dir': '/tmp/cache'
            },
            'apis': {
                'cloud_side': config['api']['cloud_side'],
                'internal_side': config['api']['internal_side']
            }
        }
        
        # 直接使用配置字典初始化SyncManager
        manager = SyncManager(config=config)
        
        # 确保缓存目录设置正确
        manager.cache_dir = '/tmp/cache'
        
        # 设置endpoints
        manager.endpoints = config['endpoints']
        
        # 模拟API客户端
        manager.api_clients = {
            'cloud_side': {
                'config': config['api']['cloud_side'],
                'session': MagicMock(),
                'token': None,
                'token_expires': 0
            },
            'internal_side': {
                'config': config['api']['internal_side'],
                'session': MagicMock(),
                'token': None,
                'token_expires': 0
            }
        }
        return manager


class TestSyncManager:
    """SyncManager测试类"""

    def test_init(self, sync_manager):
        """测试初始化"""
        # 修改断言，不再检查config_file属性
        assert sync_manager.config is not None
        assert 'test_model' in sync_manager.models
        assert 'test_transform' in sync_manager.transformers

    def test_replace_placeholders(self, sync_manager):
        """测试占位符替换"""
        # 测试字符串替换
        item = {'id': 123, 'name': 'test'}
        result = sync_manager._replace_placeholders("ID: {id}, Name: {name}", item)
        assert result == "ID: 123, Name: test"

        # 测试字典替换
        obj = {
            'query': 'id={id}',
            'filters': {
                'name': '{name}'
            }
        }
        result = sync_manager._replace_placeholders(obj, item)
        assert result['query'] == 'id=123'
        assert result['filters']['name'] == 'test'

        # 测试列表替换
        obj = ['id={id}', {'name': '{name}'}]
        result = sync_manager._replace_placeholders(obj, item)
        assert result[0] == 'id=123'
        assert result[1]['name'] == 'test'

    def test_extract_by_path(self, sync_manager):
        """测试路径提取"""
        # 测试嵌套字典
        data = {
            'data': {
                'items': [
                    {'id': 1, 'name': 'item1'},
                    {'id': 2, 'name': 'item2'}
                ]
            }
        }

        # 测试点号路径
        result = sync_manager._extract_by_path(data, 'data.items.1.name')
        assert result == 'item2'

        # 测试数组索引路径
        result = sync_manager._extract_by_path(data, 'data.items[0].id')
        assert result == 1

        # 测试无效路径
        result = sync_manager._extract_by_path(data, 'data.invalid.path')
        assert result is None

        # 测试空路径
        result = sync_manager._extract_by_path(data, '')
        assert result == data

    def test_apply_transform(self, sync_manager):
        """测试转换函数应用"""
        # 测试Python转换
        item = {'value': 10}
        result = sync_manager._apply_transform('test_transform', item, {})
        assert result == 20

        # 测试映射转换
        item = {'status': '1'}
        transform_params = {'field': 'status'}
        result = sync_manager._apply_transform('test_mapping', item, {}, transform_params)
        assert result == 'Active'

        # 测试默认映射
        item = {'status': '3'}
        result = sync_manager._apply_transform('test_mapping', item, {}, transform_params)
        assert result == 'Unknown'

        # 测试无效转换
        result = sync_manager._apply_transform('invalid_transform', item, {})
        assert result is None

    @patch('requests.post')
    def test_authenticate_token(self, mock_post, sync_manager):
        """测试Token认证"""
        # 模拟成功响应
        mock_response = MagicMock()
        mock_response.json.return_value = {'token': 'test_token'}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response

        # 测试认证
        result = sync_manager._authenticate_token('cloud_side')
        assert result is True
        assert sync_manager.api_clients['cloud_side']['token'] == 'test_token'
        assert sync_manager.api_clients['cloud_side']['token_expires'] > time.time()

        # 验证请求
        mock_post.assert_called_once()
        args, kwargs = mock_post.call_args
        assert kwargs['json']['username'] == 'test_user'
        assert kwargs['json']['password'] == 'test_pass'

    def test_authenticate_basic(self, sync_manager):
        """测试Basic认证"""
        result = sync_manager._authenticate_basic('internal_side')
        assert result is True
        assert sync_manager.api_clients['internal_side']['token'] == 'basic_auth'
        assert sync_manager.api_clients['internal_side']['token_expires'] == float('inf')

        # 验证认证设置
        session = sync_manager.api_clients['internal_side']['session']
        assert session.auth == ('internal_user', 'internal_pass')

    @patch('os.path.exists')
    @patch('builtins.open', new_callable=mock_open)
    @patch('json.dump')
    def test_save_to_cache(self, mock_dump, mock_open, mock_exists, sync_manager):
        """测试缓存保存"""
        # 使用os.path.join确保路径分隔符正确
        cache_dir = '/tmp/cache'
        sync_manager.cache_dir = cache_dir

        mock_exists.return_value = True

        # 测试保存
        data = {'key': 'value'}
        result = sync_manager._save_to_cache('test_key', data, 3600)
        assert result is True

        # 验证写入
        expected_path = os.path.join(cache_dir, 'test_key.json')
        mock_open.assert_called_once_with(expected_path, 'w', encoding='utf-8')
        mock_dump.assert_called_once()
        args, kwargs = mock_dump.call_args
        assert 'data' in args[0]
        assert args[0]['data'] == data
        assert 'timestamp' in args[0]
        assert 'expire_time' in args[0]
        assert args[0]['expire_time'] == 3600

    @patch('os.path.exists')
    @patch('builtins.open', new_callable=mock_open)
    @patch('json.load')
    def test_load_from_cache(self, mock_load, mock_open, mock_exists, sync_manager):
        """测试缓存加载"""
        # 使用os.path.join确保路径分隔符正确
        cache_dir = '/tmp/cache'
        sync_manager.cache_dir = cache_dir
        
        # 测试缓存存在且未过期
        mock_exists.return_value = True
        mock_load.return_value = {
            'data': {'key': 'value'},
            'timestamp': time.time() - 1800,  # 30分钟前
            'expire_time': 3600  # 1小时过期
        }

        result = sync_manager._load_from_cache('test_key')
        assert result == {'key': 'value'}

        # 测试缓存不存在
        mock_exists.return_value = False
        result = sync_manager._load_from_cache('nonexistent_key')
        assert result is None

        # 测试缓存已过期
        mock_exists.return_value = True
        mock_load.return_value = {
            'data': {'key': 'value'},
            'timestamp': time.time() - 7200,  # 2小时前
            'expire_time': 3600  # 1小时过期
        }

        with patch('os.remove') as mock_remove:
            result = sync_manager._load_from_cache('expired_key')
            assert result is None
            # 使用os.path.join确保路径分隔符正确
            expected_path = os.path.join(cache_dir, 'expired_key.json')
            mock_remove.assert_called_once_with(expected_path)

    @patch('src.sync_manager.SyncManager._call_cloud_api')
    def test_fetch_cloud_data(self, mock_call_api, sync_manager):
        """测试获取云内侧数据"""
        # 模拟API响应
        mock_call_api.return_value = {
            'objList': [
                {'id': 1, 'name': 'item1'},
                {'id': 2, 'name': 'item2'}
            ]
        }

        # 测试获取数据
        model_config = TEST_CONFIG['models']['test_model']
        result = sync_manager._fetch_cloud_data('get_test_items', model_config)
        assert len(result) == 2
        assert result[0]['id'] == 1
        assert result[1]['name'] == 'item2'

        # 验证API调用 - 更新期望的参数，包含分页参数
        mock_call_api.assert_called_once_with('get_test_items', {'pageSize': 100, 'pageNo': 1})

    @patch('src.sync_manager.SyncManager._call_cloud_api')
    def test_fetch_relation_table_data(self, mock_call_api, sync_manager):
        """测试通过关系表获取关联模型数据"""
        # 模拟关系表API响应
        mock_call_api.side_effect = [
            # 关系表响应
            {
                'objList': [
                    {'source_id': 1, 'target_id': 101},
                    {'source_id': 1, 'target_id': 102}
                ]
            },
            # 目标实体响应
            {
                'objList': [
                    {'id': 101, 'name': 'target1'},
                    {'id': 102, 'name': 'target2'}
                ]
            }
        ]

        # 测试数据
        model_id = 'test_model'
        related_model = TEST_CONFIG['models']['test_model']['cloud_side']['related_models'][1]
        primary_data = [{'id': 1, 'name': 'item1'}]

        # 执行测试
        result = sync_manager._fetch_relation_table_data(model_id, related_model, primary_data)

        # 验证结果
        assert len(result) == 2
        assert result[0]['id'] == 101
        assert result[1]['name'] == 'target2'

        # 验证API调用
        assert mock_call_api.call_count == 2
        # 验证第一次调用（关系表）
        args1, kwargs1 = mock_call_api.call_args_list[0]
        assert args1[0] == 'get_relations'
        # 验证第二次调用（目标实体）
        args2, kwargs2 = mock_call_api.call_args_list[1]
        assert args2[0] == 'get_targets'

    @patch('src.sync_manager.SyncManager._ensure_authentication')
    def test_call_cloud_api(self, mock_auth, sync_manager):
        """测试调用云内侧API"""
        # 模拟认证
        mock_auth.return_value = True
        
        # 获取endpoints配置
        sync_manager.endpoints = {
            'cloud_side': {
                'test_endpoint': '/api/test'
            }
        }
        
        # 设置API客户端配置
        sync_manager.api_clients['cloud_side']['config'] = {
            'base_url': 'http://example.com'
        }
        
        # 直接模拟session对象
        mock_session = MagicMock()
        mock_response = MagicMock()
        mock_response.json.return_value = {'result': 'success'}
        mock_response.raise_for_status.return_value = None
        mock_session.get.return_value = mock_response
        
        # 替换API客户端的session
        sync_manager.api_clients['cloud_side']['session'] = mock_session

        # 测试API调用
        result = sync_manager._call_cloud_api('test_endpoint', {'param': 'value'})
        assert result == {'result': 'success'}

        # 验证认证和请求
        mock_auth.assert_called_once_with('cloud_side')
        mock_session.get.assert_called_once()

    @patch('src.sync_manager.SyncManager._ensure_authentication')
    def test_call_internal_api(self, mock_auth, sync_manager):
        """测试调用行内侧API"""
        # 模拟认证
        mock_auth.return_value = True

        # 直接模拟session对象
        mock_session = MagicMock()
        mock_response = MagicMock()
        mock_response.json.return_value = {'result': 'success'}
        mock_session.get.return_value = mock_response

        # 替换API客户端的session
        sync_manager.api_clients['internal_side']['session'] = mock_session

        # 测试API调用
        result = sync_manager._call_internal_api('test_endpoint', {'param': 'value'})
        assert result == {'result': 'success'}

        # 验证认证和请求
        mock_auth.assert_called_once_with('internal_side')
        mock_session.get.assert_called_once()

    @patch('src.sync_manager.SyncManager._call_internal_api')
    def test_update_internal_item(self, mock_call_api, sync_manager):
        """测试更新行内侧项目"""
        # 模拟API响应
        mock_call_api.return_value = {'result': 'success'}

        # 测试更新
        endpoint = 'update_ci'
        internal_side_config = TEST_CONFIG['models']['test_model']['internal_side']
        item = {'id': 1, 'field1': 'value1'}

        result = sync_manager._update_internal_item(endpoint, internal_side_config, item)
        assert result is True  # 修改断言，期望返回布尔值True

        # 验证API调用
        mock_call_api.assert_called_once()
        args, kwargs = mock_call_api.call_args
        assert args[0] == 'update_ci'

        # 检查第二个参数（params）是否包含预期的数据
        assert isinstance(args[1], dict)
        assert 'className' in args[1]
        assert args[1]['className'] == 'InternalModel'
        assert 'data' in args[1]
        assert args[1]['data'] == item

    @patch('src.sync_manager.SyncManager._call_cloud_api')
    def test_update_cloud_item(self, mock_call_api, sync_manager):
        """测试更新云内侧项目"""
        # 模拟API响应
        mock_call_api.return_value = {'result': 'success'}

        # 测试更新
        endpoint = 'update_item'
        item = {'id': 1, 'field1': 'value1'}

        result = sync_manager._update_cloud_item(endpoint, item)
        assert result is True

        # 验证API调用
        mock_call_api.assert_called_once_with('update_item', {}, method='POST', data=item)