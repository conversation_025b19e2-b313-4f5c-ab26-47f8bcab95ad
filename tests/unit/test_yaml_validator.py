import os
import yaml
import json
import pytest
from pathlib import Path

def validate_yaml_file(file_path):
    """验证YAML文件结构是否正确
    
    Args:
        file_path: YAML文件路径
        
    Returns:
        tuple: (是否有效, 错误信息列表)
    """
    errors = []
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查文件是否为空
        if not content.strip():
            errors.append(f"文件为空: {file_path}")
            return False, errors
        
        # 尝试解析YAML
        try:
            yaml_data = yaml.safe_load(content)
        except yaml.YAMLError as e:
            if hasattr(e, 'problem_mark'):
                mark = e.problem_mark
                errors.append(f"YAML语法错误: 第{mark.line+1}行, 第{mark.column+1}列: {e.problem}")
            else:
                errors.append(f"YAML语法错误: {str(e)}")
            return False, errors
        
        # 检查是否为None（空文件或只有注释）
        if yaml_data is None:
            errors.append(f"文件内容为空或只包含注释: {file_path}")
            return False, errors
        
        # 检查是否为字典（有效的YAML配置应该是字典）
        if not isinstance(yaml_data, dict):
            errors.append(f"YAML内容不是有效的配置格式（应为字典）: {file_path}")
            return False, errors
        
        # 检查重复内容
        check_duplicate_content(content, file_path, errors)
        
        # 检查JSON兼容性
        try:
            json.dumps(yaml_data)
        except TypeError as e:
            errors.append(f"YAML内容无法转换为JSON: {str(e)}")
            return False, errors
        
        # 检查基本结构（针对模型配置文件）
        if "models" in file_path:
            check_model_config(yaml_data, file_path, errors)
        
        # 检查模板JSON格式
        check_template_json(yaml_data, file_path, errors)

        return len(errors) == 0, errors
    
    except Exception as e:
        errors.append(f"验证过程发生错误: {str(e)}")
        return False, errors

def check_duplicate_content(content, file_path, errors):
    """检查文件是否包含重复内容"""
    lines = content.strip().split('\n')
    
    # 检查是否有完全相同的两半部分
    half_length = len(lines) // 2
    if half_length > 10:  # 只检查较长的文件
        first_half = '\n'.join(lines[:half_length])
        second_half = '\n'.join(lines[half_length:])
        
        # 如果两半内容相似度高，可能是重复内容
        similarity = calculate_similarity(first_half, second_half)
        if similarity > 0.8:  # 80%相似度阈值
            errors.append(f"文件可能包含重复内容: {file_path}")

def calculate_similarity(text1, text2):
    """计算两段文本的相似度"""
    # 简单实现：计算相同行的比例
    lines1 = set(text1.split('\n'))
    lines2 = set(text2.split('\n'))
    common_lines = lines1.intersection(lines2)
    
    if not lines1 or not lines2:
        return 0
    
    return len(common_lines) / max(len(lines1), len(lines2))

def check_model_config(config, file_path, errors):
    """检查模型配置文件的基本结构"""
    # 检查必要的顶级字段
    required_fields = ['model_id', 'model_name']
    for field in required_fields:
        if field not in config:
            errors.append(f"缺少必要的顶级字段 '{field}': {file_path}")
    
    # 检查field_mappings结构
    if 'field_mappings' in config:
        field_mappings = config['field_mappings']
        if not isinstance(field_mappings, list):
            errors.append(f"field_mappings 不是列表格式: {file_path}")
            return

        for i, mapping in enumerate(field_mappings):
            if not isinstance(mapping, dict):
                errors.append(f"字段映射 #{i+1} 不是字典格式: {file_path}")
                continue
                
            # 检查必要的映射字段
            required_mapping_fields = ['internal_field', 'cloud_field', 'sync_direction']
            for field in required_mapping_fields:
                if field not in mapping:
                    errors.append(f"字段映射 #{i+1} 缺少必要的字段 '{field}': {file_path}")
            
            # 检查transform字段是否有未完成的占位符
            if 'transform' in mapping and isinstance(mapping['transform'], str):
                transform = mapping['transform']
                if '[START SELECTED REGION]' in transform or '[END SELECTED REGION]' in transform:
                    errors.append(f"字段映射 #{i+1} 的transform字段包含未完成的占位符: {file_path}")

def check_template_json(config, file_path, errors):
    """检查配置中的模板JSON格式"""
    # 检查check_templates部分
    if 'check_templates' in config:
        check_templates = config['check_templates']
        if not isinstance(check_templates, list):
            errors.append(f"check_templates 不是列表格式: {file_path}")
            return

        for i, template in enumerate(check_templates):
            if not isinstance(template, dict):
                errors.append(f"检查模板 #{i+1} 不是字典格式: {file_path}")
                continue

            # 检查params字段
            if 'params' in template and isinstance(template['params'], dict):
                # 检查condition字段，这通常是JSON格式的字符串
                for param_key, param_value in template['params'].items():
                    if param_key == 'condition' and isinstance(param_value, str):
                        try:
                            json.loads(param_value)
                        except json.JSONDecodeError as e:
                            errors.append(f"检查模板 #{i+1} 的condition参数JSON格式错误: 第{e.lineno}行, 第{e.colno}列: {e.msg}: {file_path}")

    # 检查cloud_side.related_models部分的params
    if 'cloud_side' in config and 'related_models' in config['cloud_side']:
        related_models = config['cloud_side']['related_models']
        if not isinstance(related_models, list):
            errors.append(f"cloud_side.related_models 不是列表格式: {file_path}")
            return

        for i, model in enumerate(related_models):
            if not isinstance(model, dict):
                errors.append(f"关联模型 #{i+1} 不是字典格式: {file_path}")
                continue

            # 检查relation_config.params
            if 'relation_config' in model and isinstance(model['relation_config'], dict) and 'params' in model['relation_config']:
                params = model['relation_config']['params']
                if isinstance(params, dict) and 'condition' in params:
                    condition = params['condition']
                    # 尝试将condition转换为JSON字符串并解析
                    try:
                        condition_str = json.dumps(condition)
                        json.loads(condition_str)
                    except (TypeError, json.JSONDecodeError) as e:
                        errors.append(f"关联模型 #{i+1} 的condition参数格式错误: {str(e)}: {file_path}")

            # 检查target_config.params
            if 'target_config' in model and isinstance(model['target_config'], dict) and 'params' in model['target_config']:
                params = model['target_config']['params']
                if isinstance(params, dict) and 'condition' in params:
                    condition = params['condition']
                    # 尝试将condition转换为JSON字符串并解析
                    try:
                        condition_str = json.dumps(condition)
                        json.loads(condition_str)
                    except (TypeError, json.JSONDecodeError) as e:
                        errors.append(f"目标配置 #{i+1} 的condition参数格式错误: {str(e)}: {file_path}")

def test_validate_all_yaml_files():
    """测试验证所有YAML配置文件"""
    # 获取项目根目录
    root_dir = Path(__file__).parent.parent.parent
    
    # 配置文件目录
    config_dir = root_dir / 'config'
    
    # 收集所有YAML文件
    yaml_files = []
    for root, _, files in os.walk(config_dir):
        for file in files:
            if file.endswith(('.yaml', '.yml')):
                yaml_files.append(os.path.join(root, file))
    
    # 验证每个文件
    invalid_files = []
    for file_path in yaml_files:
        is_valid, errors = validate_yaml_file(file_path)
        if not is_valid:
            invalid_files.append((file_path, errors))
    
    # 生成详细报告
    if invalid_files:
        report = ["以下YAML文件存在问题:"]
        for file_path, errors in invalid_files:
            report.append(f"\n文件: {file_path}")
            for error in errors:
                report.append(f"  - {error}")
        
        # 打印报告并使测试失败
        print("\n".join(report))
        assert False, f"发现 {len(invalid_files)} 个无效的YAML文件。详情请查看上方报告。"

def test_specific_files():
    """测试特定的YAML文件（根据错误日志）"""
    # 获取项目根目录
    root_dir = Path(__file__).parent.parent.parent

    # 特定文件路径
    problem_files = [
        root_dir / 'config' / 'models' / 'sys_storagepool.yaml',
        root_dir / 'config' / 'models' / 'sys_stordevice.yaml',
        root_dir / 'config' / 'models' / 'cloud_azone.yaml',
        root_dir / 'config' / 'models' / 'network_access_switch.yaml',
        root_dir / 'config' / 'models' / 'network_backbone_router.yaml'
    ]

    # 验证每个文件
    invalid_files = []
    for file_path in problem_files:
        if file_path.exists():
            is_valid, errors = validate_yaml_file(str(file_path))
            if not is_valid:
                invalid_files.append((str(file_path), errors))
        else:
            print(f"文件不存在: {file_path}")

    # 生成详细报告
    if invalid_files:
        report = ["以下YAML文件存在问题:"]
        for file_path, errors in invalid_files:
            report.append(f"\n文件: {file_path}")
            for error in errors:
                report.append(f"  - {error}")

        # 打印报告并使测试失败
        print("\n".join(report))
        assert False, f"发现 {len(invalid_files)} 个无效的YAML文件。详情请查看上方报告。"

def check_file_structure(file_path):
    """检查单个文件的结构并打印详细信息"""
    print(f"\n检查文件: {file_path}")

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 打印文件内容的前几行
        print("文件内容预览:")
        lines = content.split('\n')
        for i, line in enumerate(lines[:10]):
            print(f"{i+1}: {line}")

        # 尝试解析YAML
        yaml_data = yaml.safe_load(content)

        # 检查顶级键
        if yaml_data is None:
            print("警告: 文件内容为空或只包含注释")
            return

        if not isinstance(yaml_data, dict):
            print(f"错误: YAML内容不是字典格式，而是 {type(yaml_data)}")
            return

        print("\n顶级键:")
        for key in yaml_data.keys():
            print(f"- {key}: {type(yaml_data[key])}")

        # 检查特定结构
        if 'field_mappings' in yaml_data:
            print("\nfield_mappings 类型:", type(yaml_data['field_mappings']))
            if isinstance(yaml_data['field_mappings'], list):
                print(f"field_mappings 长度: {len(yaml_data['field_mappings'])}")
                if yaml_data['field_mappings']:
                    print(f"第一个映射类型: {type(yaml_data['field_mappings'][0])}")

        if 'cloud_side' in yaml_data and 'related_models' in yaml_data['cloud_side']:
            print("\ncloud_side.related_models 类型:", type(yaml_data['cloud_side']['related_models']))
            if isinstance(yaml_data['cloud_side']['related_models'], list):
                print(f"related_models 长度: {len(yaml_data['cloud_side']['related_models'])}")

    except Exception as e:
        print(f"检查过程发生错误: {str(e)}")

if __name__ == "__main__":
    # 直接运行此脚本时，执行验证并打印结果
    import sys

    if len(sys.argv) > 1:
        # 如果提供了文件路径参数，则检查指定文件
        file_path = sys.argv[1]
        if os.path.exists(file_path):
            check_file_structure(file_path)
            is_valid, errors = validate_yaml_file(file_path)
            if is_valid:
                print("\n✅ 文件验证通过！")
            else:
                print("\n❌ 文件验证失败:")
                for error in errors:
                    print(f"  - {error}")
        else:
            print(f"文件不存在: {file_path}")
    else:
        # 否则运行所有测试
        print("测试特定文件...")
        try:
            test_specific_files()
            print("特定文件测试通过！")
        except AssertionError as e:
            print(str(e))

        print("\n测试所有YAML文件...")
        try:
            test_validate_all_yaml_files()
            print("所有文件测试通过！")
        except AssertionError as e:
            print(str(e))