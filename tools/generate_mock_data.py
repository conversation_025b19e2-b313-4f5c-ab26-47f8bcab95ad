#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import json
import argparse
import logging
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.config_loader import ConfigLoader

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_mock_data(api_name: str, endpoint: str, params: Optional[Dict[str, Any]] = None, 
                      output_dir: str = './tests/mock_data', config_path: str = './config/base_config.yaml') -> bool:
    """从真实API响应生成模拟数据
    
    Args:
        api_name: API名称，如'cloud_side'或'internal_side'
        endpoint: 端点名称，如'get_vms'
        params: 请求参数
        output_dir: 输出目录
        config_path: 配置文件路径
        
    Returns:
        是否成功生成模拟数据
    """
    try:
        # 加载配置
        config_loader = ConfigLoader(config_path)
        config = config_loader.load_config()
        
        # 检查API是否存在
        if api_name not in config['base']['apis']:
            logger.error(f"API '{api_name}' not found in configuration")
            return False
        
        # 检查端点是否存在
        if endpoint not in config['endpoints'][api_name]:
            logger.error(f"Endpoint '{endpoint}' not found for API '{api_name}'")
            return False
        
        # 创建输出目录
        os.makedirs(os.path.join(output_dir, api_name), exist_ok=True)
        
        # 获取端点URL
        endpoint_url = config['endpoints'][api_name][endpoint]
        
        # 这里应该实现真实API调用逻辑
        # 由于我们没有实际的API客户端，这里生成一些示例数据
        
        # 示例数据
        mock_data = {}
        
        if api_name == 'cloud_side':
            if endpoint == 'get_vms':
                mock_data = {
                    'data': [
                        {
                            'id': 'vm1',
                            'name': 'VM 1',
                            'status': 'ACTIVE',
                            'flavorId': 'flavor1',
                            'hostId': 'host1',
                            'ipAddress': '***********',
                            'floatingIp': '********',
                            'osType': 'Linux',
                            'createdAt': '2023-04-01T12:00:00Z',
                            'remark': 'Test VM 1'
                        },
                        {
                            'id': 'vm2',
                            'name': 'VM 2',
                            'status': 'SHUTOFF',
                            'flavorId': 'flavor2',
                            'hostId': 'host2',
                            'ipAddress': '***********',
                            'floatingIp': '********',
                            'osType': 'Windows',
                            'createdAt': '2023-04-02T12:00:00Z',
                            'remark': 'Test VM 2'
                        }
                    ]
                }
            elif endpoint == 'get_volumes':
                mock_data = {
                    'data': [
                        {
                            'id': 'vol1',
                            'name': 'Volume 1',
                            'size': 100,
                            'status': 'ACTIVE',
                            'vmId': 'vm1'
                        },
                        {
                            'id': 'vol2',
                            'name': 'Volume 2',
                            'size': 200,
                            'status': 'ACTIVE',
                            'vmId': 'vm1'
                        },
                        {
                            'id': 'vol3',
                            'name': 'Volume 3',
                            'size': 300,
                            'status': 'ACTIVE',
                            'vmId': 'vm2'
                        }
                    ]
                }
            elif endpoint == 'get_flavors':
                mock_data = {
                    'data': [
                        {
                            'id': 'flavor1',
                            'name': 'Flavor 1',
                            'vcpuSize': 2,
                            'ramSize': 4,
                            'diskSize': 50
                        },
                        {
                            'id': 'flavor2',
                            'name': 'Flavor 2',
                            'vcpuSize': 4,
                            'ramSize': 8,
                            'diskSize': 100
                        }
                    ]
                }
            elif endpoint == 'get_hosts':
                mock_data = {
                    'data': [
                        {
                            'id': 'host1',
                            'name': 'Host 1',
                            'status': 'ACTIVE'
                        },
                        {
                            'id': 'host2',
                            'name': 'Host 2',
                            'status': 'ACTIVE'
                        }
                    ]
                }
        elif api_name == 'internal_side':
            if endpoint == 'get_ci':
                mock_data = {
                    'data': [
                        {
                            'Code': 'PCVirtualHost_***********',
                            'Description': 'Test VM 1',
                            'Notes': 'Test VM 1',
                            'CreatedTime': '2023/04/01',
                            'HostRunType': '00',
                            'PrdIPAddr': '********',
                            'OS': 'Linux',
                            'DiskCapacity': 150,
                            'CPUCoreNumber': 2,
                            'Memory': 4,
                            'HostedServer': 'Host 1',
                            'IPInfo': '私网IP: ***********, 公网IP: ********',
                            'ManageIPAddr': '***********',
                            'PrivateIPAddr': '***********'
                        },
                        {
                            'Code': 'PCVirtualHost_***********',
                            'Description': 'Test VM 2',
                            'Notes': 'Test VM 2',
                            'CreatedTime': '2023/04/02',
                            'HostRunType': '01',
                            'PrdIPAddr': '********',
                            'OS': 'Windows',
                            'DiskCapacity': 400,
                            'CPUCoreNumber': 4,
                            'Memory': 8,
                            'HostedServer': 'Host 2',
                            'IPInfo': '私网IP: ***********, 公网IP: ********',
                            'ManageIPAddr': '***********',
                            'PrivateIPAddr': '***********'
                        }
                    ]
                }
        
        # 保存模拟数据
        output_file = os.path.join(output_dir, api_name, f"{endpoint_url.replace('/', '_')}.json")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(mock_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Generated mock data: {output_file}")
        return True
    except Exception as e:
        logger.error(f"Failed to generate mock data: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='生成CMDB同步工具模拟数据')
    parser.add_argument('--api', required=True, choices=['cloud_side', 'internal_side'], help='API名称')
    parser.add_argument('--endpoint', required=True, help='端点名称')
    parser.add_argument('--params', help='请求参数（JSON格式）')
    parser.add_argument('--output-dir', default='./tests/mock_data', help='输出目录')
    parser.add_argument('--config', default='./config/base_config.yaml', help='配置文件路径')
    args = parser.parse_args()
    
    # 解析请求参数
    params = None
    if args.params:
        try:
            params = json.loads(args.params)
        except json.JSONDecodeError:
            logger.error("Invalid JSON format for params")
            return 1
    
    # 生成模拟数据
    success = generate_mock_data(args.api, args.endpoint, params, args.output_dir, args.config)
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
