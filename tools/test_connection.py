#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import argparse
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.config_loader import ConfigLoader
from tests.mock_api_client import MockAPIClient

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_connection(api_name: str, config_path: str = './config/base_config.yaml', env: str = 'dev') -> bool:
    """测试API连接
    
    Args:
        api_name: API名称，如'cloud_side'或'internal_side'
        config_path: 配置文件路径
        env: 环境名称
        
    Returns:
        连接是否成功
    """
    try:
        # 设置环境变量
        os.environ['CMDB_SYNC_ENV'] = env
        
        # 加载配置
        config_loader = ConfigLoader(config_path)
        config = config_loader.load_config()
        
        # 检查API是否存在
        if api_name not in config['base']['apis']:
            logger.error(f"API '{api_name}' not found in configuration")
            return False
        
        # 创建API客户端
        client = MockAPIClient(config)
        
        # 测试连接
        logger.info(f"Testing connection to {api_name}...")
        result = client.test_connection(api_name)
        
        if result:
            logger.info(f"Connection to {api_name} successful")
            return True
        else:
            logger.error(f"Connection to {api_name} failed")
            return False
    except Exception as e:
        logger.error(f"Connection test failed: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='测试CMDB同步工具API连接')
    parser.add_argument('--api', required=True, choices=['cloud_side', 'internal_side'], help='API名称')
    parser.add_argument('--config', default='./config/base_config.yaml', help='配置文件路径')
    parser.add_argument('--env', default='dev', choices=['dev', 'test', 'prod'], help='环境')
    parser.add_argument('--mock', action='store_true', help='启用模拟')
    args = parser.parse_args()
    
    # 设置模拟环境变量
    if args.mock:
        os.environ['CMDB_SYNC_MOCK'] = 'true'
    else:
        os.environ.pop('CMDB_SYNC_MOCK', None)
    
    # 测试连接
    success = test_connection(args.api, args.config, args.env)
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
