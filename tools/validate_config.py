#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import argparse
import logging
import yaml
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.config_loader import ConfigLoader

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def validate_base_config(base_config: Dict[str, Any]) -> List[str]:
    """验证基础配置
    
    Args:
        base_config: 基础配置对象
        
    Returns:
        错误列表，如果没有错误则为空列表
    """
    errors = []
    
    # 验证全局配置
    if 'global' not in base_config:
        errors.append("Missing 'global' section in base config")
    else:
        global_config = base_config['global']
        required_global_fields = ['cache_dir', 'log_dir', 'models_dir', 'endpoints_dir', 'transformers_dir']
        for field in required_global_fields:
            if field not in global_config:
                errors.append(f"Missing '{field}' in global config")
    
    # 验证API配置
    if 'apis' not in base_config:
        errors.append("Missing 'apis' section in base config")
    else:
        apis_config = base_config['apis']
        required_apis = ['cloud_side', 'internal_side']
        for api in required_apis:
            if api not in apis_config:
                errors.append(f"Missing '{api}' in apis config")
            else:
                api_config = apis_config[api]
                if 'base_url' not in api_config:
                    errors.append(f"Missing 'base_url' in {api} config")
                if 'auth' not in api_config:
                    errors.append(f"Missing 'auth' in {api} config")
    
    # 验证缓存配置
    if 'cache' not in base_config:
        errors.append("Missing 'cache' section in base config")
    
    # 验证日志配置
    if 'logging' not in base_config:
        errors.append("Missing 'logging' section in base config")
    
    return errors

def validate_model_config(model_id: str, model_config: Dict[str, Any]) -> List[str]:
    """验证模型配置
    
    Args:
        model_id: 模型ID
        model_config: 模型配置对象
        
    Returns:
        错误列表，如果没有错误则为空列表
    """
    errors = []
    
    # 验证基本字段
    required_fields = ['model_id', 'model_name', 'cloud_side', 'internal_side', 'field_mappings']
    for field in required_fields:
        if field not in model_config:
            errors.append(f"Missing '{field}' in model config for {model_id}")
    
    # 验证model_id一致性
    if 'model_id' in model_config and model_config['model_id'] != model_id:
        errors.append(f"Model ID mismatch: {model_config['model_id']} != {model_id}")
    
    # 验证云内侧配置
    if 'cloud_side' in model_config:
        cloud_side = model_config['cloud_side']
        required_cloud_fields = ['primary_model', 'primary_endpoint', 'primary_key']
        for field in required_cloud_fields:
            if field not in cloud_side:
                errors.append(f"Missing '{field}' in cloud_side config for {model_id}")
    
    # 验证行内侧配置
    if 'internal_side' in model_config:
        internal_side = model_config['internal_side']
        required_internal_fields = ['model', 'primary_key', 'get_endpoint', 'update_endpoint']
        for field in required_internal_fields:
            if field not in internal_side:
                errors.append(f"Missing '{field}' in internal_side config for {model_id}")
    
    # 验证字段映射
    if 'field_mappings' in model_config:
        field_mappings = model_config['field_mappings']
        for i, mapping in enumerate(field_mappings):
            required_mapping_fields = ['internal_field', 'cloud_field', 'sync_direction']
            for field in required_mapping_fields:
                if field not in mapping:
                    errors.append(f"Missing '{field}' in field mapping #{i+1} for {model_id}")
            
            # 验证同步方向
            if 'sync_direction' in mapping:
                direction = mapping['sync_direction']
                if direction not in ['cloud_to_internal', 'internal_to_cloud']:
                    errors.append(f"Invalid sync_direction '{direction}' in field mapping #{i+1} for {model_id}")
    
    return errors

def validate_field_mappings(model_id: str, model_config: Dict[str, Any], transformers: Dict[str, Any]) -> List[str]:
    """验证字段映射
    
    Args:
        model_id: 模型ID
        model_config: 模型配置对象
        transformers: 转换函数配置
        
    Returns:
        错误列表，如果没有错误则为空列表
    """
    errors = []
    
    if 'field_mappings' in model_config:
        field_mappings = model_config['field_mappings']
        for i, mapping in enumerate(field_mappings):
            # 验证转换函数
            if 'transform' in mapping and mapping['transform'] is not None:
                transform = mapping['transform']
                # 跳过lambda表达式
                if not transform.startswith('lambda'):
                    # 检查转换函数是否存在
                    if transform not in transformers:
                        errors.append(f"Transformer '{transform}' not found for field mapping #{i+1} in {model_id}")
    
    return errors

def validate_config(config_path: str = './config/base_config.yaml') -> bool:
    """验证配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        配置是否有效
    """
    try:
        # 加载配置
        config_loader = ConfigLoader(config_path)
        config = config_loader.load_config()
        
        errors = []
        
        # 验证基础配置
        base_errors = validate_base_config(config['base'])
        errors.extend(base_errors)
        
        # 验证模型配置
        for model_id, model_config in config['models'].items():
            model_errors = validate_model_config(model_id, model_config)
            errors.extend(model_errors)
            
            # 验证字段映射
            mapping_errors = validate_field_mappings(model_id, model_config, config['transformers'])
            errors.extend(mapping_errors)
        
        if errors:
            logger.error("Configuration validation failed:")
            for error in errors:
                logger.error(f"  - {error}")
            return False
        else:
            logger.info("Configuration validation passed")
            return True
    except Exception as e:
        logger.error(f"Configuration validation failed: {e}")
        return False

def validate_model(model_id: str, config_path: str = './config/base_config.yaml') -> bool:
    """验证特定模型配置
    
    Args:
        model_id: 模型ID
        config_path: 配置文件路径
        
    Returns:
        模型配置是否有效
    """
    try:
        # 加载配置
        config_loader = ConfigLoader(config_path)
        config = config_loader.load_config()
        
        # 检查模型是否存在
        if model_id not in config['models']:
            logger.error(f"Model '{model_id}' not found in configuration")
            return False
        
        model_config = config['models'][model_id]
        errors = []
        
        # 验证模型配置
        model_errors = validate_model_config(model_id, model_config)
        errors.extend(model_errors)
        
        # 验证字段映射
        mapping_errors = validate_field_mappings(model_id, model_config, config['transformers'])
        errors.extend(mapping_errors)
        
        if errors:
            logger.error(f"Model '{model_id}' validation failed:")
            for error in errors:
                logger.error(f"  - {error}")
            return False
        else:
            logger.info(f"Model '{model_id}' validation passed")
            return True
    except Exception as e:
        logger.error(f"Model validation failed: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='验证CMDB同步工具配置')
    parser.add_argument('--config', default='./config/base_config.yaml', help='配置文件路径')
    parser.add_argument('--model', help='指定要验证的模型ID')
    args = parser.parse_args()
    
    if args.model:
        # 验证特定模型
        success = validate_model(args.model, args.config)
    else:
        # 验证所有配置
        success = validate_config(args.config)
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
