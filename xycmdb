#!/bin/bash

port=18081
baseDir='/data/xycmdb/xycmdbsync'
envDir='/data/xycmdb/envs/xycmdb/bin'
timeout=120
# 允许执行的用户（root和optuser）
allowed_users=("root" "optuser")

# 获取当前执行脚本的用户
current_user=$(whoami)

curOperate=$1
cd $baseDir || { echo "无法进入目录 $baseDir"; exit 1; }

# 检查操作参数
if [[ -z $curOperate ]]; then
    echo 'xycmdb <操作: start/stop/restart/status>'
    exit 1
fi

if [[ $curOperate != 'start' && $curOperate != 'stop' && $curOperate != 'restart' && $curOperate != 'status' ]]; then
    echo '操作: start/stop/restart/status'
    exit 1
fi

# 检查当前用户是否在允许列表中
is_allowed=false
for user in "${allowed_users[@]}"; do
    if [[ $current_user == "$user" ]]; then
        is_allowed=true
        break
    fi
done

# 非允许用户执行操作时提示错误
if [[ $is_allowed == false ]]; then
    echo "错误：仅root和optuser可执行此操作（非root用户无法使用runuser）"
    exit 1
fi

function checkPort() {
    netstat -tlpn | grep -w ${port} >/dev/null
}

function startService() {
    if checkPort; then
        echo -e "[xycmdb]: \033[1;32m 已运行. \033[0m"
        return
    fi

    echo -e "[xycmdb]: 启动中..."
    # 构建执行命令
    #command="nohup ${envDir}/python ${baseDir}/src/cmdb_sync_driver.py 1>${baseDir}/logs/cmdb_sync.log 2>&1 &"
    command="nohup ${envDir}/python ${baseDir}/src/cmdb_sync_driver.py 1>/dev/null 2>&1 &"

    # 根据当前用户执行命令（root用runuser切换，optuser直接执行）
    if [[ $current_user == "root" ]]; then
        # root用户：用runuser切换到optuser执行
        /usr/sbin/runuser -l optuser -c "cd $baseDir && $command"
    else
        # optuser用户：直接执行（无需切换）
        eval "$command"
    fi

    # 等待启动完成
    useTime=0
    while true; do
        if checkPort; then
            echo -e "\033[1;32m 启动成功 \033[0m"
            break
        else
            echo -n "."
            sleep 2
        fi

        let useTime+=2
        if [[ $useTime -gt $timeout ]]; then
            echo -e "\n[xycmdb]: \033[1;31m 启动超时失败 \033[0m"
            exit 1
        fi
    done
}

function stopService() {
    if ! checkPort; then
        echo -e "[xycmdb]: \033[1;32m 已停止. \033[0m"
        return
    fi

    # 获取端口对应的进程ID
    pid=$(lsof -i:$port | grep LISTEN | awk '{print $2}')
    if [[ -z $pid ]]; then
        echo -e "[xycmdb]: \033[1;32m 已停止. \033[0m"
        return
    fi

    # 根据当前用户停止进程（root用runuser切换，optuser直接kill）
    if [[ $current_user == "root" ]]; then
        /usr/sbin/runuser -l optuser -c "kill -9 $pid"
    else
        kill -9 $pid
    fi

    echo -e "[xycmdb]: \033[1;32m 已停止. \033[0m"
}

function checkService() {
    if checkPort; then
        echo -e "[xycmdb]: \033[1;32m 运行中. \033[0m"
    else
        echo -e "[xycmdb]: \033[1;31m 已停止. \033[0m"
        exit 22
    fi
}

case $curOperate in
    start)
        startService
        ;;
    stop)
        stopService
        ;;
    restart)
        stopService
        echo ""
        sleep 2
        startService
        ;;
    status)
        checkService
        ;;
esac

exit 0
